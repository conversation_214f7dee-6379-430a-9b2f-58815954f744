<!DOCTYPE html>
<html translate="no" lang="pt">
  <head>
    <meta charset="utf-8" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
        w[l] = w[l] || []; w[l].push({
          'gtm.start':
            new Date().getTime(), event: 'gtm.js'
        }); var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
            'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-WWN6SCZ9');</script>
    <!-- End Google Tag Manager -->

    <!-- disable google translate -->
    <meta name="google" content="notranslate" />

    <!-- PWA primary color -->
    <meta name="theme-color" content="#00AB55" />

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="%REACT_APP_BRANDING_IMAGES_URL%favicon/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="%REACT_APP_BRANDING_IMAGES_URL%favicon/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="%REACT_APP_BRANDING_IMAGES_URL%favicon/favicon-16x16.png" />

    <!-- Using Google Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Barlow:wght@900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
      integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Using Local Font -->
    <link rel="stylesheet" type="text/css" href="%PUBLIC_URL%/fonts/index.css" />
    <title>%REACT_APP_BRANDING%</title>

    <meta
      name="description"
      content="The starting point for your next project with Cakto, built on the newest version of Material-UI ©, ready to be customized to your style"
    />
    <meta name="keywords" content="react,material,kit,application,dashboard,admin,template" />
    <meta name="author" content="Cakto" />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="fb-root"></div>

    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe 
        src="https://www.googletagmanager.com/ns.html?id=GTM-WWN6SCZ9" 
        height="0" 
        width="0" 
        style="display:none;visibility:hidden"
      >
      </iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    <script
      async
      defer
      crossorigin="anonymous"
      src="https://connect.facebook.net/pt_BR/sdk.js#xfbml=1&version=v18.0&appId=627798869089075"
      nonce="JPMR7DC6"
    ></script>
    <script src="https://sdkweb-lib.idwall.co/index.js"></script>
    <div id="root"></div>
    <script>
      const capitalize = (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      const title = document.querySelector('title');
      if (title && title.textContent) {
        title.textContent = capitalize(title.textContent);
      }
    </script>
  </body>
</html>
