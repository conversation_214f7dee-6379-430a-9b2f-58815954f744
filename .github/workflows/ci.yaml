name: CI Pipeline

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - staging

  pull_request:
    branches:
      - main
      - staging

env:
  node_version: 22.x

jobs:
  build:
    runs-on: ubuntu-latest

    steps:

      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Node ${{ env.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.node_version }}

      - name: Install Dependencies
        run: yarn install

      - name: Run Tests with coverage
        run: yarn test --coverage

      - name: Upload Coverage Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: ./coverage

      - name: Build Application
        run: |
          CI=false yarn build
