name: Sync main into staging

on:
  push:
    branches:
      - main

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

      - name: Reset staging to main
        if: github.ref == 'refs/heads/main'
        run: |
          git checkout staging
          git reset --hard main
          git push --force-with-lease origin staging
