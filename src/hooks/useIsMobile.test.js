import { renderHook } from '@testing-library/react';
import { useIsMobile } from './useIsMobile';
import useResponsive from './useResponsive';

// Mock the useResponsive hook
jest.mock('./useResponsive', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('useIsMobile', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    // Reset navigator mock
    Object.defineProperty(window, 'navigator', {
      value: {},
      writable: true,
    });
  });

  it('should return true for mobile user agents', () => {
    // Mock mobile user agent
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(true);
  });

  it('should return true for Android user agents', () => {
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Linux; Android 11; SM-G991B)',
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(true);
  });

  it('should return true for iPad user agents', () => {
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(true);
  });

  it('should return false for desktop user agents', () => {
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(false);
  });

  it('should return true when responsive breakpoint is mobile', () => {
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      writable: true,
    });
    useResponsive.mockReturnValue(true);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(true);
  });

  it('should handle SSR environment', () => {
    // Mock SSR environment by removing navigator
    Object.defineProperty(window, 'navigator', {
      value: undefined,
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(false);
  });

  it('should return true if either user agent or responsive check is true', () => {
    // Test case 1: Mobile user agent + non-mobile responsive
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      writable: true,
    });
    useResponsive.mockReturnValue(false);
    const { result: result1 } = renderHook(() => useIsMobile());
    expect(result1.current.isMobile).toBe(true);
    // Test case 2: Desktop user agent + mobile responsive
    Object.defineProperty(window.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      writable: true,
    });
    useResponsive.mockReturnValue(true);
    const { result: result2 } = renderHook(() => useIsMobile());
    expect(result2.current.isMobile).toBe(true);
  });

  it('should handle various mobile device user agents', () => {
    const mobileUserAgents = [
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      'Mozilla/5.0 (Linux; Android 11; SM-G991B)',
      'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
      'Mozilla/5.0 (BlackBerry; U; BlackBerry 9900; en)',
      'Opera/9.80 (Android 2.3.3; Linux; Opera Mobi/ADR-1111101157; U; en)',
      'Mozilla/5.0 (webOS/1.4.0; U; en-US) AppleWebKit/532.2 (KHTML, like Gecko)',
      'Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0)',
    ];
    mobileUserAgents.forEach((userAgent) => {
      Object.defineProperty(window.navigator, 'userAgent', {
        value: userAgent,
        writable: true,
      });
      useResponsive.mockReturnValue(false);
      const { result } = renderHook(() => useIsMobile());
      expect(result.current.isMobile).toBe(true);
    });
  });
});
