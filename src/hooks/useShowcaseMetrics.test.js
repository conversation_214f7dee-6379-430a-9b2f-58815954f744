import { registerMetricsForShowcaseProducts } from '@/services/products';
import { act, renderHook } from '@testing-library/react';
import { useShowcaseMetrics } from './useShowcaseMetrics';

// Mock the products service
jest.mock('@/services/products', () => ({
  registerMetricsForShowcaseProducts: jest.fn().mockResolvedValue(undefined),
}));

describe('useShowcaseMetrics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should register view metrics for new products', async () => {
    const { result } = renderHook(() => useShowcaseMetrics());
    const productIds = ['1', '2', '3'];

    await act(async () => {
      result.current.registerMetric('view', productIds);
    });

    expect(registerMetricsForShowcaseProducts).toHaveBeenCalledWith('view', productIds);
  });

  it('should register click metrics for new products', async () => {
    const { result } = renderHook(() => useShowcaseMetrics());
    const productIds = ['1', '2', '3'];

    await act(async () => {
      result.current.registerMetric('click', productIds);
    });

    expect(registerMetricsForShowcaseProducts).toHaveBeenCalledWith('click', productIds);
  });

  it('should not register metrics for already viewed products', async () => {
    const { result } = renderHook(() => useShowcaseMetrics());
    const productIds = ['1', '2', '3'];

    // First registration
    await act(async () => {
      result.current.registerMetric('view', productIds);
    });

    // Second registration with same products
    await act(async () => {
      result.current.registerMetric('view', productIds);
    });

    // Should only be called once
    expect(registerMetricsForShowcaseProducts).toHaveBeenCalledTimes(1);
  });

  it('should handle errors when registering metrics', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    registerMetricsForShowcaseProducts.mockRejectedValueOnce(new Error('API Error'));

    const { result } = renderHook(() => useShowcaseMetrics());
    const productIds = ['1', '2', '3'];

    await act(async () => {
      result.current.registerMetric('view', productIds);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Error registering metrics for showcase products');
    consoleSpy.mockRestore();
  });

  it('should not register metrics for invalid type', async () => {
    const { result } = renderHook(() => useShowcaseMetrics());
    const productIds = ['1', '2', '3'];

    await act(async () => {
      result.current.registerMetric('invalid', productIds);
    });

    expect(registerMetricsForShowcaseProducts).not.toHaveBeenCalled();
  });
});
