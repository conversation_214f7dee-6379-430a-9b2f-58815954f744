import { registerMetricsForShowcaseProducts } from '@/services/products';
import { useState } from 'react';

export const useShowcaseMetrics = () => {
  const [, setAlreadyViewedProductIds] = useState([]);

  const [, setAlreadyClickedProductIds] = useState([]);

  /**
   * @param {'view' | 'click'} type
   * @param {string[]} productIds
   */
  const registerMetric = (type, productIds) => {
    const stateSetter = {
      view: setAlreadyViewedProductIds,
      click: setAlreadyClickedProductIds,
    };

    if (type in stateSetter) {
      stateSetter[type]((prev) => {
        const nonRegisteredProductIds = productIds.filter((productId) => !prev.includes(productId));

        if (nonRegisteredProductIds.length > 0) {
          registerMetricsForShowcaseProducts(type, nonRegisteredProductIds).catch(() => {
            console.error('Error registering metrics for showcase products');
          });
        }

        return [...prev, ...nonRegisteredProductIds];
      });
    }
  };

  return {
    registerMetric,
  };
};
