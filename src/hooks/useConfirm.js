const { Dialog, DialogTitle, DialogContent, DialogActions, Button } = require('@mui/material');
const { useState } = require('react');

const useConfirm = ({
  title = 'Confirmação',
  message = 'Você tem certeza que deseja sair sem salvar?',
  confirmLabel = 'Sim',
  cancelLabel = 'Não',
  confirmColor = 'primary',
  cancelColor = 'inherit',
  onConfirm = () => {},
  onCancel = () => {},
}) => {
  const [open, setOpen] = useState(false);

  const Component = () => (
    <Dialog open={open} onClose={() => setOpen(false)}>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>{message}</DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setOpen(false);
            onCancel();
          }}
          color={cancelColor}
          variant="outlined"
        >
          {cancelLabel}
        </Button>
        <Button
          onClick={() => {
            setOpen(false);
            onConfirm();
          }}
          color={confirmColor}
          variant="contained"
        >
          {confirmLabel}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return {
    ConfirmComponent: Component,
    confirm: () => setOpen(true),
  };
};

export default useConfirm;
