import { useAuthContext } from '@/auth/useAuthContext';

export function useExperimentalFeatures() {
  const { user } = useAuthContext();

  const checkIfUserHasAtLeastOnePermission = (features) => {
    const experimentalFeatures = user?.experimental_features || [];

    const featuresArray = Array.isArray(features) ? features : [features];

    return featuresArray.some((feature) => experimentalFeatures.includes(feature));
  };

  const checkIfUserHasAllPermissions = (features) => {
    const experimentalFeatures = user?.experimental_features || [];

    const featuresArray = Array.isArray(features) ? features : [features];

    return featuresArray.every((feature) => experimentalFeatures.includes(feature));
  };

  return { checkIfUserHasAtLeastOnePermission, checkIfUserHasAllPermissions };
}
