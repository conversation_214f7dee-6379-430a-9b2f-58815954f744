import TelegramIcon from '@/components/icons/TelegramIcon';
import DiscordIcon from '@/components/icons/DiscordIcon';

export const CONTENT_DELIVERY_OPTIONS = [
  {
    title: '<PERSON><PERSON> de membros Cakto',
    description: 'Nova Área de Membros Premium da Cakto',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="#212B36" />
        <path
          d="M22.0403 14.0267L16.627 18.08"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M25.8732 10.1895C26.9312 11.2476 26.9312 12.9631 25.8732 14.0211C24.8151 15.0792 23.0996 15.0792 22.0416 14.0211C20.9835 12.9631 20.9835 11.2476 22.0416 10.1895C23.0997 9.13148 24.8151 9.13148 25.8732 10.1895"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22.0403 25.9733L16.627 21.92"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M25.8732 25.9789C26.9312 27.037 26.9312 28.7524 25.8732 29.8105C24.8151 30.8685 23.0996 30.8685 22.0416 29.8105C20.9835 28.7524 20.9835 27.037 22.0416 25.9789C23.0997 24.9208 24.8151 24.9208 25.8732 25.9789"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.6206 18.0862C17.6776 19.1432 17.6776 20.8569 16.6206 21.914C15.5635 22.971 13.8498 22.971 12.7928 21.914C11.7357 20.8569 11.7357 19.1432 12.7928 18.0862C13.8498 17.0291 15.5636 17.0291 16.6206 18.0862"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: 'cakto',
  },
  {
    title: 'Área de membros Externa',
    description: 'Cursos on-line e comunidade externos',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="#212B36" />
        <path
          d="M22.0403 14.0267L16.627 18.08"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M25.8732 10.1895C26.9312 11.2476 26.9312 12.9631 25.8732 14.0211C24.8151 15.0792 23.0996 15.0792 22.0416 14.0211C20.9835 12.9631 20.9835 11.2476 22.0416 10.1895C23.0997 9.13148 24.8151 9.13148 25.8732 10.1895"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22.0403 25.9733L16.627 21.92"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M25.8732 25.9789C26.9312 27.037 26.9312 28.7524 25.8732 29.8105C24.8151 30.8685 23.0996 30.8685 22.0416 29.8105C20.9835 28.7524 20.9835 27.037 22.0416 25.9789C23.0997 24.9208 24.8151 24.9208 25.8732 25.9789"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.6206 18.0862C17.6776 19.1432 17.6776 20.8569 16.6206 21.914C15.5635 22.971 13.8498 22.971 12.7928 21.914C11.7357 20.8569 11.7357 19.1432 12.7928 18.0862C13.8498 17.0291 15.5636 17.0291 16.6206 18.0862"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: 'external',
  },
  {
    title: 'Instagram Close Friends',
    description: 'Adicione automaticamente alguém aos Close Friends',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="url(#paint0_linear_4435_8112)" />
        <path
          d="M20.2 14.2C19.0133 14.2 17.8532 14.5518 16.8665 15.2111C15.8798 15.8704 15.1108 16.8075 14.6567 17.9039C14.2025 19.0002 14.0837 20.2066 14.3152 21.3705C14.5468 22.5344 15.1182 23.6035 15.9573 24.4426C16.7964 25.2817 17.8655 25.8532 19.0294 26.0847C20.1933 26.3162 21.3997 26.1974 22.4961 25.7432C23.5924 25.2891 24.5295 24.5201 25.1888 23.5334C25.8481 22.5467 26.2 21.3866 26.2 20.2C26.1983 18.6092 25.5656 17.084 24.4408 15.9591C23.3159 14.8343 21.7907 14.2016 20.2 14.2ZM20.2 24.2C19.4088 24.2 18.6355 23.9654 17.9777 23.5258C17.3199 23.0863 16.8072 22.4616 16.5044 21.7307C16.2017 20.9998 16.1225 20.1955 16.2768 19.4196C16.4312 18.6437 16.8121 17.9309 17.3715 17.3715C17.9309 16.8121 18.6437 16.4312 19.4196 16.2768C20.1955 16.1225 20.9998 16.2017 21.7307 16.5044C22.4616 16.8072 23.0863 17.3199 23.5258 17.9777C23.9654 18.6355 24.2 19.4088 24.2 20.2C24.2 21.2608 23.7785 22.2782 23.0284 23.0284C22.2782 23.7785 21.2608 24.2 20.2 24.2ZM26.2 7.19995H14.2C12.344 7.20194 10.5647 7.94007 9.2524 9.2524C7.94007 10.5647 7.20194 12.344 7.19995 14.2V26.2C7.20194 28.0559 7.94007 29.8352 9.2524 31.1475C10.5647 32.4598 12.344 33.198 14.2 33.2H26.2C28.0559 33.198 29.8352 32.4598 31.1475 31.1475C32.4598 29.8352 33.198 28.0559 33.2 26.2V14.2C33.198 12.344 32.4598 10.5647 31.1475 9.2524C29.8352 7.94007 28.0559 7.20194 26.2 7.19995ZM31.2 26.2C31.2 27.526 30.6732 28.7978 29.7355 29.7355C28.7978 30.6732 27.526 31.2 26.2 31.2H14.2C12.8739 31.2 11.6021 30.6732 10.6644 29.7355C9.72674 28.7978 9.19995 27.526 9.19995 26.2V14.2C9.19995 12.8739 9.72674 11.6021 10.6644 10.6644C11.6021 9.72674 12.8739 9.19995 14.2 9.19995H26.2C27.526 9.19995 28.7978 9.72674 29.7355 10.6644C30.6732 11.6021 31.2 12.8739 31.2 14.2V26.2ZM28.2 13.7C28.2 13.9966 28.112 14.2866 27.9472 14.5333C27.7823 14.78 27.5481 14.9722 27.274 15.0858C26.9999 15.1993 26.6983 15.229 26.4073 15.1711C26.1163 15.1133 25.8491 14.9704 25.6393 14.7606C25.4295 14.5508 25.2867 14.2836 25.2288 13.9926C25.1709 13.7016 25.2006 13.4 25.3141 13.1259C25.4277 12.8518 25.6199 12.6176 25.8666 12.4527C26.1133 12.2879 26.4033 12.2 26.7 12.2C27.0978 12.2 27.4793 12.358 27.7606 12.6393C28.0419 12.9206 28.2 13.3021 28.2 13.7Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_4435_8112"
            x1="11.5"
            y1="40"
            x2="29.5"
            y2="6.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FFB34E" />
            <stop offset="0.52" stopColor="#F9514A" />
            <stop offset="1" stopColor="#C837AB" />
          </linearGradient>
        </defs>
      </svg>
    ),
    value: 'instagram_cf',
  },
  {
    title: 'Instagram Perfil Privado',
    description: 'Permita o acesso automático a um perfil privado ',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="url(#paint0_linear_4435_8112)" />
        <path
          d="M20.2 14.2C19.0133 14.2 17.8532 14.5518 16.8665 15.2111C15.8798 15.8704 15.1108 16.8075 14.6567 17.9039C14.2025 19.0002 14.0837 20.2066 14.3152 21.3705C14.5468 22.5344 15.1182 23.6035 15.9573 24.4426C16.7964 25.2817 17.8655 25.8532 19.0294 26.0847C20.1933 26.3162 21.3997 26.1974 22.4961 25.7432C23.5924 25.2891 24.5295 24.5201 25.1888 23.5334C25.8481 22.5467 26.2 21.3866 26.2 20.2C26.1983 18.6092 25.5656 17.084 24.4408 15.9591C23.3159 14.8343 21.7907 14.2016 20.2 14.2ZM20.2 24.2C19.4088 24.2 18.6355 23.9654 17.9777 23.5258C17.3199 23.0863 16.8072 22.4616 16.5044 21.7307C16.2017 20.9998 16.1225 20.1955 16.2768 19.4196C16.4312 18.6437 16.8121 17.9309 17.3715 17.3715C17.9309 16.8121 18.6437 16.4312 19.4196 16.2768C20.1955 16.1225 20.9998 16.2017 21.7307 16.5044C22.4616 16.8072 23.0863 17.3199 23.5258 17.9777C23.9654 18.6355 24.2 19.4088 24.2 20.2C24.2 21.2608 23.7785 22.2782 23.0284 23.0284C22.2782 23.7785 21.2608 24.2 20.2 24.2ZM26.2 7.19995H14.2C12.344 7.20194 10.5647 7.94007 9.2524 9.2524C7.94007 10.5647 7.20194 12.344 7.19995 14.2V26.2C7.20194 28.0559 7.94007 29.8352 9.2524 31.1475C10.5647 32.4598 12.344 33.198 14.2 33.2H26.2C28.0559 33.198 29.8352 32.4598 31.1475 31.1475C32.4598 29.8352 33.198 28.0559 33.2 26.2V14.2C33.198 12.344 32.4598 10.5647 31.1475 9.2524C29.8352 7.94007 28.0559 7.20194 26.2 7.19995ZM31.2 26.2C31.2 27.526 30.6732 28.7978 29.7355 29.7355C28.7978 30.6732 27.526 31.2 26.2 31.2H14.2C12.8739 31.2 11.6021 30.6732 10.6644 29.7355C9.72674 28.7978 9.19995 27.526 9.19995 26.2V14.2C9.19995 12.8739 9.72674 11.6021 10.6644 10.6644C11.6021 9.72674 12.8739 9.19995 14.2 9.19995H26.2C27.526 9.19995 28.7978 9.72674 29.7355 10.6644C30.6732 11.6021 31.2 12.8739 31.2 14.2V26.2ZM28.2 13.7C28.2 13.9966 28.112 14.2866 27.9472 14.5333C27.7823 14.78 27.5481 14.9722 27.274 15.0858C26.9999 15.1993 26.6983 15.229 26.4073 15.1711C26.1163 15.1133 25.8491 14.9704 25.6393 14.7606C25.4295 14.5508 25.2867 14.2836 25.2288 13.9926C25.1709 13.7016 25.2006 13.4 25.3141 13.1259C25.4277 12.8518 25.6199 12.6176 25.8666 12.4527C26.1133 12.2879 26.4033 12.2 26.7 12.2C27.0978 12.2 27.4793 12.358 27.7606 12.6393C28.0419 12.9206 28.2 13.3021 28.2 13.7Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_4435_8112"
            x1="11.5"
            y1="40"
            x2="29.5"
            y2="6.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FFB34E" />
            <stop offset="0.52" stopColor="#F9514A" />
            <stop offset="1" stopColor="#C837AB" />
          </linearGradient>
        </defs>
      </svg>
    ),
    value: 'instagram_pp',
  },
  {
    title: 'Link de pagamento',
    description: 'Utilize exclusivamente o link de pagamento para receber os valores',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="#212B36" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.8787 22.9947L11.832 13.3334H28.6667C29.5347 13.3334 30.1707 14.148 29.96 14.9907L28.1627 22.18C27.8894 23.272 26.9614 24.0747 25.8414 24.1867L16.7534 25.096C15.3987 25.2307 14.16 24.3254 13.8787 22.9947Z"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.8321 13.3334L10.9654 9.33337H8.66675"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M26.8117 29.6893C26.5424 29.6893 26.3237 29.908 26.3264 30.1773C26.3264 30.4466 26.5451 30.6653 26.8144 30.6653C27.0837 30.6653 27.3024 30.4466 27.3024 30.1773C27.3011 29.908 27.0824 29.6893 26.8117 29.6893"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15.5963 29.6894C15.327 29.6894 15.1083 29.908 15.111 30.1774C15.1083 30.448 15.3283 30.6667 15.5977 30.6667C15.867 30.6667 16.0857 30.448 16.0857 30.1787C16.0857 29.908 15.867 29.6894 15.5963 29.6894"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: 'disabled',
  },
  {
    title: 'Acesso por e-mail',
    description:
      'Envie um acesso personalizado a um drive, link, etc., para seu cliente diretamente pelo e-mail',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="#212B36" />
        <path
          d="M13.998 16.9987L17.9003 19.4045C19.1879 20.1984 20.8132 20.1984 22.1008 19.4045L26.003 16.9987"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <rect
          x="7.99512"
          y="10.6627"
          width="24.01"
          height="18.6744"
          rx="4"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: 'emailAccess',
  },
  {
    title: 'Telegram',
    description: 'Grupo ou canal privado',
    icon: <TelegramIcon />,
    value: 'telegram',
  },
  {
    title: 'Discord',
    description:
      'Gerencie a entrada de novos usuários em um canal do Discord e monetize um servidor',
    icon: <DiscordIcon />,
    value: 'discord',
  },
  {
    title: 'E-book/Arquivos via E-mail',
    description: 'Envie e-books ou outros arquivos diretamente para o e-mail dos clientes',
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="40" height="40" rx="20" fill="#212B36" />
        <path
          d="M12 12H20V16H24V28H12V12Z"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20 12V16H24"
          stroke="#36B37E"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: 'files',
  },
];
