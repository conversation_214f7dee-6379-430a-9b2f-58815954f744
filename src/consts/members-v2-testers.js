export const MEMBERS_V2_TESTERS_CONFIG = {
  emails: [
    'is<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
    'g<PERSON><PERSON><PERSON><PERSON>@cakto.com.br',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ]
};

export const isUserMembersV2Tester = (user) => {
  if (!user) return false;

  const { emails } = MEMBERS_V2_TESTERS_CONFIG;

  return user.email && emails.includes(String(user.email));
};


export const canUserAccessMembersV2 = (user) => {
  if (!user) return false;

  // Todos os produtores podem acessar a área de membros v2
  return user.is_producer === true;
};

export const generateMembersV2Url = (productId, productName) => {
  const base = process.env.REACT_APP_MEMBERS_V2_BASE_URL || 'https://aluno.cakto.com.br';
  const encodedProductName = encodeURIComponent(productName || '');

  return `${base}/app/admin/courses/create?caktoProductId=${productId}&caktoProductName=${encodedProductName}`;
};
