import React from 'react';
import { SvgIcon } from '@mui/material';

const WithdrawPixIcon = (props) => (
  <SvgIcon {...props} viewBox="0 0 41 40">
    <g clipPath="url(#clip0_3081_18907)">
      <path
        d="M26.15 27.5335L21.1333 22.5168C20.95 22.3335 20.7333 22.3002 20.6166 22.3002C20.5 22.3002 20.2833 22.3335 20.1 22.5168L15.0666 27.5502C14.5 28.1168 13.6166 29.0335 10.6666 29.0335L16.85 35.2002C17.7875 36.1365 19.0583 36.6624 20.3833 36.6624C21.7083 36.6624 22.9791 36.1365 23.9166 35.2002L30.1166 29.0168C28.6 29.0168 27.3333 28.7168 26.15 27.5335ZM15.0666 12.4502L20.1 17.4835C20.2333 17.6168 20.4333 17.7002 20.6166 17.7002C20.8 17.7002 21 17.6168 21.1333 17.4835L26.1166 12.5002C27.3 11.2668 28.65 10.9835 30.1666 10.9835L23.9666 4.80016C23.0291 3.86382 21.7583 3.33789 20.4333 3.33789C19.1083 3.33789 17.8375 3.86382 16.9 4.80016L10.7166 10.9668C13.65 10.9668 14.55 11.9335 15.0666 12.4502Z"
        fill="currentColor"
      />
      <path
        d="M35.5833 16.4162L31.8333 12.6495H29.7333C28.8333 12.6495 27.9333 13.0162 27.3166 13.6662L22.3166 18.6662C21.8499 19.1329 21.2333 19.3662 20.6166 19.3662C19.9816 19.3573 19.3738 19.107 18.9166 18.6662L13.8833 13.6162C13.2499 12.9829 12.3833 12.6162 11.4666 12.6162H9.0166L5.19994 16.4495C4.2636 17.387 3.73767 18.6579 3.73767 19.9829C3.73767 21.3079 4.2636 22.5787 5.19994 23.5162L9.0166 27.3495H11.4833C12.3833 27.3495 13.2499 26.9829 13.8999 26.3495L18.9333 21.3162C19.3999 20.8495 20.0166 20.6162 20.6333 20.6162C21.2499 20.6162 21.8666 20.8495 22.3333 21.3162L27.3499 26.3329C27.9833 26.9662 28.8499 27.3329 29.7666 27.3329H31.8666L35.6166 23.5662C36.5577 22.6123 37.0825 21.3243 37.0763 19.9843C37.07 18.6443 36.5332 17.3613 35.5833 16.4162Z"
        fill="currentColor"
      />
      <path
        d="M34.7333 1.66699C34.7333 1.11471 34.2856 0.666992 33.7333 0.666992C33.181 0.666992 32.7333 1.11471 32.7333 1.66699L34.7333 1.66699ZM33.0262 12.3741C33.4167 12.7646 34.0499 12.7646 34.4404 12.3741L40.8043 6.01014C41.1949 5.61961 41.1949 4.98645 40.8043 4.59592C40.4138 4.2054 39.7807 4.2054 39.3901 4.59592L33.7333 10.2528L28.0764 4.59592C27.6859 4.2054 27.0527 4.2054 26.6622 4.59592C26.2717 4.98645 26.2717 5.61961 26.6622 6.01014L33.0262 12.3741ZM32.7333 1.66699V11.667H34.7333V1.66699L32.7333 1.66699Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_3081_18907">
        <rect width="40" height="40" fill="currentColor" transform="translate(0.400024)" />
      </clipPath>
    </defs>
  </SvgIcon>
);

export default WithdrawPixIcon;
