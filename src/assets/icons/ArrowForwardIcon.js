import React from 'react';
import { SvgIcon } from '@mui/material';
import { useTheme } from '@mui/material/styles';

const ArrowForwardIcon = (props) => {
  const theme = useTheme();

  const PRIMARY_MAIN = theme.palette.text.primary;

  // eslint-disable-next-line react/prop-types
  const { path = {}, ...rest } = props;

  return (
    <SvgIcon {...rest} viewBox="0 0 24 24">
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10 16L14 12L10 8"
          stroke={PRIMARY_MAIN}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...path}
        />
      </svg>
    </SvgIcon>
  );
};

export default ArrowForwardIcon;
