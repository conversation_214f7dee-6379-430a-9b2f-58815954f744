import React from 'react';
import { SvgIcon } from '@mui/material';

const TrashIcon = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 24">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.5428 21.0037H8.45698C7.28078 21.0037 6.30288 20.0981 6.21267 18.9254L5.24707 6.37256H18.7527L17.7871 18.9254C17.6969 20.0981 16.719 21.0037 15.5428 21.0037V21.0037Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.0032 6.37277H3.99658"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.1865 2.99658H14.8138C15.4354 2.99658 15.9393 3.50047 15.9393 4.12205V6.37299H8.06104V4.12205C8.06104 3.82356 8.17961 3.53729 8.39068 3.32622C8.60174 3.11516 8.88801 2.99658 9.1865 2.99658Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.99951 17.0022H14.0012"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </SvgIcon>
);

export default TrashIcon;
