import React from 'react';
import { SvgIcon } from '@mui/material';

const CopyIcon = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.667 11.3333H7.33366C5.8609 11.3333 4.66699 10.1394 4.66699 8.66667V4.66667C4.66699 3.19391 5.8609 2 7.33366 2H11.3337C12.8064 2 14.0003 3.19391 14.0003 4.66667V8C14.0003 9.84095 12.5079 11.3333 10.667 11.3333Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 11.3335H8.96296C9.5357 11.3335 10 10.8692 10 10.2965V9.40757C10 8.26209 10.9286 7.3335 12.0741 7.3335H12.963C13.238 7.3335 13.5018 7.22424 13.6963 7.02975C13.8907 6.83527 14 6.5715 14 6.29646V5.3335"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3333 11.2665V11.3332V11.3332C11.3333 12.8059 10.1394 13.9998 8.66667 13.9998H4.66667C3.19391 13.9998 2 12.8059 2 11.3332V7.33317C2 5.86041 3.19391 4.6665 4.66667 4.6665V4.6665"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </SvgIcon>
);

export default CopyIcon;
