import React from 'react';
import { SvgIcon } from '@mui/material';

const SendPixIcon = (props) => (
  <SvgIcon {...props} viewBox="0 0 41 40">
    <g clipPath="url(#clip0_3081_18910)">
      <path
        d="M25.95 27.5335L20.9333 22.5168C20.75 22.3335 20.5333 22.3002 20.4167 22.3002C20.3 22.3002 20.0833 22.3335 19.9 22.5168L14.8667 27.5502C14.3 28.1168 13.4167 29.0335 10.4667 29.0335L16.65 35.2002C17.5875 36.1365 18.8583 36.6624 20.1833 36.6624C21.5083 36.6624 22.7792 36.1365 23.7167 35.2002L29.9167 29.0168C28.4 29.0168 27.1333 28.7168 25.95 27.5335ZM14.8667 12.4502L19.9 17.4835C20.0333 17.6168 20.2333 17.7002 20.4167 17.7002C20.6 17.7002 20.8 17.6168 20.9333 17.4835L25.9167 12.5002C27.1 11.2668 28.45 10.9835 29.9667 10.9835L23.7667 4.80016C22.8292 3.86382 21.5583 3.33789 20.2333 3.33789C18.9083 3.33789 17.6375 3.86382 16.7 4.80016L10.5167 10.9668C13.45 10.9668 14.35 11.9335 14.8667 12.4502Z"
        fill="currentColor"
      />
      <path
        d="M35.3833 16.4162L31.6333 12.6495H29.5333C28.6333 12.6495 27.7333 13.0162 27.1167 13.6662L22.1167 18.6662C21.65 19.1329 21.0333 19.3662 20.4167 19.3662C19.7816 19.3573 19.1738 19.107 18.7167 18.6662L13.6833 13.6162C13.05 12.9829 12.1833 12.6162 11.2667 12.6162H8.81665L4.99999 16.4495C4.06365 17.387 3.53772 18.6579 3.53772 19.9829C3.53772 21.3079 4.06365 22.5787 4.99999 23.5162L8.81665 27.3495H11.2833C12.1833 27.3495 13.05 26.9829 13.7 26.3495L18.7333 21.3162C19.2 20.8495 19.8167 20.6162 20.4333 20.6162C21.05 20.6162 21.6667 20.8495 22.1333 21.3162L27.15 26.3329C27.7833 26.9662 28.65 27.3329 29.5667 27.3329H31.6667L35.4167 23.5662C36.3577 22.6123 36.8826 21.3243 36.8763 19.9843C36.8701 18.6443 36.3333 17.3613 35.3833 16.4162Z"
        fill="currentColor"
      />
      <path
        d="M32.5333 11.667C32.5333 12.2193 32.981 12.667 33.5333 12.667C34.0856 12.667 34.5333 12.2193 34.5333 11.667H32.5333ZM34.2404 0.959886C33.8499 0.569361 33.2167 0.569361 32.8262 0.959886L26.4623 7.32385C26.0717 7.71437 26.0717 8.34754 26.4623 8.73806C26.8528 9.12858 27.4859 9.12858 27.8765 8.73806L33.5333 3.08121L39.1902 8.73806C39.5807 9.12858 40.2139 9.12858 40.6044 8.73806C40.9949 8.34754 40.9949 7.71437 40.6044 7.32385L34.2404 0.959886ZM34.5333 11.667V1.66699H32.5333V11.667H34.5333Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_3081_18910">
        <rect width="40" height="40" fill="currentColor" transform="translate(0.200073)" />
      </clipPath>
    </defs>
  </SvgIcon>
);

export default SendPixIcon;
