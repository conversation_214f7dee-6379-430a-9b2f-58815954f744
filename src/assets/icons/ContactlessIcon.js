import React from 'react';
import { SvgIcon } from '@mui/material';

const ContactlessIcon = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 24">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.0002 19.4092C15.7532 15.3172 15.7532 8.68418 12.0002 4.59218"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.196 22.4058C21.268 16.6588 21.268 7.34076 16.196 1.59376"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.179 16.4209C10.607 13.9799 10.607 10.0209 8.179 7.5799"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </SvgIcon>
);

export default ContactlessIcon;
