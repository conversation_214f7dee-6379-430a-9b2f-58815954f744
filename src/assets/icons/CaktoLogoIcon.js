import PropTypes from 'prop-types';
import React from 'react';

const CaktoLogoIcon = (props) => (
  <svg
    version="1.0"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 8000.000000 2994.000000"
    preserveAspectRatio="xMidYMid meet"
    {...props}
  >
    <g
      transform="translate(0.000000,2994.000000) scale(0.100000,-0.100000)"
      fill={props?.fill ?? '#000000'}
      stroke="none"
    >
      <path
        d="M12390 29184 c-1006 -54 -1781 -242 -2535 -614 -949 -468 -1792
 -1238 -2451 -2240 -539 -820 -830 -1659 -893 -2580 -13 -183 -10 -560 6 -750
 52 -647 206 -2250 253 -2655 119 -1002 314 -2071 585 -3210 153 -640 268
 -1020 649 -2155 201 -598 295 -905 426 -1395 34 -126 92 -324 129 -440 63
 -194 69 -219 75 -327 5 -93 4 -118 -6 -118 -19 0 -302 145 -458 235 -966 555
 -1550 1272 -1719 2110 -31 152 -85 547 -136 980 -24 215 -54 464 -66 555 -94
 747 -287 1402 -599 2035 -117 237 -192 365 -295 505 -277 372 -649 611 -1099
 706 -150 32 -477 44 -647 25 -567 -66 -1132 -328 -1537 -716 -381 -363 -695
 -994 -847 -1696 -96 -448 -136 -860 -140 -1449 -3 -412 0 -509 30 -855 65
 -746 256 -1683 492 -2410 213 -658 547 -1354 795 -1657 237 -291 558 -515 913
 -638 306 -105 1755 -429 2639 -590 850 -154 1251 -195 2456 -250 678 -31 683
 -31 716 -47 17 -8 37 -26 43 -41 18 -39 38 -266 125 -1412 7 -96 12 -176 12
 -177 -1 -1 -123 -20 -271 -43 -1823 -281 -2971 -700 -3203 -1169 -35 -71 -37
 -80 -36 -171 0 -113 22 -169 99 -265 109 -133 240 -231 476 -353 940 -490
 2973 -858 5439 -987 702 -36 885 -40 1935 -40 1052 0 1245 4 1935 40 2592 137
 4678 532 5559 1054 396 235 537 481 415 724 -159 318 -735 612 -1694 866 -430
 114 -1026 230 -1645 321 -147 21 -269 41 -272 44 -3 3 2 69 11 148 91 814 252
 2581 356 3903 63 809 170 2327 170 2414 0 25 6 34 27 42 27 10 496 31 1523 69
 953 35 1442 69 2100 146 721 84 1607 239 1993 348 407 115 708 250 1022 459
 180 120 303 223 475 396 364 367 629 795 824 1333 222 614 329 1370 391 2763
 3 72 8 164 10 205 2 41 7 154 11 250 3 96 8 189 10 205 8 79 4 572 -6 707 -46
 630 -188 1175 -443 1693 -272 552 -579 930 -968 1188 -247 164 -478 256 -789
 314 -150 27 -541 25 -714 -5 -771 -134 -1290 -522 -1645 -1231 -191 -379 -380
 -1002 -521 -1711 -50 -252 -106 -577 -175 -1025 -82 -527 -126 -726 -212 -954
 -100 -268 -318 -615 -576 -916 -104 -122 -367 -376 -499 -482 -395 -320 -860
 -569 -1398 -750 -136 -46 -210 -66 -243 -66 -26 1 -50 3 -53 6 -3 2 -4 135 -3
 293 9 1039 0 2520 -16 2694 -2 25 -7 131 -10 235 -35 1149 -156 2421 -310
 3261 -274 1491 -711 2537 -1395 3334 -254 296 -631 646 -964 894 -472 352
 -1026 644 -1526 806 -449 145 -878 231 -1350 270 -130 11 -646 20 -760 14z"
      />
      <path
        d="M50970 15105 l0 -7175 1385 0 1385 0 2 3096 3 3097 184 -314 c102
 -173 280 -476 396 -674 116 -198 319 -542 450 -765 130 -223 274 -468 320
 -545 45 -77 166 -282 267 -455 153 -261 580 -987 1910 -3253 l111 -187 1714 0
 c943 0 1712 3 1710 8 -6 10 -327 555 -1652 2807 -615 1045 -1153 1959 -1196
 2032 l-77 132 86 73 c861 725 1466 1784 1701 2978 98 498 124 872 119 1720
 l-3 495 -1385 0 -1385 0 -6 -695 c-6 -706 -7 -736 -50 -1004 -77 -480 -263
 -927 -518 -1246 -70 -88 -220 -240 -307 -311 -272 -222 -623 -383 -1014 -463
 -259 -53 -330 -58 -872 -63 l-508 -5 0 3946 0 3946 -1385 0 -1385 0 0 -7175z"
      />
      <path
        d="M61303 16628 c3 -4424 -2 -4055 63 -4478 196 -1275 811 -2385 1746
 -3152 840 -690 1860 -1075 3071 -1159 193 -13 800 -7 917 10 115 16 482 72
 508 77 l22 4 0 1280 c0 1166 -1 1280 -16 1280 -9 0 -95 -13 -192 -30 -649
 -108 -1186 -86 -1712 70 -432 129 -828 390 -1091 720 -307 385 -487 885 -539
 1495 -6 72 -10 597 -10 1308 l0 1187 1505 0 1505 0 0 1295 0 1295 -1505 0
 -1505 0 0 1405 0 1405 -1385 0 -1385 0 3 -4012z"
      />
      <path
        d="M34085 18269 c-318 -22 -698 -76 -975 -138 -2104 -470 -3536 -2025
 -3859 -4191 -49 -328 -56 -443 -56 -905 0 -472 7 -582 61 -935 168 -1097 629
 -2061 1333 -2785 200 -205 365 -352 566 -500 762 -565 1663 -896 2720 -999
 441 -44 1104 -38 1720 14 717 61 1511 206 2035 372 l105 34 3 1392 c1 767 -2
 1392 -6 1392 -5 0 -56 -22 -113 -49 -638 -299 -1592 -491 -2640 -532 -798 -31
 -1422 82 -1891 342 -632 351 -985 916 -1100 1759 -20 145 -22 208 -22 510 0
 271 3 373 17 475 49 363 131 644 263 902 100 195 199 332 352 486 399 404 960
 626 1736 687 71 5 251 10 400 10 1051 0 2203 -213 2875 -531 68 -33 126 -59
 128 -59 1 0 3 632 3 1405 l0 1404 -112 35 c-190 58 -247 74 -438 120 -559 134
 -1142 220 -1840 272 -253 18 -1069 27 -1265 13z"
      />
      <path
        d="M43545 18269 c-1150 -75 -2160 -426 -2960 -1028 -258 -194 -560 -476
 -757 -706 -620 -727 -1018 -1630 -1158 -2630 -135 -964 -32 -1941 297 -2824
 211 -568 528 -1107 921 -1566 82 -95 357 -376 452 -461 302 -269 664 -516
 1035 -704 530 -268 1094 -431 1740 -502 164 -18 828 -18 990 0 423 47 821 149
 1160 297 589 256 1107 696 1480 1258 32 48 62 84 65 80 4 -4 17 -48 29 -98 97
 -400 280 -962 445 -1365 l34 -85 1387 -3 c1109 -2 1386 0 1382 10 -3 7 -26 63
 -52 123 -261 612 -455 1298 -574 2025 -115 701 -160 1321 -176 2420 -9 626
 -26 913 -76 1275 -172 1263 -660 2309 -1440 3086 -778 774 -1824 1244 -3059
 1373 -332 35 -846 46 -1165 25z m833 -2594 c1113 -116 1791 -745 2032 -1883
 125 -594 116 -1198 -27 -1732 -217 -811 -711 -1327 -1484 -1549 -422 -122
 -968 -152 -1464 -80 -982 141 -1639 686 -1912 1584 -180 595 -185 1390 -12
 1995 159 558 465 991 899 1276 339 221 775 360 1250 397 139 11 583 6 718 -8z"
      />
      <path
        d="M73235 18274 c-509 -37 -839 -86 -1217 -183 -1214 -310 -2211 -1002
 -2878 -1996 -212 -316 -419 -732 -561 -1129 -298 -829 -389 -1875 -248 -2816
 166 -1100 633 -2070 1354 -2809 871 -893 2049 -1405 3453 -1501 194 -13 722
 -13 912 0 1142 79 2119 425 2935 1039 338 254 703 628 962 986 477 657 790
 1449 917 2320 81 552 79 1209 -5 1770 -130 869 -454 1668 -944 2330 -367 497
 -855 937 -1380 1247 -687 405 -1425 634 -2325 724 -155 15 -839 28 -975 18z
 m700 -2594 c930 -86 1607 -535 1954 -1295 136 -297 217 -622 252 -1005 15
 -169 6 -646 -15 -810 -47 -356 -138 -666 -275 -941 -194 -388 -467 -682 -831
 -897 -417 -246 -1019 -370 -1621 -334 -731 44 -1267 263 -1674 683 -393 405
 -615 946 -676 1648 -15 176 -7 635 15 801 106 800 448 1397 1012 1767 328 215
 719 339 1214 386 85 9 545 6 645 -3z"
      />
    </g>
  </svg>
);

CaktoLogoIcon.propTypes = {
  fill: PropTypes.string,
};

export default CaktoLogoIcon;
