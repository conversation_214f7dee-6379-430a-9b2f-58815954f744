import PropTypes from 'prop-types';
import { Stack } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import LoadingScreen from '@/components/loading-screen';
import CarouselBanner from '@/sections/@dashboard/home/<USER>';
import { fetchBannerService } from '@/services/banner';
import { StyledContent, StyledRoot, StyledSection } from './styles';

// ----------------------------------------------------------------------

RegisterLayout.propTypes = {
  children: PropTypes.node,
};

export default function RegisterLayout({ children }) {
  const { data, isLoading } = useQuery({
    queryKey: ['banners-login'],
    async queryFn() {
      return fetchBannerService({ location: 'register' });
    },
  });

  if (isLoading) return <LoadingScreen />;

  return (
    <StyledRoot>
      <StyledSection>
        <CarouselBanner hideBullets maxHeight banners={data} fetching={isLoading} disabled />
      </StyledSection>

      <StyledContent>
        <Stack sx={{ width: 1 }}> {children} </Stack>
      </StyledContent>
    </StyledRoot>
  );
}
