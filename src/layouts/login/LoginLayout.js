import LoadingScreen from '@/components/loading-screen';
import useResponsive from '@/hooks/useResponsive';
import CarouselBanner from '@/sections/@dashboard/home/<USER>';
import { fetchBannerService } from '@/services/banner';
import { Stack } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import PropTypes from 'prop-types';
import { StyledContent, StyledRoot, StyledSection } from './styles';

// ----------------------------------------------------------------------

LoginLayout.propTypes = {
  children: PropTypes.node,
};

export default function LoginLayout({ children }) {
  const isMobile = useResponsive('down', 'sm');

  const { data, isLoading } = useQuery({
    queryKey: ['banners-login'],
    async queryFn() {
      return fetchBannerService({ location: 'login' });
    },
  });

  if (isLoading) return <LoadingScreen />;

  return (
    <StyledRoot>
      <StyledSection>
        <CarouselBanner hideBullets maxHeight banners={data} fetching={isLoading} disabled />
      </StyledSection>

      <StyledContent>
        <Stack
          sx={{
            width: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: {
              xs: '20px',
              sm: '40px',
              md: '60px',
            },
          }}
        >
          {isMobile && (
            <CarouselBanner hideBullets maxHeight banners={data} fetching={isLoading} disabled />
          )}

          {children}
        </Stack>
      </StyledContent>
    </StyledRoot>
  );
}
