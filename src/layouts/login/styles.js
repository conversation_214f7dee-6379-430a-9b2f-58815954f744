import { styled } from '@mui/material/styles';

// ----------------------------------------------------------------------

export const StyledRoot = styled('main')(() => ({
  minHeight: '100%',
  display: 'flex',
  position: 'relative',
}));

export const StyledSection = styled('div')(({ theme }) => ({
  display: 'none',
  position: 'relative',
  [theme.breakpoints.up('md')]: {
    flex: '1 1 50%',
    maxWidth: '60%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  [theme.breakpoints.up('lg')]: {
    flex: '1 1 55%',
    maxWidth: '65%',
  },
  [theme.breakpoints.up('xl')]: {
    flex: '1 1 60%',
    maxWidth: '70%',
  },
}));

export const StyledSectionBg = styled('div')(({ theme }) => ({
  background: "url('/assets/capa-login-cakto.png?v2')",
  backgroundSize: 'contain',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center center',
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: -1,
  width: '100%',
  height: '100%',
  position: 'absolute',
}));

export const StyledContent = styled('div')(({ theme }) => ({
  width: '100%',
  maxWidth: 480,
  margin: 'auto',
  display: 'flex',
  minHeight: '100vh',
  justifyContent: 'center',
  padding: theme.spacing(0, 2),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(0, 3),
    maxWidth: 500,
  },
  [theme.breakpoints.up('md')]: {
    width: '50%',
    minWidth: 400,
    maxWidth: 550, 
    margin: 0,
    padding: theme.spacing(0, 2),
  },
  [theme.breakpoints.up('lg')]: {
    width: '45%',
    minWidth: 450,
    maxWidth: 600,
    padding: theme.spacing(0, 3),
  },
  [theme.breakpoints.up('xl')]: {
    width: '40%',
    minWidth: 500,
    maxWidth: 650,
    flexShrink: 0,
    padding: theme.spacing(0, 4),
  },
}));
