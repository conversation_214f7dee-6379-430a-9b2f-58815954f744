import { Box, Stack } from '@mui/material';
import { NAV } from '../../../config-global';
import { hideScrollbarX } from '../../../utils/cssStyles';
import { NavSectionMini } from '../../../components/nav-section';
import LogoIcon from '../../../components/logo/LogoIcon';
import NavToggleButton from './NavToggleButton';
import Navigation from './config-navigation';

// ----------------------------------------------------------------------

export default function NavMini() {
  const navConfig = Navigation();

  return (
    <Box
      component="nav"
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_DASHBOARD_MINI },
      }}
    >
      <NavToggleButton
        sx={{
          top: 22,
          left: NAV.W_DASHBOARD_MINI - 12,
        }}
      />

      <Stack
        sx={{
          pb: 2,
          height: 1,
          position: 'fixed',
          width: NAV.W_DASHBOARD_MINI,
          borderRight: (theme) => `dashed 1px ${theme.palette.divider}`,
          ...hideScrollbarX,
        }}
      >
        <LogoIcon />
        <NavSectionMini data={navConfig} />
      </Stack>
    </Box>
  );
}
