import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { billingLevels } from '@/utils/constants';
// @mui
import { Box, Drawer, Stack } from '@mui/material';
// hooks
import useResponsive from '../../../hooks/useResponsive';
// config
import { NAV } from '../../../config-global';
// components
import Logo from '../../../components/logo';
import { NavSectionVertical } from '../../../components/nav-section';
import Scrollbar from '../../../components/scrollbar';
//
import { useAuthContext } from '../../../auth/useAuthContext';
import NavToggleButton from './NavToggleButton';
import Navigation from './config-navigation';
import { BillingCard } from './BillingCard';

// ----------------------------------------------------------------------

NavVertical.propTypes = {
  openNav: PropTypes.bool,
  onCloseNav: PropTypes.func,
};

export default function NavVertical({ openNav, onCloseNav }) {
  const navConfig = Navigation();

  const { pathname } = useLocation();

  const { user } = useAuthContext();

  // const companies = user?.companies || [];

  // const [company_id, setCompanyId] = useState(companies[0]?.id || '');

  const isDesktop = useResponsive('up', 'lg');

  const totalSales = user?.totalSales || 0;

  const currentLevel = billingLevels.find((level) => totalSales >= level.min_value && (level.max_value === null || totalSales < level.max_value)) || billingLevels[0];
  const nextAward = currentLevel.max_value || 0;
  const prizeAchieved = totalSales > nextAward;

  const [previousTotalSales, setPreviousTotalSales] = useState(0);
  const [triggerLoginEffect, setTriggerLoginEffect] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    const lastKnownSales = localStorage.getItem('lastKnownSales');
    const lastKnownLevel = localStorage.getItem('lastKnownLevel');
    
    if (!hasInitialized && totalSales > 0) {
      if (!lastKnownSales || !lastKnownLevel || lastKnownLevel !== currentLevel.name) {
        setTriggerLoginEffect(true);
        setTimeout(() => setTriggerLoginEffect(false), 1000);
      }
      
      localStorage.setItem('lastKnownSales', totalSales.toString());
      localStorage.setItem('lastKnownLevel', currentLevel.name);
      setHasInitialized(true);
    }
    
    setPreviousTotalSales(totalSales);
  }, [totalSales, hasInitialized, currentLevel]);

  useEffect(() => {
    if (!user) {
      localStorage.removeItem('lastKnownSales');
      localStorage.removeItem('lastKnownLevel');
      setHasInitialized(false);
    }
  }, [user]);

  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  /* useEffect(() => {
    AuthenticatedHttp.interceptors.request.use((config) => {
      if (company_id) config.headers.Http_collaborator_id = company_id;
      return config;
    })
  }, [company_id]); */

  // const onChangeCompany = (value) => setCompanyId(value);

  const renderContent = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': {
          height: 1,
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <Stack
        spacing={3}
        sx={{
          pt: 3,
          pb: 2,
          px: 2.5,
          flexShrink: 0,
        }}
      >
        <Logo />

        {/*  <Grid item xs={5} md={2.5}>
          <FormControl sx={{ width: '100%' }} size="small">
            <InputLabel id="company_id">Empresas</InputLabel>
            <Select
              labelId="company_id"
              id="company_id"
              label="Empresa"
              value={company_id}
              onChange={(e) => onChangeCompany(e.target.value)}
            >
              {companies?.length > 0 && companies.map((_company) => (
                <MenuItem key={_company.id} value={_company.id}>{_company.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid> */}

          <Link to="/dashboard/awards" className="cursor-pointer">
              <BillingCard
                totalSales={totalSales}
                nextAward={nextAward}
                showConfetti={prizeAchieved}
                previousTotalSales={previousTotalSales}
                triggerLoginEffect={triggerLoginEffect}
              />
          </Link>
      </Stack>

      <NavSectionVertical data={navConfig} onItemClick={onCloseNav} />
    </Scrollbar>
  );

  return (
    <Box
      component="nav"
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_DASHBOARD },
      }}
    >
      <NavToggleButton />

      {isDesktop ? (
        <Drawer
          open
          variant="permanent"
          PaperProps={{
            sx: {
              zIndex: 0,
              width: NAV.W_DASHBOARD,
              bgcolor: 'transparent',
              borderRightStyle: 'dashed',
            },
          }}
        >
          {renderContent}
        </Drawer>
      ) : (
        <Drawer
          open={openNav}
          onClose={onCloseNav}
          ModalProps={{
            keepMounted: true,
          }}
          PaperProps={{
            sx: {
              width: NAV.W_DASHBOARD,
            },
          }}
        >
          {renderContent}
        </Drawer>
      )}
    </Box>
  );
}
