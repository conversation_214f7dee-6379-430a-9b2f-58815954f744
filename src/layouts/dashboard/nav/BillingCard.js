"use client"

import { useEffect, useMemo, useState, useRef } from "react"
import PropTypes from 'prop-types'
import confetti from "canvas-confetti"
import { billingLevels } from '@/utils/constants'
import { useSettingsContext } from "@/components/settings"


function fShortCurrency(value, options) {
    const formatter = new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
        ...options,
    })

    if (value >= 1000000) {
        return `${formatter
            .format(value / 1000000)
            .replace("R$", "R$")
            .replace(",00", "")}M`
    }

    if (value >= 1000) {
        return `${formatter
            .format(value / 1000)
            .replace("R$", "R$")
            .replace(",00", "")}K`
    }

    return formatter.format(value)
}

export function BillingCard({
    totalSales,
    nextAward,
    previousTotalSales = 0,
    triggerLoginEffect = false
}) {
    const [isBlinking, setIsBlinking] = useState(false)
    const [shownLevels, setShownLevels] = useState(new Set())
    const previousLevelRef = useRef(null)
    const initialLoadRef = useRef(true)
    const { themeMode } = useSettingsContext();

    const currentLevel = useMemo(() =>
        billingLevels.find((level) => totalSales >= level.min_value && (level.max_value === null || totalSales < level.max_value)) ||
        billingLevels[0]
        , [totalSales])

    const percentConclude = nextAward ? (totalSales / nextAward) * 100 : 100

    const prizeAchieved = totalSales > nextAward

    const awards = useMemo(() => {
        if (!nextAward) {
            return fShortCurrency(totalSales);
        }
        return `${fShortCurrency(totalSales, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })
            .replace(/R\$\s?(\d+(?:,\d+)?)\s?mil/g, 'R$ $1K')
            .replace(/(\d+,\d+)k/g, (match) => match.replace(/,/g, '.'))
            .replace(
                /R\$\s?(\d+,\d+)\s?mi\b/g,
                (match, grupo1) => `R$ ${grupo1.replace(',', '.')}M`
            )} / ${fShortCurrency(nextAward, 0)
                ?.replace(/R\$\s?(\d+)\s?mil/g, 'R$ $1K')
                .replace(/R\$\s?(\d+)\s?mi\b/g, 'R$ $1M')}`;
    }, [nextAward, totalSales]);

    const triggerConfettiEffect = useMemo(() => (level, reason) => {
        setIsBlinking(true)

        const duration = reason === "levelUp" ? 4000 : 3000
        const end = Date.now() + duration

        let colors
        if (level.color.includes("red") || level.color.includes("pink")) {
            colors = ["#ef4444", "#f87171", "#fca5a5"]
        } else if (level.color.includes("emerald") || level.color.includes("green")) {
            colors = ["#10b981", "#34d399", "#6ee7b7"]
        } else if (level.color.includes("yellow") || level.color.includes("amber")) {
            colors = ["#f59e0b", "#fbbf24", "#fcd34d"]
        } else if (level.color.includes("cyan") || level.color.includes("blue")) {
            colors = ["#06b6d4", "#22d3ee", "#67e8f9"]
        } else if (level.color.includes("gray") || level.color.includes("slate")) {
            colors = ["#6b7280", "#9ca3af", "#d1d5db"]
        } else {
            colors = ["#8b5cf6", "#a78bfa", "#c4b5fd"]
        }

        const frame = () => {
            const particleCount = reason === "levelUp" ? 4 : 2

            confetti({
                particleCount,
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors,
            })
            confetti({
                particleCount,
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors,
            })

            if (reason === "levelUp") {
                confetti({
                    particleCount: 3,
                    angle: 90,
                    spread: 45,
                    origin: { x: 0.5, y: 0.6 },
                    colors,
                })
            }

            if (Date.now() < end) {
                requestAnimationFrame(frame)
            }
        }

        frame()

        setTimeout(() => setIsBlinking(false), reason === "levelUp" ? 3000 : 2000)
    }, [])

    useEffect(() => {
        const hasLevelChanged = previousLevelRef.current && previousLevelRef.current.name !== currentLevel.name
        const hasProgressIncreased = totalSales > previousTotalSales
        const isNewLevel = !shownLevels.has(currentLevel.name)

        if (hasLevelChanged && hasProgressIncreased && isNewLevel) {
            console.log("[v0] Level up detected:", previousLevelRef.current?.name, "->", currentLevel.name)
            triggerConfettiEffect(currentLevel, "levelUp")
            setShownLevels(prev => new Set([...prev, currentLevel.name]))
        }

        previousLevelRef.current = currentLevel
    }, [totalSales, previousTotalSales, currentLevel, shownLevels, triggerConfettiEffect])

    useEffect(() => {
        if (triggerLoginEffect && totalSales > 0 && initialLoadRef.current) {
            const isNewLevel = !shownLevels.has(currentLevel.name)

            if (isNewLevel) {
                console.log("[v0] Login effect triggered for new level:", currentLevel.name)
                triggerConfettiEffect(currentLevel, "login")
                setShownLevels(prev => new Set([...prev, currentLevel.name]))
            }

            initialLoadRef.current = false
        }
    }, [triggerLoginEffect, currentLevel, totalSales, triggerConfettiEffect, shownLevels])

    return (
        <div className="relative w-full max-w-2xl mx-auto">
            <div className="relative w-full h-auto">
                <img
                    src={currentLevel.backgroundImage || "/placeholder.svg"}
                    alt={`Nível ${currentLevel.name} - Faturamento`}
                    width={528}
                    height={169}
                    className={`
            w-full h-auto transition-all duration-300
            ${isBlinking ? "animate-pulse shadow-lg shadow-current" : ""}
          `}
                />

                <div className="absolute inset-0 flex items-center justify-between px-2 pt-[0.7rem] pb-[1.2rem]">
                    <div className="flex items-center gap-4 flex-1 ml-10">
                        <div className="flex-1">
                            <h3 className={`text-xs mb-1 ${themeMode === 'dark' ? currentLevel.textColorDark : currentLevel.textColor}`}>Faturamento</h3>

                            <div className={`text-xs font-bold mb-1 ${themeMode === 'dark' ? currentLevel.textColorDark : currentLevel.textColor}   `}>{awards}</div>

                            <div className="relative">
                                <div className="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden max-w-[80%]">
                                    <div
                                        className={`
                      transition-all duration-1000 ease-out
                      ${isBlinking ? "animate-pulse" : ""}
                    `}
                                        style={{
                                            ...currentLevel.progressStyle,
                                            width: `${prizeAchieved ? 100 : percentConclude}%`,
                                            position: "relative",
                                        }}
                                    />
                                    <div className={`absolute right-1 top-0 -mt-2 text-sm font-medium ${themeMode === 'dark' ? currentLevel.textColorDark : currentLevel.textColor} `}>
                                        {Math.round(prizeAchieved ? 100 : percentConclude)}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

BillingCard.propTypes = {
    totalSales: PropTypes.number,
    nextAward: PropTypes.number,
    previousTotalSales: PropTypes.number,
    triggerLoginEffect: PropTypes.bool,
}
