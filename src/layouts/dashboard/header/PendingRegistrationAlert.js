import { usePendingRegistration } from '@/hooks/usePendingRegistration';
import { Alert, Stack, Typography } from '@mui/material';
import { useNavigate } from 'react-router';

export const PendingRegistrationAlert = () => {
  const navigate = useNavigate();

  const { isPendingRegistration } = usePendingRegistration();

  if (!isPendingRegistration) {
    return null;
  }

  return (
    <Stack
      sx={{
        backgroundColor: '#FCAB02',
        cursor: 'pointer',
        width: '100%',
      }}
      justifyContent="center"
      alignItems="center"
      onClick={() => navigate('/dashboard/financial?tab=identity')}
    >
      <Alert
        severity="warning"
        variant="filled"
        sx={{
          color: 'black',
          textAlign: 'center',
        }}
      >
        <Typography
          variant="body2"
          fontWeight="bold"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: '2',
            WebkitBoxOrient: 'vertical',
          }}
        >
          Para fazer sua primeira venda, complete seu cadastro
        </Typography>
      </Alert>
    </Stack>
  );
};
