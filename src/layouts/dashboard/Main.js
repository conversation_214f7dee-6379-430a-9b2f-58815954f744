/* eslint-disable no-eval */
import PropTypes from 'prop-types';
// @mui
import { Box } from '@mui/material';
// hooks
import { usePendingRegistration } from '@/hooks/usePendingRegistration';
import useResponsive from '../../hooks/useResponsive';
// config
import { HEADER, HEADER_ALERT, NAV } from '../../config-global';
// components
import Crisp from '../../components/crisp/Crisp';
import { useSettingsContext } from '../../components/settings';

// ----------------------------------------------------------------------

const SPACING = 8;

Main.propTypes = {
  sx: PropTypes.object,
  children: PropTypes.node,
};

export default function Main({ children, sx, ...other }) {
  const { themeLayout } = useSettingsContext();

  const isNavHorizontal = themeLayout === 'horizontal';

  const isNavMini = themeLayout === 'mini';

  const isDesktop = useResponsive('up', 'lg');

  const { isPendingRegistration } = usePendingRegistration();

  if (isNavHorizontal) {
    return (
      <>
        <Crisp />
        <Box
          component="main"
          sx={{
            pt: `${HEADER.H_MOBILE + HEADER_ALERT.HEIGHT(isPendingRegistration) + SPACING}px`,
            pb: `${HEADER.H_MOBILE + SPACING}px`,
            ...(isDesktop && {
              px: 2,
              pt: `${
                HEADER.H_DASHBOARD_DESKTOP + HEADER_ALERT.HEIGHT(isPendingRegistration) + 80
              }px`,
              pb: `${HEADER.H_DASHBOARD_DESKTOP + SPACING}px`,
            }),
          }}
        >
          {children}
        </Box>
      </>
    );
  }

  return (
    <>
      <Crisp />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          pt: `${HEADER.H_MOBILE + HEADER_ALERT.HEIGHT(isPendingRegistration) + SPACING}px`,
          pb: `${HEADER.H_MOBILE + SPACING}px`,
          ...(isDesktop && {
            px: 2,
            pt: `${
              HEADER.H_DASHBOARD_DESKTOP + HEADER_ALERT.HEIGHT(isPendingRegistration) + SPACING
            }px`,
            pb: `${HEADER.H_DASHBOARD_DESKTOP + SPACING}px`,
            width: `calc(100% - ${NAV.W_DASHBOARD}px)`,
            ...(isNavMini && {
              width: `calc(100% - ${NAV.W_DASHBOARD_MINI}px)`,
            }),
          }),
          ...sx,
        }}
        {...other}
      >
        {children}
      </Box>
    </>
  );
}
