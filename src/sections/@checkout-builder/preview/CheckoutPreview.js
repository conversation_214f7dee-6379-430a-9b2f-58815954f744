import { Alert, Paper } from '@mui/material';
import { Stack, alpha } from '@mui/system';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { useRef } from 'react';
import { Device<PERSON>rameset } from 'react-device-frameset';
// eslint-disable-next-line import/no-unresolved
import 'react-device-frameset/styles/marvel-devices.min.css';
import { useFormContext, useWatch } from 'react-hook-form';
import { CheckoutBuilderDeviceTypes } from '../constants';

const CheckoutPreview = ({ open, onClose }) => {
  const device = useWatch({
    name: 'device',
  });

  const mockup =
    {
      [CheckoutBuilderDeviceTypes.desktop.id]: 'MacBook Pro',
      [CheckoutBuilderDeviceTypes.mobile.id]: 'iPhone X',
    }[device] || 'iPhone X';

  const variants = {
    open: {
      display: 'flex',
      opacity: 1,
    },
    closed: {
      display: 'flex',
      opacity: 0,
      transitionEnd: {
        display: 'none',
      },
    },
  };

  const ref = useRef();

  const form = useFormContext();

  const config = form.getValues();

  if (ref.current) {
    ref.current.contentWindow.postMessage(
      {
        type: 'cakto-checkout-config',
        config,
        device,
      },
      '*'
    );
  }

  const url = process.env.REACT_APP_CHECKOUT_URL || '';

  return (
    <Stack
      component={motion.div}
      variants={variants}
      initial="closed"
      animate={open ? 'open' : 'closed'}
      exit="closed"
      transition={{ duration: 0.2 }}
      height="calc(100% - 64px)"
      width={1}
      position="fixed"
      sx={{
        zIndex: (theme) => `${theme.zIndex.tooltip + 2} !important`,
        bgcolor: (theme) => alpha(theme.palette.grey[900], 0.5),
      }}
      justifyContent="center"
      alignItems="center"
      onAbort={onClose}
      onClick={onClose}
    >
      <Stack
        component={Paper}
        width={1}
        height={1}
        direction="row"
        justifyContent="center"
        alignItems="center"
        sx={{
          bgcolor: (theme) => alpha(theme.palette.grey[900], 0.9),
        }}
      >
        <DeviceFrameset
          device={mockup}
          landscape={device === CheckoutBuilderDeviceTypes.desktop.id}
        >
          <Stack
            height={1}
            sx={{
              ...(device === CheckoutBuilderDeviceTypes.mobile.id && {
                pt: 5,
              }),
            }}
          >
            {url ? (
              <iframe
                ref={ref}
                title="Checkout"
                src={url.concat('/preview')}
                width="100%"
                height="100%"
                style={{ border: 'none', display: 'block' }}
              />
            ) : (
              <Alert severity="warning">URL do checkout não configurada</Alert>
            )}
          </Stack>
        </DeviceFrameset>
      </Stack>
    </Stack>
  );
};

CheckoutPreview.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default CheckoutPreview;
