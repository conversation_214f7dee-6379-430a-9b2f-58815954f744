import { MenuItem, Paper, Typography, Card } from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { RHFSelect, RHFSwitch, RHFUpload } from '@/components/hook-form';
import RHFColorPicker from '@/components/hook-form/RHFColorPicker';
import {
  CheckoutBuilderFontFamilies,
  CheckoutBuilderComponentTypes,
  ThemeModesOptions,
  lightThemeValues,
  darkThemeValues,
} from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderSettingsForm = ({ device }) => {
  const form = useFormContext();

  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const setLightMode = () => {
    form.setValue(getAttributeName('background.color'), lightThemeValues.background.color);

    form.setValue(
      getAttributeName('form.background.color'),
      lightThemeValues.form.background.color
    );

    form.setValue(getAttributeName('text.color.primary'), lightThemeValues.text.color.primary);
    form.setValue(getAttributeName('text.color.secondary'), lightThemeValues.text.color.secondary);
    form.setValue(getAttributeName('text.color.active'), lightThemeValues.text.color.active);

    form.setValue(getAttributeName('icon.color'), lightThemeValues.icon.color);

    form.setValue(
      getAttributeName('paymentOptions.button.unselected.text.color'),
      lightThemeValues.paymentOptions.button.unselected.text.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.unselected.background.color'),
      lightThemeValues.paymentOptions.button.unselected.background.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.unselected.icon.color'),
      lightThemeValues.paymentOptions.button.unselected.icon.color
    );

    form.setValue(
      getAttributeName('paymentOptions.button.selected.text.color'),
      lightThemeValues.paymentOptions.button.selected.text.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.selected.background.color'),
      lightThemeValues.paymentOptions.button.selected.background.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.selected.icon.color'),
      lightThemeValues.paymentOptions.button.selected.icon.color
    );

    form.setValue(getAttributeName('payButton.text.color'), lightThemeValues.payButton.text.color);
    form.setValue(getAttributeName('payButton.color'), lightThemeValues.payButton.color);

    form.setValue(
      getAttributeName('box.default.background.color'),
      lightThemeValues.box.default.background.color
    );
    form.setValue(
      getAttributeName('box.selected.background.color'),
      lightThemeValues.box.selected.background.color
    );
    form.setValue(
      getAttributeName('box.unselected.background.color'),
      lightThemeValues.box.unselected.background.color
    );

    form.setValue(
      getAttributeName('box.default.header.background.color'),
      lightThemeValues.box.default.header.background.color
    );
    form.setValue(
      getAttributeName('box.selected.header.background.color'),
      lightThemeValues.box.selected.header.background.color
    );
    form.setValue(
      getAttributeName('box.unselected.header.background.color'),
      lightThemeValues.box.unselected.header.background.color
    );

    form.setValue(
      getAttributeName('box.default.header.text.color.primary'),
      lightThemeValues.box.default.header.text.color.primary
    );
    form.setValue(
      getAttributeName('box.selected.header.text.color.primary'),
      lightThemeValues.box.selected.header.text.color.primary
    );
    form.setValue(
      getAttributeName('box.unselected.header.text.color.primary'),
      lightThemeValues.box.unselected.header.text.color.primary
    );

    form.setValue(
      getAttributeName('box.default.header.text.color.secondary'),
      lightThemeValues.box.default.header.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.selected.header.text.color.secondary'),
      lightThemeValues.box.selected.header.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.unselected.header.text.color.secondary'),
      lightThemeValues.box.unselected.header.text.color.secondary
    );

    form.setValue(
      getAttributeName('box.default.text.color.primary'),
      lightThemeValues.box.default.text.color.primary
    );
    form.setValue(
      getAttributeName('box.selected.text.color.primary'),
      lightThemeValues.box.selected.text.color.primary
    );
    form.setValue(
      getAttributeName('box.unselected.text.color.primary'),
      lightThemeValues.box.unselected.text.color.primary
    );

    form.setValue(
      getAttributeName('box.default.text.color.secondary'),
      lightThemeValues.box.default.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.selected.text.color.secondary'),
      lightThemeValues.box.selected.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.unselected.text.color.secondary'),
      lightThemeValues.box.unselected.text.color.secondary
    );
  };

  const setDarkMode = () => {
    form.setValue(getAttributeName('background.color'), darkThemeValues.background.color);

    form.setValue(getAttributeName('form.background.color'), darkThemeValues.form.background.color);
    form.setValue(getAttributeName('text.color.primary'), darkThemeValues.text.color.primary);
    form.setValue(getAttributeName('text.color.secondary'), darkThemeValues.text.color.secondary);
    form.setValue(getAttributeName('text.color.active'), darkThemeValues.text.color.active);

    form.setValue(getAttributeName('icon.color'), darkThemeValues.icon.color);

    form.setValue(
      getAttributeName('paymentOptions.button.unselected.text.color'),
      darkThemeValues.paymentOptions.button.unselected.text.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.unselected.background.color'),
      darkThemeValues.paymentOptions.button.unselected.background.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.unselected.icon.color'),
      darkThemeValues.paymentOptions.button.unselected.icon.color
    );

    form.setValue(
      getAttributeName('paymentOptions.button.selected.text.color'),
      darkThemeValues.paymentOptions.button.selected.text.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.selected.background.color'),
      darkThemeValues.paymentOptions.button.selected.background.color
    );
    form.setValue(
      getAttributeName('paymentOptions.button.selected.icon.color'),
      darkThemeValues.paymentOptions.button.selected.icon.color
    );

    form.setValue(getAttributeName('payButton.text.color'), darkThemeValues.payButton.text.color);
    form.setValue(getAttributeName('payButton.color'), darkThemeValues.payButton.color);

    form.setValue(
      getAttributeName('box.default.background.color'),
      darkThemeValues.box.default.background.color
    );
    form.setValue(
      getAttributeName('box.selected.background.color'),
      darkThemeValues.box.selected.background.color
    );
    form.setValue(
      getAttributeName('box.unselected.background.color'),
      darkThemeValues.box.unselected.background.color
    );

    form.setValue(
      getAttributeName('box.default.header.background.color'),
      darkThemeValues.box.default.header.background.color
    );
    form.setValue(
      getAttributeName('box.selected.header.background.color'),
      darkThemeValues.box.selected.header.background.color
    );
    form.setValue(
      getAttributeName('box.unselected.header.background.color'),
      darkThemeValues.box.unselected.header.background.color
    );

    form.setValue(
      getAttributeName('box.default.header.text.color.primary'),
      darkThemeValues.box.default.header.text.color.primary
    );
    form.setValue(
      getAttributeName('box.selected.header.text.color.primary'),
      darkThemeValues.box.selected.header.text.color.primary
    );
    form.setValue(
      getAttributeName('box.unselected.header.text.color.primary'),
      darkThemeValues.box.unselected.header.text.color.primary
    );

    form.setValue(
      getAttributeName('box.default.header.text.color.secondary'),
      darkThemeValues.box.default.header.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.selected.header.text.color.secondary'),
      darkThemeValues.box.selected.header.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.unselected.header.text.color.secondary'),
      darkThemeValues.box.unselected.header.text.color.secondary
    );

    form.setValue(
      getAttributeName('box.default.text.color.primary'),
      darkThemeValues.box.default.text.color.primary
    );
    form.setValue(
      getAttributeName('box.selected.text.color.primary'),
      darkThemeValues.box.selected.text.color.primary
    );
    form.setValue(
      getAttributeName('box.unselected.text.color.primary'),
      darkThemeValues.box.unselected.text.color.primary
    );

    form.setValue(
      getAttributeName('box.default.text.color.secondary'),
      darkThemeValues.box.default.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.selected.text.color.secondary'),
      darkThemeValues.box.selected.text.color.secondary
    );
    form.setValue(
      getAttributeName('box.unselected.text.color.secondary'),
      darkThemeValues.box.unselected.text.color.secondary
    );
  };

  const onChangeThemeMode = (event) => {
    const { value } = event.target;

    if (value === 'light') {
      setLightMode();
    }

    if (value === 'dark') {
      setDarkMode();
    }
  };

  return (
    <Stack gap={2} key={`settings-${device}`}>
      <RHFSelect
        onChange={onChangeThemeMode}
        name="themeMode"
        size="small"
        label="Tema"
        defaultValue="custom"
      >
        {ThemeModesOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </RHFSelect>
      <RHFSelect
        name={getAttributeName('font.family')}
        size="small"
        label="Fonte"
        defaultValue="Roboto"
      >
        {CheckoutBuilderFontFamilies.map((fontFamily) => (
          <MenuItem key={fontFamily} value={fontFamily}>
            {fontFamily}
          </MenuItem>
        ))}
      </RHFSelect>

      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue={CheckoutBuilderComponentTypes.checkout.config.settings.text.color.primary}
          name={getAttributeName('text.color.primary')}
          label="Cor primária do texto"
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue={CheckoutBuilderComponentTypes.checkout.config.settings.text.color.secondary}
          name={getAttributeName('text.color.secondary')}
          label="Cor secundária do texto"
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue={CheckoutBuilderComponentTypes.checkout.config.settings.text.color.active}
          name={getAttributeName('text.color.active')}
          label="Cor ativa do texto"
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue={CheckoutBuilderComponentTypes.checkout.config.settings.icon.color}
          name={getAttributeName('icon.color')}
          label="Cor dos ícones"
        />
      </Paper>
      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue="#ffffff"
          name={getAttributeName('background.color')}
          label="Cor de fundo"
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          defaultValue={
            CheckoutBuilderComponentTypes.checkout.config.settings.form.background.color
          }
          name={getAttributeName('form.background.color')}
          label="Cor de fundo do formulário de pagamento"
        />
      </Paper>

      <Stack gap={1}>
        <Typography variant="caption" color="text.secondary">
          text{' '}
        </Typography>
        <Card variant="outlined" style={{ padding: '10px 5px' }}>
          <Stack gap={1}>
            <Typography variant="caption" color="text.secondary">
              Botões não selecionados
            </Typography>

            <Card variant="outlined" style={{ padding: '10px 5px' }}>
              <Stack gap={2}>
                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .unselected.text.color
                    }
                    name={getAttributeName('paymentOptions.button.unselected.text.color')}
                    label="Cor do texto"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .unselected.background.color
                    }
                    name={getAttributeName('paymentOptions.button.unselected.background.color')}
                    label="Cor do fundo"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .unselected.icon.color
                    }
                    name={getAttributeName('paymentOptions.button.unselected.icon.color')}
                    label="Cor dos ícones"
                  />
                </Paper>
              </Stack>
            </Card>
          </Stack>

          <Stack gap={1} mt={2}>
            <Typography variant="caption" color="text.secondary">
              Botão selecionado
            </Typography>

            <Card variant="outlined" style={{ padding: '10px 5px' }}>
              <Stack gap={2}>
                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .selected.text.color
                    }
                    name={getAttributeName('paymentOptions.button.selected.text.color')}
                    label="Cor do texto"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .selected.background.color
                    }
                    name={getAttributeName('paymentOptions.button.selected.background.color')}
                    label="Cor do fundo"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button
                        .selected.icon.color
                    }
                    name={getAttributeName('paymentOptions.button.selected.icon.color')}
                    label="Cor do ícone"
                  />
                </Paper>
              </Stack>
            </Card>
          </Stack>
        </Card>
      </Stack>

      <Stack gap={1}>
        <Typography variant="caption" color="text.secondary">
          Caixas de conteúdo
        </Typography>
        <Card variant="outlined" style={{ padding: '10px 5px' }}>
          <Stack gap={1}>
            <Typography variant="caption" color="text.secondary">
              Caixas padrões
            </Typography>

            <Card variant="outlined" style={{ padding: '10px 5px' }}>
              <Stack gap={2}>
                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.header
                        .background.color
                    }
                    name={getAttributeName('box.default.header.background.color')}
                    label="Cor de fundo do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.header.text
                        .color.primary
                    }
                    name={getAttributeName('.box.default.header.text.color.primary')}
                    label="Cor primária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.header.text
                        .color.secondary
                    }
                    name={getAttributeName('box.default.header.text.color.secondary')}
                    label="Cor secundária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.background
                        .color
                    }
                    name={getAttributeName('box.default.background.color')}
                    label="Cor de fundo ca caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.text.color
                        .primary
                    }
                    name={getAttributeName('box.default.text.color.primary')}
                    label="Cor primária do texto da caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.default.text.color
                        .secondary
                    }
                    name={getAttributeName('box.default.text.color.secondary')}
                    label="Cor secundária do texto da caixa"
                  />
                </Paper>
              </Stack>
            </Card>
          </Stack>

          <Stack gap={1}>
            <Typography variant="caption" color="text.secondary">
              Caixas não selecionadas
            </Typography>

            <Card variant="outlined" style={{ padding: '10px 5px' }}>
              <Stack gap={2}>
                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected.header
                        .background.color
                    }
                    name={getAttributeName('box.unselected.header.background.color')}
                    label="Cor de fundo do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected.header
                        .text.color.primary
                    }
                    name={getAttributeName('box.unselected.header.text.color.primary')}
                    label="Cor primária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected.header
                        .text.color.secondary
                    }
                    name={getAttributeName('box.unselected.header.text.color.secondary')}
                    label="Cor secundária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected
                        .background.color
                    }
                    name={getAttributeName('box.unselected.background.color')}
                    label="Cor de fundo ca caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected.text
                        .color.primary
                    }
                    name={getAttributeName('box.unselected.text.color.primary')}
                    label="Cor primária do texto da caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.unselected.text
                        .color.secondary
                    }
                    name={getAttributeName('box.unselected.text.color.secondary')}
                    label="Cor secundária do texto da caixa"
                  />
                </Paper>
              </Stack>
            </Card>
          </Stack>

          <Stack gap={1}>
            <Typography variant="caption" color="text.secondary">
              Caixas selecionadas
            </Typography>

            <Card variant="outlined" style={{ padding: '10px 5px' }}>
              <Stack gap={2}>
                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.header
                        .background.color
                    }
                    name={getAttributeName('box.selected.header.background.color')}
                    label="Cor de fundo do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.header
                        .text.color.primary
                    }
                    name={getAttributeName('box.selected.header.text.color.primary')}
                    label="Cor primária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.header
                        .text.color.secondary
                    }
                    name={getAttributeName('box.selected.header.text.color.secondary')}
                    label="Cor secundária do texto do cabeçalho"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.background
                        .color
                    }
                    name={getAttributeName('box.selected.background.color')}
                    label="Cor de fundo ca caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.text.color
                        .primary
                    }
                    name={getAttributeName('box.selected.text.color.primary')}
                    label="Cor primária do texto da caixa"
                  />
                </Paper>

                <Paper variant="outlined">
                  <RHFColorPicker
                    defaultValue={
                      CheckoutBuilderComponentTypes.checkout.config.settings.box.selected.text.color
                        .secondary
                    }
                    name={getAttributeName('box.selected.text.color.secondary')}
                    label="Cor secundária do texto da caixa"
                  />
                </Paper>
              </Stack>
            </Card>
          </Stack>
        </Card>
      </Stack>

      <Stack gap={1}>
        <Typography variant="caption" color="text.secondary">
          Botão do pagamento
        </Typography>

        <Card variant="outlined" style={{ padding: '10px 5px' }}>
          <Stack gap={2}>
            <Paper variant="outlined">
              <RHFColorPicker
                defaultValue={
                  CheckoutBuilderComponentTypes.checkout.config.settings.payButton.text.color
                }
                name={getAttributeName('payButton.text.color')}
                label="Cor do texto botão de pagar"
              />
            </Paper>

            <Paper variant="outlined">
              <RHFColorPicker
                defaultValue={
                  CheckoutBuilderComponentTypes.checkout.config.settings.payButton.color
                }
                name={getAttributeName('payButton.color')}
                label="Cor do botão de pagar"
              />
            </Paper>
          </Stack>
        </Card>
      </Stack>

      <RHFUpload
        name={getAttributeName('background.image')}
        maxSize={10000000}
        onDrop={([file]) => {
          form.setValue(
            getAttributeName('background.image'),
            {
              file,
              preview: URL.createObjectURL(file),
            },
            { shouldValidate: true, shouldDirty: true }
          );
        }}
        onRemove={() => {
          form.setValue(getAttributeName('background.image'), null);
        }}
        helperText="Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB"
        accept={{ 'image/jpeg': [], 'image/png': [] }}
      />
      <RHFSwitch
        name={getAttributeName('background.fixed')}
        label="Imagem de fundo fixa"
        defaultValue={false}
      />
      <RHFSwitch
        name={getAttributeName('background.repeat')}
        label="Repetir imagem de fundo"
        defaultValue={false}
      />
      <RHFSwitch
        name={getAttributeName('background.cover')}
        label="Expandir imagem de fundo"
        defaultValue={false}
      />
    </Stack>
  );
};

CheckoutBuilderSettingsForm.propTypes = {
  device: PropTypes.oneOf(['desktop', 'mobile']).isRequired,
};

export default CheckoutBuilderSettingsForm;
