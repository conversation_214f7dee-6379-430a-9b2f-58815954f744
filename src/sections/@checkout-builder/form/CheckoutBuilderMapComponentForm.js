import { InputAdornment, Paper, Stack, ToggleButton, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import RHFToggleButtonGroup from '@/components/hook-form/RHFToggleButtonGroup';
import { RHFTextField } from '@/components/hook-form';
import Iconify from '@/components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderMapAlignments = {
  left: {
    id: 'left',
    label: '<PERSON><PERSON><PERSON>',
    icon: 'mdi:format-align-left',
  },
  center: {
    id: 'center',
    label: 'Centro',
    icon: 'mdi:format-align-center',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

const CheckoutBuilderMapComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  return (
    <Stack gap={2}>
      <RHFTextField
        name={getAttributeName('address')}
        label="Endereço"
        size="small"
        placeholder="..."
        defaultValue={CheckoutBuilderComponentTypes.map.attributes.address}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="mdi:map-marker" />
            </InputAdornment>
          ),
        }}
      />

      <Stack component={Paper} p={1} gap={1} variant="outlined">
        <Typography variant="caption" color="text.secondary">
          Alinhamento
        </Typography>
        <RHFToggleButtonGroup
          name={getAttributeName('alignment')}
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.map.attributes.alignment}
          exclusive
          color="primary"
          sx={{
            border: 'none',
          }}
        >
          {Object.values(CheckoutBuilderMapAlignments).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Stack>
    </Stack>
  );
};

CheckoutBuilderMapComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderMapComponentForm;
