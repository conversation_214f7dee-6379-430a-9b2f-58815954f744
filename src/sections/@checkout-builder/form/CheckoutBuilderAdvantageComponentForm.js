import { Box, MenuItem, Paper, ToggleButton } from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { ReactComponent as AtSignIcon } from '../../../assets/checkout/atsign.svg';
import { ReactComponent as ChartIcon } from '../../../assets/checkout/chart.svg';
import { ReactComponent as ChatIcon } from '../../../assets/checkout/chat.svg';
import { ReactComponent as ClickIcon } from '../../../assets/checkout/click.svg';
import { ReactComponent as CloudIcon } from '../../../assets/checkout/cloud.svg';
import { ReactComponent as DownloadIcon } from '../../../assets/checkout/download.svg';
import { ReactComponent as FileIcon } from '../../../assets/checkout/file.svg';
import { ReactComponent as HeartIcon } from '../../../assets/checkout/heart.svg';
import { ReactComponent as PeopleIcon } from '../../../assets/checkout/people.svg';
import { ReactComponent as PlayIcon } from '../../../assets/checkout/play.svg';
import { ReactComponent as VerifiedIcon } from '../../../assets/checkout/verified.svg';
import { ReactComponent as WebIcon } from '../../../assets/checkout/web.svg';
import { RHFSelect, RHFSwitch } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const icons = {
  atsign: {
    id: 'atsign',
    Icon: AtSignIcon,
  },
  chart: {
    id: 'chart',
    Icon: ChartIcon,
  },
  chat: {
    id: 'chat',
    Icon: ChatIcon,
  },
  click: {
    id: 'click',
    Icon: ClickIcon,
  },
  cloud: {
    id: 'cloud',
    Icon: CloudIcon,
  },
  download: {
    id: 'download',
    Icon: DownloadIcon,
  },
  file: {
    id: 'file',
    Icon: FileIcon,
  },
  heart: {
    id: 'heart',
    Icon: HeartIcon,
  },
  people: {
    id: 'people',
    Icon: PeopleIcon,
  },
  play: {
    id: 'play',
    Icon: PlayIcon,
  },
  verified: {
    id: 'verified',
    Icon: VerifiedIcon,
  },
  web: {
    id: 'web',
    Icon: WebIcon,
  },
};

export const CheckoutBuilderAdvantageSizes = {
  original: {
    id: 'original',
    name: 'Original',
    width: '100%',
  },
  small: {
    id: 'small',
    name: 'Pequeno',
    width: '50%',
  },
  medium: {
    id: 'medium',
    name: 'Médio',
    width: '65%',
  },
  large: {
    id: 'large',
    name: 'Grande',
    width: '83%',
  },
};

const CheckoutBuilderAdvantageComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  return (
    <Stack spacing={2}>
      <Box p={1}>
        <RHFToggleButtonGroup
          name={getAttributeName('icon')}
          label="Icon"
          defaultValue={0}
          exclusive
          orientation="vertical"
          color="primary"
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gridTemplateRows: 'repeat(4, 1fr)',
            border: 'none',
            placeItems: 'center',
            alignContent: 'center',
            boxSizing: 'border-box',
            columnGap: 1,
          }}
        >
          {Object.values(icons).map(({ id, Icon }) => (
            <ToggleButton
              key={id}
              value={id}
              size="small"
              sx={{
                overflow: 'hidden',
                outlineWidth: '1px',
                outlineStyle: 'solid',
                m: 0,
              }}
            >
              <Icon width={32} height={32} fill="currentColor" />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Box>

      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('primaryColor')}
          label="Cor principal"
          defaultValue={CheckoutBuilderComponentTypes.advantage.attributes.primaryColor}
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('titleTextColor')}
          label="Cor do título"
          defaultValue={CheckoutBuilderComponentTypes.advantage.attributes.titleTextColor}
        />
      </Paper>

      <RHFSelect
        name={getAttributeName('size')}
        label="Tamanho"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.advantage.attributes.size}
      >
        {Object.values(CheckoutBuilderAdvantageSizes).map((size) => (
          <MenuItem key={size.id} value={size.id}>
            {size.name}
          </MenuItem>
        ))}
      </RHFSelect>

      <RHFSwitch
        name={getAttributeName('darkMode')}
        label="Modo escuro"
        defaultValue={CheckoutBuilderComponentTypes.advantage.attributes.darkMode}
      />

      <RHFSwitch
        name={getAttributeName('vertical')}
        label="Modo Vertical"
        defaultValue={CheckoutBuilderComponentTypes.advantage.attributes.vertical}
      />
    </Stack>
  );
};

CheckoutBuilderAdvantageComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderAdvantageComponentForm;
