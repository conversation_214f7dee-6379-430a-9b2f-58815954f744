import PropTypes from 'prop-types';
import {
  Box,
  Divider,
  Drawer,
  Icon<PERSON>utton,
  LinearProgress,
  Toolbar,
  Typography,
  Fab,
} from '@mui/material';
import { Stack } from '@mui/system';
import { useContext } from 'react';
import { v4 } from 'uuid';
import { useFormContext, useWatch, useFieldArray } from 'react-hook-form';
import ConfirmationIconButton from '@/components/buttons/ConfirmationIconButton';
import Iconify from '@/components/iconify';
import Scrollbar from '@/components/scrollbar/Scrollbar';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { SelectedElementContext } from '@/contexts/SelectedElementContext';
import { FormScopes } from '@/utils/form';
import useResponsive from '@/hooks/useResponsive';
import {
  CHECKOUT_BUILDER_DRAWER_WIDTH,
  CheckoutBuilderComponentTypes,
  CheckoutBuilderDeviceTypes,
} from '../constants';
import useDragAndDropRows from '../hooks/useDragAndDropRowsHook';

const CheckoutBuilderFormDrawer = ({ children }) => {
  const { scope, setScope } = useContext(CheckoutBuilderContext);

  const { selected, setSelectedID } = useContext(SelectedElementContext);

  const onClose = () => {
    setScope(FormScopes.INDEX);
    setSelectedID(null);
  };

  const form = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const map = {
    [CheckoutBuilderDeviceTypes.desktop.id]: useFieldArray({
      name: `desktop.rows`,
      keyName: 'key',
    }),
    [CheckoutBuilderDeviceTypes.mobile.id]: useFieldArray({
      name: `mobile.rows`,
      keyName: 'key',
    }),
  };

  const rows = map[device] || null;

  const { insertComponent, removeComponent } = useDragAndDropRows();

  const isMobile = useResponsive('down', 'sm');

  const desktopRows = form.getValues('desktop.rows');
  const lastDesktopRowId = desktopRows[desktopRows.length - 1]?.id;

  const mobileRows = form.getValues('mobile.rows');
  const lastMobileRowId = mobileRows[mobileRows.length - 1]?.id;

  const lastRowId = device === 'desktop' ? lastDesktopRowId : lastMobileRowId;

  const selectedId = selected.id;

  return (
    <Drawer
      id="checkout-builder-form-drawer"
      variant="persistent"
      anchor="right"
      open={
        isMobile
          ? scope === FormScopes.OPEN_SETTINGS
          : scope === FormScopes.CREATE || scope === FormScopes.EDIT
      }
      onClose={onClose}
      hideBackdrop
      sx={{
        width: CHECKOUT_BUILDER_DRAWER_WIDTH,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: {
          width: CHECKOUT_BUILDER_DRAWER_WIDTH,
          boxSizing: 'border-box',
          overflow: 'hidden',
        },
      }}
    >
      <Toolbar />
      <Box sx={{ p: 1 }}>
        <Stack direction="row" spacing={2} p={1} mt={isMobile ? 10 : 1}>
          <Typography variant="h6" sx={{ flex: 1 }}>
            {CheckoutBuilderComponentTypes[selected?.type]?.name || ''}
          </Typography>
          <Stack direction="row" spacing={1}>
            {!CheckoutBuilderComponentTypes[selected?.type]?.extra && lastRowId !== selectedId && (
              <>
                <ConfirmationIconButton
                  color="error"
                  size="small"
                  onConfirm={() => {
                    if (selected?.type === 'row') {
                      const rowId = selected?.id || 0;

                      const valuesToUpdate = rows.fields.filter(({ id }) => id !== rowId);
                      form.setValue(`${device}.rows`, valuesToUpdate);
                    } else {
                      removeComponent({
                        from: {
                          rowIndex: selected?.index?.row,
                          columnIndex: selected?.index?.column,
                          componentIndex: selected?.index?.component,
                        },
                      });
                    }

                    onClose();
                  }}
                >
                  <Iconify icon="ion:trash-outline" />
                </ConfirmationIconButton>
                <Fab
                  color="inherit"
                  size="small"
                  onClick={() => {
                    if (selected.type === 'row') {
                      const rowIndex = selected?.index?.row || 0;
                      const copied = form.getValues(`${device}.rows.${rowIndex}`);
                      const row = form.getValues(`${device}.rows[${selected?.index?.row}]`);

                      const valuesToUpdate = rows.fields.reduce((acc, item, index) => {
                        acc.push(item);
                        if (index === rowIndex)
                          acc.push({
                            ...copied,
                            id: v4(),
                            type: 'row',
                            columns: row.columns.map((column) => ({
                              ...column,
                              id: v4(),
                              type: 'column',
                              components: column.components.map((component) => ({
                                ...component,
                                id: v4(),
                              })),
                            })),
                          });
                        return acc;
                      }, []);

                      form.setValue(`${device}.rows`, valuesToUpdate);
                    } else {
                      const copied = form.getValues(
                        `${device}.rows[${selected?.index?.row}].columns[${selected?.index?.column}].components[${selected?.index?.component}]`
                      );
                      insertComponent({
                        to: {
                          rowIndex: selected?.index?.row,
                          columnIndex: selected?.index?.column,
                          componentIndex: selected?.index?.component,
                        },
                        payload: {
                          ...copied,
                          id: v4(),
                        },
                      });
                    }

                    onClose();
                  }}
                >
                  <Iconify icon="ion:copy-outline" />
                </Fab>
              </>
            )}
            <IconButton color="inherit" size="small" onClick={onClose}>
              <Iconify icon="ion:close" />
            </IconButton>
          </Stack>
        </Stack>
        <Divider sx={{ my: 1 }} />
        <Scrollbar
          sx={{
            height: isMobile ? 'calc(100svh - 30%)' : 'calc(100svh - 164px)',
            pr: 0.5,
            py: 1,
            pb: isMobile ? 10 : undefined,
          }}
        >
          {selected?.id ? children : <LinearProgress />}
        </Scrollbar>
      </Box>
    </Drawer>
  );
};

CheckoutBuilderFormDrawer.propTypes = {
  children: PropTypes.node.isRequired,
};

export default CheckoutBuilderFormDrawer;
