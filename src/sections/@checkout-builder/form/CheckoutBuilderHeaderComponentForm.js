import { MenuItem, Paper, Stack, ToggleButton, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import { useFormContext, useWatch } from 'react-hook-form';
import { RHFSelect, RHFSwitch, RHFTextField, RHFUpload } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderHeaderBackgroundTypes = {
  image: {
    id: 'image',
    label: 'Imagem',
    icon: 'mdi:image',
  },
  color: {
    id: 'color',
    label: 'Cor',
    icon: 'mdi:palette',
  },
  transparent: {
    id: 'transparent',
    label: 'Transparente',
    icon: 'carbon:opacity',
  },
};

export const CheckoutBuilderHeaderProductImageTypes = {
  same: {
    id: 'same',
    label: 'Mesma imagem do produto',
  },
  custom: {
    id: 'custom',
    label: 'Fazer upload de uma imagem',
  },
  none: {
    id: 'none',
    label: 'Nenhuma imagem',
  },
};

export const CheckoutBuilderHeaderProductImageAlignments = {
  left: {
    id: 'left',
    label: 'Esquerda',
    icon: 'mdi:format-align-left',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

const CheckoutBuilderHeaderComponentForm = ({ selected: { index } }) => {
  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [backgroundType, productImageType, showSubtitle] = useWatch({
    name: [
      getAttributeName('backgroundType'),
      getAttributeName('productImageType'),
      getAttributeName('showSubtitle'),
    ],
  });

  return (
    <Stack gap={2}>
      <Stack mt={1}>
        <Typography variant="caption" fontWeight={100} color="text.secondary">
          Background
        </Typography>
        <Stack gap={1} mt={1.5}>
          <RHFToggleButtonGroup
            name={getAttributeName('backgroundType')}
            label="Tipo de fundo"
            size="small"
            defaultValue={CheckoutBuilderComponentTypes.header.attributes.backgroundType}
            exclusive
            color="primary"
          >
            {Object.values(CheckoutBuilderHeaderBackgroundTypes).map((type) => (
              <ToggleButton
                key={type.id}
                value={type.id}
                sx={{
                  width: 1,
                }}
              >
                <Iconify icon={type.icon} />
              </ToggleButton>
            ))}
          </RHFToggleButtonGroup>
          {backgroundType === CheckoutBuilderHeaderBackgroundTypes.image.id && (
            <RHFUpload
              name={getAttributeName('backgroundImage')}
              maxSize={10000000}
              onDrop={([file]) => {
                form.setValue(
                  getAttributeName('backgroundImage'),
                  Object.assign(file, {
                    preview: URL.createObjectURL(file),
                  }),
                  { shouldValidate: true, shouldDirty: true }
                );
              }}
              onRemove={() =>
                form.setValue(getAttributeName('backgroundImage'), null, {
                  shouldValidate: true,
                  shouldDirty: true,
                })
              }
              helperText="Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB"
              accept={{ 'image/jpeg': [], 'image/png': [] }}
            />
          )}
          {backgroundType === CheckoutBuilderHeaderBackgroundTypes.color.id && (
            <Paper variant="outlined">
              <RHFColorPicker
                name={getAttributeName('backgroundColor')}
                label="Cor de fundo"
                defaultValue={CheckoutBuilderComponentTypes.header.attributes.backgroundColor}
              />
            </Paper>
          )}
        </Stack>
      </Stack>
      <RHFSelect
        name={getAttributeName('productImageType')}
        label="Imagem do produto"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.header.attributes.productImageType}
      >
        {Object.values(CheckoutBuilderHeaderProductImageTypes).map((type) => (
          <MenuItem key={type.id} value={type.id}>
            {type.label}
          </MenuItem>
        ))}
      </RHFSelect>
      {productImageType === CheckoutBuilderHeaderProductImageTypes.custom.id && (
        <RHFUpload
          name={getAttributeName('productImage')}
          maxSize={10000000}
          onDrop={([file]) => {
            form.setValue(
              getAttributeName('productImage'),
              Object.assign(file, {
                preview: URL.createObjectURL(file),
              }),
              { shouldValidate: true, shouldDirty: true }
            );
          }}
          onRemove={() =>
            form.setValue(getAttributeName('productImage'), null, {
              shouldValidate: true,
              shouldDirty: true,
            })
          }
          multiple={false}
          helperText="Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB"
          accept={{ 'image/jpeg': [], 'image/png': [] }}
        />
      )}
      {[
        CheckoutBuilderHeaderProductImageTypes.same.id,
        CheckoutBuilderHeaderProductImageTypes.custom.id,
      ].includes(productImageType) && (
        <RHFToggleButtonGroup
          name={getAttributeName('productImageAlignment')}
          label="Alinhamento da imagem do produto"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.header.attributes.productImageAlignment}
          exclusive
          color="primary"
        >
          {Object.values(CheckoutBuilderHeaderProductImageAlignments).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      )}
      <Paper sx={{ p: 1 }} variant="outlined">
        <Typography variant="caption" fontWeight={100} color="text.secondary">
          Título
        </Typography>
        <Stack mt={2} gap={1}>
          <RHFTextField
            name={getAttributeName('titleFontSize')}
            label="Tamanho"
            size="small"
            type="number"
            defaultValue={CheckoutBuilderComponentTypes.header.attributes.titleFontSize}
          />
          <Paper variant="outlined">
            <RHFColorPicker
              name={getAttributeName('titleTextColor')}
              label="Cor"
              defaultValue={CheckoutBuilderComponentTypes.header.attributes.titleTextColor}
            />
          </Paper>
        </Stack>
      </Paper>
      <RHFSwitch
        name={getAttributeName('showSubtitle')}
        label="Mostrar subtítulo"
        defaultValue={CheckoutBuilderComponentTypes.header.attributes.showSubtitle}
      />
      {showSubtitle && (
        <Paper sx={{ p: 1 }} variant="outlined">
          <Typography variant="caption" fontWeight={100} color="text.secondary">
            Subtítulo
          </Typography>
          <Stack mt={2} gap={1}>
            <RHFTextField
              name={getAttributeName('subtitleFontSize')}
              label="Tamanho"
              size="small"
              type="number"
              defaultValue={CheckoutBuilderComponentTypes.header.attributes.subtitleFontSize}
            />
            <Paper variant="outlined">
              <RHFColorPicker
                name={getAttributeName('subtitleTextColor')}
                label="Cor"
                defaultValue={CheckoutBuilderComponentTypes.header.attributes.subtitleTextColor}
              />
            </Paper>
          </Stack>
        </Paper>
      )}
    </Stack>
  );
};

CheckoutBuilderHeaderComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderHeaderComponentForm;
