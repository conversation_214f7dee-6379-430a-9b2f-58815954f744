import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Dialog } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useFormContext, useWatch } from 'react-hook-form';
import { useContext, useState } from 'react';
import { RHFSelect, RHFTextField, RHFUpload } from '@/components/hook-form';
import RHFColorPicker from '@/components/hook-form/RHFColorPicker';
import Iconify from '@/components/iconify';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import CheckoutBuilderExitPopUpComponent from '@/sections/@checkout-builder/components/CheckoutBuilderExitPopUpComponent';
import { fCurrency } from '@/utils/formatNumber';
import { getCuponsService } from '@/services/coupons';
import useResponsive from '@/hooks/useResponsive';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderExitPopUpTypes = {
  image: {
    id: 'image',
    label: 'Imagem',
    icon: 'mdi:image',
  },
  imageAndText: {
    id: 'imageAndText',
    label: 'Imagem e texto',
    icon: 'mdi:image-text',
  },
  video: {
    id: 'video',
    label: 'Vídeo',
    icon: 'mdi:video',
  },
};

export const CheckoutBuilderExitPopUpActionOnClick = {
  offer: {
    id: 'offer',
    label: 'Redirecionar para Oferta',
  },
  discount: {
    id: 'discount',
    label: 'Aplicar Cupom de desconto',
  },
  redirect: {
    id: 'redirect',
    label: 'Redirecionar para URL',
  },
  close: {
    id: 'close',
    label: 'Apenas fechar',
  },
};

const CheckoutBuilderExitPopUpComponentForm = () => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const form = useFormContext();

  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const [type, actionOnClick] = useWatch({
    name: [getAttributeName('exitPopup', 'type'), getAttributeName('exitPopup', 'actionOnClick')],
  });

  const {
    checkout: { offers, product },
  } = useContext(CheckoutBuilderContext);

  const { data: coupons } = useQuery({
    queryKey: ['checkout-coupons'],
    queryFn: () => getCuponsService({ limit: 100 }),
    select(data) {
      return data.results.filter(({ products }) => !!products?.find(({ id }) => id === product.id));
    },
  });

  const isMobile = useResponsive('down', 'sm');

  const onOpenPreview = () => {
    setIsPreviewOpen(true);
  };

  const onClosePreview = () => {
    setIsPreviewOpen(false);
  };

  return (
    <Stack gap={2}>
      <RHFSelect
        name={getAttributeName('exitPopup', 'type')}
        label="Tipo"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.exitPopup.attributes.type}
      >
        {Object.values(CheckoutBuilderExitPopUpTypes).map(({ id, label }) => (
          <MenuItem key={id} value={id}>
            {label}
          </MenuItem>
        ))}
      </RHFSelect>
      <RHFSelect
        name={getAttributeName('exitPopup', 'actionOnClick')}
        label="Ação ao clicar no botão"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.exitPopup.attributes.actionOnClick}
      >
        {Object.values(CheckoutBuilderExitPopUpActionOnClick).map(({ id, label }) => (
          <MenuItem key={id} value={id}>
            {label}
          </MenuItem>
        ))}
      </RHFSelect>
      {actionOnClick === CheckoutBuilderExitPopUpActionOnClick.offer.id && (
        <RHFSelect
          name={getAttributeName('exitPopup', 'offer')}
          label="Selecione a oferta"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.exitPopup.attributes.offer}
        >
          {offers?.map(({ id, name, price }) => (
            <MenuItem key={id} value={id}>
              <Stack direction="row" alignItems="center" gap={1}>
                <Typography color="success.main" fontWeight="bold">
                  {fCurrency(price || 0)}
                </Typography>
                <span>{name}</span>
              </Stack>
            </MenuItem>
          ))}
        </RHFSelect>
      )}
      {actionOnClick === CheckoutBuilderExitPopUpActionOnClick.discount.id && (
        <RHFSelect
          name={getAttributeName('exitPopup', 'coupon')}
          label="Selecione o cupom de desconto"
          size="small"
          defaultValue={coupons?.length && coupons[0]?.code}
        >
          {coupons?.map(({ id, code }) => (
            <MenuItem key={id} value={code}>
              <Stack direction="row" alignItems="center" gap={1}>
                <Typography color="success.main" fontWeight="bold">
                  {code}
                </Typography>
              </Stack>
            </MenuItem>
          ))}
        </RHFSelect>
      )}
      {actionOnClick === CheckoutBuilderExitPopUpActionOnClick.redirect.id && (
        <RHFTextField
          name={getAttributeName('exitPopup', 'url')}
          size="small"
          label="URL de redirecionamento"
          placeholder="https://"
          InputLabelProps={{ shrink: true }}
        />
      )}
      {[
        CheckoutBuilderExitPopUpTypes.imageAndText.id,
        CheckoutBuilderExitPopUpTypes.video.id,
      ].includes(type) && (
        <RHFTextField
          name={getAttributeName('exitPopup', 'title')}
          label="Título"
          size="small"
        />
      )}
      {type === CheckoutBuilderExitPopUpTypes.imageAndText.id && (
        <RHFTextField
          name={getAttributeName('exitPopup', 'description')}
          size="small"
          multiline
          rows={4}
        />
      )}
      {type === CheckoutBuilderExitPopUpTypes.video.id && (
        <RHFTextField
          name={getAttributeName('exitPopup', 'video')}
          size="small"
          label="URL do vídeo"
          placeholder="https://www.youtube.com/watch?v=..."
          InputLabelProps={{ shrink: true }}
        />
      )}
      <RHFTextField
        name={getAttributeName('exitPopup', 'actionLabel')}
        label="Texto do botão"
        size="small"
      />
      <RHFColorPicker
        name={getAttributeName('exitPopup', 'backgroundButtonColor')}
        label="Cor de botão"
      />

      <RHFColorPicker
        name={getAttributeName('exitPopup', 'textButtonColor')}
        label="Cor de texto botão"
      />
      {[
        CheckoutBuilderExitPopUpTypes.image.id,
        CheckoutBuilderExitPopUpTypes.imageAndText.id,
      ].includes(type) && (
        <RHFUpload
          name={getAttributeName('exitPopup', 'image')}
          maxSize={10000000}
          onDrop={([file]) => {
            form.setValue(
              getAttributeName('exitPopup', 'image'),
              Object.assign(file, {
                preview: URL.createObjectURL(file),
              }),
              { shouldValidate: true, shouldDirty: true }
            );
          }}
          onRemove={() =>
            form.setValue(getAttributeName('exitPopup', 'image'), null, {
              shouldValidate: true,
              shouldDirty: true,
            })
          }
          multiple={false}
          helperText="Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB"
          accept={{ 'image/jpeg': [], 'image/png': [] }}
        />
      )}
      {isMobile && (
        <>
          <Button
            variant="outlined"
            color="inherit"
            endIcon={<Iconify icon="mdi:eye" />}
            onClick={onOpenPreview}
          >
            Preview
          </Button>

          <Dialog fullWidth open={isPreviewOpen} onClose={onClosePreview} scroll="body">
            <CheckoutBuilderExitPopUpComponent onClose={onClosePreview} />
          </Dialog>
        </>
      )}
    </Stack>
  );
};

export default CheckoutBuilderExitPopUpComponentForm;
