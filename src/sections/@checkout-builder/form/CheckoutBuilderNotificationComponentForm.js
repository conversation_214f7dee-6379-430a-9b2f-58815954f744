import PropTypes from 'prop-types';
import { useState } from 'react';
import { Paper, Stack, Button, MenuItem } from '@mui/material';
import { useWatch } from 'react-hook-form';
import { AnimatePresence } from 'framer-motion';
import Iconify from '@/components/iconify';
import { Box } from '@mui/system';
import { RHFSelect, RHFSwitch, RHFTextField } from '@/components/hook-form';
import { NoficationCard } from '@/sections/@checkout-builder/components/CheckoutBuilderNotificationComponent';
import useResponsive from '@/hooks/useResponsive';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderNotificationTypes = {
  interestedLast24Hours: {
    id: 'interestedLast24Hours',
    label: 'Pessoas interessadas nas últimas 24 horas',
    icon: 'group',
    message: '{value} pessoas interessadas nesse produto nas últimas 24 horas',
  },
  interestedLastWeek: {
    id: 'interestedLastWeek',
    label: 'Pessoas interessadas na última semana',
    icon: 'growth',
    message: '{value} pessoas interessadas nesse produto na última semana',
  },
  interestedRightNow: {
    id: 'interestedRightNow',
    label: 'Pessoas interessadas agora (últimos 10 minutos)',
    icon: 'growth',
    message: '{value} pessoas interessadas nesse produto agora',
  },
  purchasedLast24Hours: {
    id: 'purchasedLast24Hours',
    label: 'Compras feitas nas últimas 24 horas',
    icon: 'check',
    message: '{value} compras desse produto nas últimas 24 horas',
  },
  purchasedLastWeek: {
    id: 'purchasedLastWeek',
    label: 'Compras feitas na última semana',
    icon: 'check',
    message: '{value} compras desse produto na última semana',
  },
};

const CheckoutBuilderNotificationComponentForm = () => {
  const [notificationPreview, setNotificationsPreview] = useState();

  const isMobile = useResponsive('down', 'sm');

  const {
    extra: { getAttributesName },
  } = useCheckoutBuilderFormHelper();

  const notification = useWatch({
    name: getAttributesName('notification'),
  });

  const enabledNotications = Object.values(CheckoutBuilderNotificationTypes).filter(
    ({ id }) => notification?.[id]?.enabled
  );

  const onOpenPreview = () => {
    setNotificationsPreview(enabledNotications);
  };

  const removePreviewNotification = (id) => {
    setNotificationsPreview((prev) => prev.filter((value) => value.id !== id));
  };

  return (
    <Stack gap={2} pl={1}>
      {Object.values(CheckoutBuilderNotificationTypes).map(({ id }) => (
        <NotificationField key={id} id={id} />
      ))}
      {isMobile && (
        <>
          <Button
            variant="outlined"
            color="inherit"
            endIcon={<Iconify icon="mdi:eye" />}
            onClick={onOpenPreview}
          >
            Preview
          </Button>

          {!!notificationPreview?.length && (
            <div
              style={{
                height: '100%',
                width: '100%',
                position: 'absolute',
                zIndex: 9999999999,
                top: 200,
                left: 0,
              }}
            >
              <Stack width={1} height={1} p={3} alignItems="flex-end">
                <Stack gap={2}>
                  <AnimatePresence>
                    {notificationPreview.map(({ id }) => (
                      <NoficationCard
                        key={id}
                        id={id}
                        onClose={() => removePreviewNotification(id)}
                      />
                    ))}
                  </AnimatePresence>
                </Stack>
              </Stack>
            </div>
          )}
        </>
      )}
    </Stack>
  );
};
const NotificationField = ({ id }) => {
  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const [enabled] = useWatch({
    name: [getAttributeName('notification', `${id}.enabled`)],
  });

  return (
    <Stack component={Paper} gap={2} variant="outlined" p={2}>
      <RHFSwitch
        name={getAttributeName('notification', `${id}.enabled`)}
        label={CheckoutBuilderNotificationTypes[id].label}
        size="small"
        defaultValue={enabled}
      />
      {enabled && (
        <Box display="flex" flexDirection="column" gap={2}>
          <Box display="flex" justifyContent="space-between" gap={1}>
            <RHFTextField
              type="number"
              name={getAttributeName('notification', `${id}.min`)}
              label="Valor mínimo"
              size="small"
              InputLabelProps={{
                shrink: true,
              }}
            />
            <RHFTextField
              type="number"
              name={getAttributeName('notification', `${id}.max`)}
              label="Valor máximo"
              size="small"
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Box>
          <Box>
            <RHFSelect
              name={getAttributeName('notification', `${id}.exibitionTime`)}
              label="Tempo de exibição (segundos)"
              size="small"
              InputLabelProps={{
                shrink: true,
              }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={15}>15</MenuItem>
              <MenuItem value={30}>30</MenuItem>
              <MenuItem value={45}>45</MenuItem>
              <MenuItem value={60}>60</MenuItem>
            </RHFSelect>
          </Box>
        </Box>
      )}
    </Stack>
  );
};

NotificationField.propTypes = {
  id: PropTypes.string.isRequired,
};

CheckoutBuilderNotificationComponentForm.propTypes = {};

export default CheckoutBuilderNotificationComponentForm;
