import { Grid, MenuItem, Paper } from '@mui/material';
import PropTypes from 'prop-types';
import { useWatch } from 'react-hook-form';
import { RHFSelect, RHFSwitch, RHFTextField } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderCoundownTypes = {
  time: {
    id: 'time',
    label: 'Tempo em minutos',
  },
  date: {
    id: 'date',
    label: 'Data final',
  },
};

const CheckoutBuilderCountdownComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [type] = useWatch({
    name: [getAttributeName('type')],
  });

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <RHFSelect
          name={getAttributeName('type')}
          label="Tipo"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.type}
        >
          {Object.values(CheckoutBuilderCoundownTypes).map(({ id, label }) => (
            <MenuItem key={id} value={id}>
              {label}
            </MenuItem>
          ))}
        </RHFSelect>
      </Grid>
      <Grid item xs={12}>
        {type === CheckoutBuilderCoundownTypes.time.id ? (
          <RHFTextField
            size="small"
            name={getAttributeName('time')}
            label="Minutos"
            type="time"
            defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.time}
          />
        ) : (
          <RHFTextField
            size="small"
            name={getAttributeName('date')}
            label="Data"
            type="date"
            defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.date}
          />
        )}
      </Grid>
      <Grid item xs={12} sm={6}>
        <Paper variant="outlined">
          <RHFColorPicker
            name={getAttributeName('backgroundColor')}
            label="Cor de fundo"
            defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.backgroundColor}
          />
        </Paper>
      </Grid>
      <Grid item xs={12} sm={6}>
        <Paper variant="outlined">
          <RHFColorPicker
            name={getAttributeName('textColor')}
            label="Cor do texto"
            defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.textColor}
          />
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <RHFTextField
          size="small"
          name={getAttributeName('activeText')}
          label="Texto contagem ativa"
          type="text"
          defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.activeText}
        />
      </Grid>
      <Grid item xs={12}>
        <RHFTextField
          size="small"
          name={getAttributeName('finishedText')}
          label="Texto contagem finalizada"
          type="text"
          defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.finishedText}
        />
      </Grid>
      <Grid item xs={12}>
        <RHFSwitch
          name={getAttributeName('fixedOnTop')}
          label="Fixar no topo"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.countdown.attributes.fixedOnTop}
        />
      </Grid>
    </Grid>
  );
};

CheckoutBuilderCountdownComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderCountdownComponentForm;
