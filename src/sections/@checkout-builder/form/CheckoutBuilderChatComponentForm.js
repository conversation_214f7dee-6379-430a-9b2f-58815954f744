import { useEffect, useState } from 'react';
import { MenuI<PERSON>, <PERSON>ack, Button } from '@mui/material';
import { useFormContext, useWatch } from 'react-hook-form';
import { AnimatePresence } from 'framer-motion';
import Iconify from '@/components/iconify';
import { RHFSelect, RHFTextField } from '@/components/hook-form';
import Image from '@/components/image';
import useResponsive from '@/hooks/useResponsive';
import { ChatProviderCard } from '@/sections/@checkout-builder/components/CheckoutBuilderChatComponent';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderChatProviders = {
  whatsapp: {
    id: 'whatsapp',
    label: 'WhatsApp',
    keyName: 'Phone Number',
  },
  jivochat: {
    id: 'jivochat',
    label: 'JivoChat',
    keyName: 'Widget ID',
  },
  zendesk: {
    id: 'zendesk',
    label: 'Zendesk',
    keyName: 'Widget ID',
  },
  manychat: {
    id: 'manychat',
    label: 'ManyChat',
    keyName: 'App ID',
  },
  crisp: {
    id: 'crisp',
    label: 'Crisp',
    keyName: 'Website ID',
  },
  tawk: {
    id: 'tawk',
    label: 'Tawk',
    keyName: 'Link do Chat',
  },
  facebook: {
    id: 'facebook',
    label: 'Facebook',
    keyName: 'Page ID',
  },
  freshchat: {
    id: 'freshchat',
    label: 'Freshchat',
    keyName: 'Widget ID',
  },
  intercom: {
    id: 'intercom',
    label: 'Intercom',
    keyName: 'App ID',
  },
};

const CheckoutBuilderChatComponentForm = () => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const provider = useWatch({
    name: getAttributeName('chat', 'provider'),
  });

  const form = useFormContext();

  const isMobile = useResponsive('down', 'sm');

  const onOpenPreview = (e) => {
    e.stopPropagation();
    setIsPreviewOpen(true);
  };

  const onClosePreview = () => {
    setIsPreviewOpen(false);
  };

  useEffect(() => {
    if (!provider) {
      form.setValue(getAttributeName('chat', 'provider'), CheckoutBuilderChatProviders.whatsapp.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [provider]);

  return (
    <Stack gap={2} onClick={onClosePreview}>
      <RHFSelect
        name={getAttributeName('chat', 'provider')}
        label="Integração"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.chat.attributes.provider}
        InputLabelProps={{
          shrink: true,
        }}
      >
        {Object.values(CheckoutBuilderChatProviders).map(({ id, label }) => (
          <MenuItem key={id} value={id}>
            <Stack direction="row" alignItems="center" gap={1.5}>
              <Image
                src={`/assets/icons/logos/${id}.png`}
                alt={label}
                sx={{
                  width: 24,
                  height: 24,
                  backgroundColor: 'transparent',
                }}
              />
              {label}
            </Stack>
          </MenuItem>
        ))}
      </RHFSelect>
      <RHFTextField
        autoFocus
        name={getAttributeName('chat', 'accountId')}
        label={CheckoutBuilderChatProviders[provider]?.keyName}
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.chat.attributes.accountId}
        placeholder="..."
        InputLabelProps={{
          shrink: true,
        }}
      />

      {isMobile && (
        <>
          <Button
            variant="outlined"
            color="inherit"
            endIcon={<Iconify icon="mdi:eye" />}
            onClick={onOpenPreview}
          >
            Preview
          </Button>

          {isPreviewOpen && (
            <div
              style={{
                height: '100%',
                width: '100%',
                position: 'absolute',
                zIndex: **********,
                top: '80%',
                left: 0,
              }}
            >
              <AnimatePresence>
                <ChatProviderCard key={provider} provider={provider} />
              </AnimatePresence>
            </div>
          )}
        </>
      )}
    </Stack>
  );
};

export default CheckoutBuilderChatComponentForm;
