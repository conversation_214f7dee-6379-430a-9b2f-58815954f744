import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { InputAdornment, Paper, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { RHFTextField } from '@/components/hook-form';
import RHFColorPicker from '@/components/hook-form/RHFColorPicker';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderTextComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const form = useFormContext();

  const handleChangeTextColor = (textColor) => {
    const text = getAttributeName('text');

    const content = form.getValues(text);

    const regex = /<([a-z]+)([^>]*)>/gi;

    const updatedText = content.replace(regex, (match, tagName, attributes) => {
      if (/style=/.test(attributes)) {
        if (!/color:/.test(attributes)) {
          attributes = attributes.replace(/style="([^"]*)"/, `style="$1; color: ${textColor}"`);
        } else {
          attributes = attributes.replace(/(color:\s*[^;"]+)(;?")/, `color: ${textColor}$2`);
        }
      } else {
        attributes += ` style="color: ${textColor};"`;
      }
      return `<${tagName}${attributes}>`;
    });

    form.setValue(text, updatedText);
  };

  return (
    <Stack spacing={2}>
      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('backgroundColor')}
          label="Cor de fundo"
          defaultValue={CheckoutBuilderComponentTypes.text.attributes.backgroundColor}
        />
      </Paper>
      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('borderColor')}
          label="Cor da borda"
          defaultValue={CheckoutBuilderComponentTypes.text.attributes.borderColor}
        />
      </Paper>
      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('color')}
          label="Cor do texto"
          onChange={handleChangeTextColor}
          defaultValue={CheckoutBuilderComponentTypes.text.attributes.color}
        />
      </Paper>
      <RHFTextField
        size="small"
        name={getAttributeName('borderWidth')}
        label="Largura da borda"
        type="number"
        defaultValue={CheckoutBuilderComponentTypes.text.attributes.borderWidth}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <Typography variant="caption" color="text.secondary">
                px
              </Typography>
            </InputAdornment>
          ),
        }}
      />
      <RHFTextField
        size="small"
        name={getAttributeName('borderRadius')}
        label="Raio da borda"
        type="number"
        defaultValue={CheckoutBuilderComponentTypes.text.attributes.borderRadius}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <Typography variant="caption" color="text.secondary">
                px
              </Typography>
            </InputAdornment>
          ),
        }}
      />
    </Stack>
  );
};

CheckoutBuilderTextComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderTextComponentForm;
