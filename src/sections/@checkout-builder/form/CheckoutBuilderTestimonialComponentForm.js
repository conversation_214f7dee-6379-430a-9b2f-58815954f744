import { Paper, Stack, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { RHFSwitch, RHFUploadAvatar } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import RHFRating from '../../../components/hook-form/RHFRating';
import RHFTextField from '../../../components/hook-form/RHFTextField';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderComponents from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderTestimonialComponentForm = ({ selected: { index } }) => {
  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderComponents({
    index,
  });

  return (
    <Stack gap={2}>
      <Stack mt={1}>
        <Stack gap={1} mt={1.5}>
          <RHFUploadAvatar
            name={getAttributeName('avatar')}
            maxSize={10000000}
            onDrop={([file]) => {
              form.setValue(
                getAttributeName('avatar'),
                Object.assign(file, {
                  preview: URL.createObjectURL(file),
                }),
                { shouldValidate: true, shouldDirty: true }
              );
            }}
            onRemove={() =>
              form.setValue(getAttributeName('avatar'), null, {
                shouldValidate: true,
                shouldDirty: true,
              })
            }
            multiple={false}
            helperText={
              <Typography variant="caption" color="text.secondary">
                Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB.
              </Typography>
            }
            accept={{ 'image/jpeg': [], 'image/png': [] }}
          />
        </Stack>
      </Stack>

      <Paper variant="outlined">
        <RHFTextField
          label="Depoimento"
          placeholder="Digite aqui seu depoimento"
          name={getAttributeName('text')}
          multiline
          minRows={5}
        />
      </Paper>

      <Stack component={Paper} p={2} gap={2} mt={1}>
        <Typography variant="caption" color="text.secondary">
          Estrelas
        </Typography>
        <RHFRating
          name={getAttributeName('rating')}
          defaultValue={CheckoutBuilderComponentTypes.testimonial.attributes.rating}
        />
      </Stack>

      <Paper variant="outlined">
        <RHFTextField
          label="Nome"
          placeholder="Digite o nome de quem está fazendo o depoimento"
          name={getAttributeName('author')}
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          label="Cor de fundo"
          name={getAttributeName('backgroundColor')}
          defaultValue={CheckoutBuilderComponentTypes.testimonial.attributes.backgroundColor}
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          label="Cor do texto"
          name={getAttributeName('textColor')}
          defaultValue={CheckoutBuilderComponentTypes.testimonial.attributes.textColor}
        />
      </Paper>

      <RHFSwitch
        name={getAttributeName('horizontal')}
        label="Modo horizontal"
        defaultValue={CheckoutBuilderComponentTypes.testimonial.attributes.horizontal}
      />
    </Stack>
  );
};

CheckoutBuilderTestimonialComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderTestimonialComponentForm;
