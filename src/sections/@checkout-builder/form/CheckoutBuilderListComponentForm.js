import {
  Alert,
  Button,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  ToggleButton,
  Typography,
} from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { v4 } from 'uuid';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ReactComponent as CheckIcon } from '../../../assets/checkout/check.svg';
import { RHFSelect, RHFSwitch, RHFTextField } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import Iconify from '../../../components/iconify/Iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderListAlignment = {
  left: {
    id: 'left',
    label: '<PERSON><PERSON><PERSON>',
    icon: 'mdi:format-align-left',
  },
  center: {
    id: 'center',
    label: 'Centro',
    icon: 'mdi:format-align-center',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

export const CheckoutBuilderListStyle = {
  none: {
    id: 'none',
    label: 'Nenhum',
  },
  check: {
    id: 'check',
    label: 'Check',
    icon: CheckIcon,
  },
  decimal: {
    id: 'decimal',
    label: 'Decimal',
  },
  disc: {
    id: 'disc',
    label: 'Círculo',
  },
};

const CheckoutBuilderListComponentForm = ({ selected: { index } }) => {
  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const name = getAttributeName('items');

  const items = useFieldArray({
    name,
  });

  return (
    <Stack spacing={2}>
      <RHFSelect
        name={getAttributeName('style')}
        label="Ícone"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.list.attributes.style}
      >
        {Object.values(CheckoutBuilderListStyle).map((type) => (
          <MenuItem key={type.id} value={type.id}>
            {type.label}
          </MenuItem>
        ))}
      </RHFSelect>

      {form.watch(getAttributeName('style')) === CheckoutBuilderListStyle.check.id && (
        <Paper variant="outlined">
          <RHFColorPicker
            name={getAttributeName('iconColor')}
            label="Cor do ícone"
            defaultValue={CheckoutBuilderComponentTypes.list.attributes.iconColor}
          />
        </Paper>
      )}

      <RHFSwitch
        name={getAttributeName('showTitle')}
        label="Título"
        defaultValue={CheckoutBuilderComponentTypes.list.attributes.showTitle}
      />

      {form.watch(getAttributeName('showTitle')) && (
        <RHFTextField
          name={getAttributeName('title')}
          label="Título"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.list.attributes.title}
        />
      )}

      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('backgroundColor')}
          label="Cor de fundo"
          defaultValue={CheckoutBuilderComponentTypes.list.attributes.backgroundColor}
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          name={getAttributeName('textColor')}
          label="Cor do texto"
          defaultValue={CheckoutBuilderComponentTypes.list.attributes.textColor}
        />
      </Paper>

      <Stack component={Paper} p={1} gap={1} variant="outlined">
        <Typography variant="caption" color="text.secondary">
          Alinhamento
        </Typography>
        <RHFToggleButtonGroup
          name={getAttributeName('alignment')}
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.header.attributes.alignment}
          exclusive
          color="primary"
          sx={{
            border: 'none',
          }}
        >
          {Object.values(CheckoutBuilderListAlignment).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Stack>

      <RHFTextField
        size="small"
        name={getAttributeName('fontSize')}
        label="Tamanho"
        type="number"
        defaultValue={CheckoutBuilderComponentTypes.list.attributes.fontSize}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <Typography variant="caption" color="text.secondary">
                px
              </Typography>
            </InputAdornment>
          ),
        }}
      />

      <Stack component={Paper} p={2} variant="outlined" alignItems="start" gap={1}>
        <Typography variant="caption" color="text.secondary">
          Itens
        </Typography>
        {items.fields.length === 0 && (
          <Alert
            severity="warning"
            sx={{
              width: 1,
            }}
          >
            Nenhum item adicionado
          </Alert>
        )}
        {items.fields.map((item, i) => (
          <RHFTextField
            key={item.id}
            size="small"
            name={`${name}.${i}.text`}
            variant="filled"
            hiddenLabel
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => {
                      items.remove(i);
                    }}
                  >
                    <Iconify icon="ion:trash" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        ))}
        <Stack direction="row" gap={1} alignItems="center">
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              items.append({
                id: v4(),
                text: 'Novo item',
              });
            }}
            disabled={items.fields.length >= 10}
          >
            Adicionar
          </Button>
          <Typography variant="caption" color="text.secondary">
            {items.fields.length} / 10
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  );
};

CheckoutBuilderListComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderListComponentForm;
