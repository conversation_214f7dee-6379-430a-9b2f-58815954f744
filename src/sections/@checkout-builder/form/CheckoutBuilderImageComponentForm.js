import { InputAdornment, Paper, Stack, ToggleButton, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { RHFTextField, RHFUpload } from '../../../components/hook-form';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderComponents from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderImageAlignments = {
  left: {
    id: 'left',
    label: 'Esquerda',
    icon: 'mdi:format-align-left',
  },
  center: {
    id: 'center',
    label: 'Centro',
    icon: 'mdi:format-align-center',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

const CheckoutBuilderImageComponentForm = ({ selected: { index } }) => {
  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderComponents({
    index,
  });

  return (
    <Stack gap={2}>
      <Stack justifyContent="center">
        <RHFUpload
          name={getAttributeName('image')}
          maxSize={10000000}
          onDrop={([file]) => {
            form.setValue(
              getAttributeName('image'),
              Object.assign(file, {
                preview: URL.createObjectURL(file),
              }),
              { shouldValidate: true, shouldDirty: true }
            );
          }}
          onRemove={() =>
            form.setValue(getAttributeName('image'), null, {
              shouldValidate: true,
              shouldDirty: true,
            })
          }
          helperText="Formatos aceitos: JPG ou PNG. Tamanho máximo: 10MB"
          accept={{ 'image/jpeg': [], 'image/png': [] }}
        />
      </Stack>

      <Stack component={Paper} p={1} gap={1} variant="outlined">
        <Typography variant="caption" color="text.secondary">
          Alinhamento
        </Typography>
        <RHFToggleButtonGroup
          name={getAttributeName('alignment')}
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.image.attributes.alignment}
          exclusive
          color="primary"
          sx={{
            border: 'none',
          }}
        >
          {Object.values(CheckoutBuilderImageAlignments).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Stack>

      <RHFTextField
        name={getAttributeName('redirectUrl')}
        label="URL de redirecionamento"
        size="small"
        placeholder="https://"
        type="url"
        defaultValue={CheckoutBuilderComponentTypes.image.attributes.redirectUrl}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="mdi:link" />
            </InputAdornment>
          ),
        }}
      />
    </Stack>
  );
};

CheckoutBuilderImageComponentForm.propTypes = { selected: PropTypes.object.isRequired };

export default CheckoutBuilderImageComponentForm;
