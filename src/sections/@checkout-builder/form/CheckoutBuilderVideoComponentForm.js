import { Alert, InputAdornment, Paper, Stack, ToggleButton, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import { RHFSwitch, RHFTextField } from '../../../components/hook-form';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderVideoAlignments = {
  // Todo: use same alignment for all components
  left: {
    id: 'left',
    label: 'Esquerda',
    icon: 'mdi:format-align-left',
  },
  center: {
    id: 'center',
    label: 'Centro',
    icon: 'mdi:format-align-center',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

const CheckoutBuilderVideoComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  return (
    <Stack gap={2}>
      <RHFTextField
        name={getAttributeName('url')}
        label="URL do vídeo"
        size="small"
        placeholder="https://"
        type="url"
        defaultValue={CheckoutBuilderComponentTypes.image.attributes.redirectUrl}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="mdi:link" />
            </InputAdornment>
          ),
        }}
      />

      <Alert severity="info">Você pode colar URLs do YouTube, Vimeo ou Facebook</Alert>

      <Stack component={Paper} p={1} gap={1} variant="outlined">
        <Typography variant="caption" color="text.secondary">
          Alinhamento
        </Typography>
        <RHFToggleButtonGroup
          name={getAttributeName('alignment')}
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.image.attributes.alignment}
          exclusive
          color="primary"
          sx={{
            border: 'none',
          }}
        >
          {Object.values(CheckoutBuilderVideoAlignments).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Stack>

      <RHFSwitch
        name={getAttributeName('hideControls')}
        label="Esconder controles"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.video.attributes.hideControls}
      />
    </Stack>
  );
};

CheckoutBuilderVideoComponentForm.propTypes = { selected: PropTypes.object.isRequired };

export default CheckoutBuilderVideoComponentForm;
