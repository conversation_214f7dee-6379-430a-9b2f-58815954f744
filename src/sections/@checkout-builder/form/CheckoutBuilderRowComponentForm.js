import { ToggleButton, ToggleButtonGroup } from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useFormContext, useWatch } from 'react-hook-form';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderRowCard } from '../components/CheckoutBuilderRowCard';
import { CheckoutBuilderDeviceTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderRowComponentForm = ({ selected: { index } }) => {
  const form = useFormContext();

  const {
    row: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [device, layout] = useWatch({
    name: ['device', getAttributeName('layout')],
  });

  const layouts = CheckoutBuilderDeviceTypes[device]?.layouts ?? [];

  const layoutIndex = Object.values(layouts).findIndex((l) => l?.toString() === layout?.toString());

  return (
    <Stack spacing={2} mt={1}>
      <ToggleButtonGroup
        value={layoutIndex}
        onChange={(event, value) => {
          if (value === null) {
            return;
          }
          form.setValue(getAttributeName('layout'), layouts[value]);
        }}
        defaultValue={0}
        exclusive
        orientation="vertical"
        color="primary"
        sx={{
          border: 'none',
        }}
      >
        {Object.values(layouts).map((cols, i) => (
          <ToggleButton
            key={i}
            value={i}
            sx={{
              width: 1,
              p: 1,
            }}
          >
            <CheckoutBuilderRowCard draggable={false} layout={cols} />
            {layoutIndex === i && (
              <Iconify
                icon="ion:checkmark-circle"
                sx={{
                  position: 'absolute',
                  top: 25,
                  right: 25,
                  color: (theme) => theme.palette.success.main,
                  width: 24,
                  height: 24,
                }}
              />
            )}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Stack>
  );
};

CheckoutBuilderRowComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderRowComponentForm;
