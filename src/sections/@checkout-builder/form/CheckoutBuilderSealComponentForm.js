import { Paper, Stack, ToggleButton, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { RHFSwitch, RHFTextField } from '../../../components/hook-form';
import RHFColorPicker from '../../../components/hook-form/RHFColorPicker';
import RHFToggleButtonGroup from '../../../components/hook-form/RHFToggleButtonGroup';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import SealOneIllustration from '../illustrations/SealOneIllustration';
import SealThreeIllustration from '../illustrations/SealThreeIllustration';
import SealTwoIllustration from '../illustrations/SealTwoIllustration';

export const CheckoutBuilderSealTypes = {
  one: {
    id: 'one',
    defaultProps: {
      primaryColor: '#4593f9',
      title: 'Privacidade',
      subtitle: 'Garantida',
      titleTextColor: '#ffffff',
      darkMode: false,
    },
    Icon: SealOneIllustration,
  },
  two: {
    id: 'two',
    defaultProps: {
      primaryColor: '#f9454b',
      title: '100%',
      subtitle: 'DE GARANTIA',
      titleTextColor: '#ffffff',
      darkMode: false,
    },
    Icon: SealTwoIllustration,
  },
  three: {
    id: 'three',
    defaultProps: {
      primaryColor: '#ffbf00',
      topText: '7',
      title: 'DIAS',
      subtitle: 'DE GARANTIA',
      titleTextColor: '#ffffff',
      darkMode: false,
    },
    Icon: SealThreeIllustration,
  },
};

export const CheckoutBuilderSealAlignments = {
  left: {
    id: 'left',
    label: 'Esquerda',
    icon: 'mdi:format-align-left',
  },
  center: {
    id: 'center',
    label: 'Centro',
    icon: 'mdi:format-align-center',
  },
  right: {
    id: 'right',
    label: 'Direita',
    icon: 'mdi:format-align-right',
  },
};

const CheckoutBuilderSealComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const form = useFormContext();

  return (
    <Stack gap={2}>
      <RHFToggleButtonGroup
        name={getAttributeName('type')}
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.seal.attributes.type}
        exclusive
        color="primary"
        sx={{
          border: 'none',
        }}
      >
        {Object.values(CheckoutBuilderSealTypes).map((type) => (
          <ToggleButton
            key={type.id}
            value={type.id}
            sx={{
              width: 1,
            }}
            onClick={() => {
              if (type.defaultProps) {
                Object.entries(type.defaultProps).forEach(([key, value]) => {
                  form.setValue(getAttributeName(key), value, {
                    shouldValidate: true,
                    shouldDirty: true,
                  });
                });
              }
            }}
          >
            {type.Icon && <type.Icon {...type.defaultProps} />}
          </ToggleButton>
        ))}
      </RHFToggleButtonGroup>

      <RHFTextField
        name={getAttributeName('topText')}
        label="Texto superior"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.seal.attributes.topText}
      />

      <RHFTextField
        name={getAttributeName('title')}
        label="Título"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.seal.attributes.title}
      />

      <RHFTextField
        name={getAttributeName('subtitle')}
        label="Subtítulo"
        size="small"
        defaultValue={CheckoutBuilderComponentTypes.seal.attributes.subtitle}
      />

      <Paper variant="outlined">
        <RHFColorPicker
          label="Cor principal"
          name={getAttributeName('primaryColor')}
          defaultValue={CheckoutBuilderComponentTypes.seal.attributes.primaryColor}
        />
      </Paper>

      <Paper variant="outlined">
        <RHFColorPicker
          label="Cor do título"
          name={getAttributeName('titleTextColor')}
          defaultValue={CheckoutBuilderComponentTypes.seal.attributes.titleTextColor}
        />
      </Paper>

      <Stack component={Paper} p={1} gap={1} variant="outlined">
        <Typography variant="caption" color="text.secondary">
          Alinhamento
        </Typography>
        <RHFToggleButtonGroup
          name={getAttributeName('alignment')}
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.seal.attributes.alignment}
          exclusive
          color="primary"
          sx={{
            border: 'none',
          }}
        >
          {Object.values(CheckoutBuilderSealAlignments).map((type) => (
            <ToggleButton
              key={type.id}
              value={type.id}
              sx={{
                width: 1,
              }}
            >
              <Iconify icon={type.icon} />
            </ToggleButton>
          ))}
        </RHFToggleButtonGroup>
      </Stack>

      <RHFSwitch
        name={getAttributeName('darkMode')}
        label="Modo escuro"
        defaultValue={CheckoutBuilderComponentTypes.seal.attributes.darkMode}
      />
    </Stack>
  );
};

CheckoutBuilderSealComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderSealComponentForm;
