import { InputAdornment, MenuItem, Stack } from '@mui/material';
import { Box } from '@mui/system';
import PropTypes from 'prop-types';
import { useWatch } from 'react-hook-form';
import { RHFMultiCheckbox, RHFSelect, RHFTextField } from '../../../components/hook-form';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

export const CheckoutBuilderFacebookType = {
  commentSection: {
    id: 'commentSection',
    label: 'Seção de comentários',
  },
  singleComment: {
    id: 'singleComment',
    label: 'Comentário único',
  },
  page: {
    id: 'page',
    label: 'Página',
  },
  post: {
    id: 'post',
    label: 'Post',
  },
};

export const CheckoutBuilderFacebookSize = {
  original: {
    id: 'original',
    label: 'Original',
  },
  small: {
    id: 'small',
    label: 'Pequeno',
  },
  medium: {
    id: 'medium',
    label: 'Médio',
  },
  large: {
    id: 'large',
    label: 'Grande',
  },
};

export const CheckoutBuilderFacebookOrderBy = {
  relevant: {
    id: 'relevant',
    label: 'Relevância',
  },
  latest: {
    id: 'latest',
    label: 'Mais recentes',
  },
  oldest: {
    id: 'oldest',
    label: 'Mais antigos',
  },
};

export const CheckoutBuilderFacebookTabs = {
  timeline: {
    id: 'timeline',
    label: 'Linha do tempo',
  },
  events: {
    id: 'events',
    label: 'Eventos',
  },
  messages: {
    id: 'messages',
    label: 'Mensagens',
  },
};

export const CheckoutBuilderFacebookOptions = {
  smallHeader: {
    id: 'smallHeader',
    label: 'Cabeçalho pequeno',
  },
  coverPhoto: {
    id: 'coverPhoto',
    label: 'Foto de capa',
  },
  facePile: {
    id: 'facePile',
    label: 'Foto das pessoas',
  },
  callToAction: {
    id: 'callToAction',
    label: 'Call to Action',
  },
};

const CheckoutBuilderFacebookComponentForm = ({ selected: { index } }) => {
  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const type = useWatch({
    name: getAttributeName('type'),
  });

  return (
    <Stack gap={2}>
      <RHFSelect
        name={getAttributeName('type')}
        label="Tipo"
        defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.type}
        size="small"
      >
        {Object.values(CheckoutBuilderFacebookType).map(({ id, label }) => (
          <MenuItem key={id} value={id}>
            {label}
          </MenuItem>
        ))}
      </RHFSelect>

      <RHFTextField
        name={getAttributeName('url')}
        label="URL"
        size="small"
        placeholder="https://"
        type="url"
        defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.url}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="mdi:link" />
            </InputAdornment>
          ),
        }}
      />

      <RHFSelect
        name={getAttributeName('size')}
        label="Tamanho"
        defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.size}
        size="small"
      >
        {Object.values(CheckoutBuilderFacebookSize).map(({ id, label }) => (
          <MenuItem key={id} value={id}>
            {label}
          </MenuItem>
        ))}
      </RHFSelect>

      {type === CheckoutBuilderFacebookType.commentSection.id && (
        <RHFTextField
          name={getAttributeName('count')}
          label="Quantidade de comentários"
          size="small"
          defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.count}
        />
      )}

      {type === CheckoutBuilderFacebookType.commentSection.id && (
        <RHFSelect
          name={getAttributeName('orderBy')}
          label="Ordenar por"
          defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.orderBy}
          size="small"
        >
          {Object.values(CheckoutBuilderFacebookOrderBy).map(({ id, label }) => (
            <MenuItem key={id} value={id}>
              {label}
            </MenuItem>
          ))}
        </RHFSelect>
      )}

      {type === CheckoutBuilderFacebookType.page.id && (
        <Box
          sx={{
            mx: 1,
          }}
        >
          <RHFMultiCheckbox
            name={getAttributeName('tabs')}
            label="Tabs"
            defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.tabs}
            size="small"
            options={Object.values(CheckoutBuilderFacebookTabs).map(({ id, label }) => ({
              value: id,
              label,
            }))}
          />
        </Box>
      )}

      {type === CheckoutBuilderFacebookType.page.id && (
        <Box
          sx={{
            mx: 1,
          }}
        >
          <RHFMultiCheckbox
            name={getAttributeName('options')}
            label="Opções"
            defaultValue={CheckoutBuilderComponentTypes.facebook.attributes.options}
            size="small"
            options={Object.values(CheckoutBuilderFacebookOptions).map(({ id, label }) => ({
              value: id,
              label,
            }))}
          />
        </Box>
      )}
    </Stack>
  );
};

CheckoutBuilderFacebookComponentForm.propTypes = {
  selected: PropTypes.object.isRequired,
};

export default CheckoutBuilderFacebookComponentForm;
