import { useCallback, useContext } from 'react';
import { useFormContext } from 'react-hook-form';
import { v4 } from 'uuid';
import { CheckoutBuilderContext } from '../../../contexts/CheckoutBuilderContext';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { FormScopes } from '../../../utils/form';

const useDragAndDropRows = () => {
  const form = useFormContext();

  const { setSelectedID } = useContext(SelectedElementContext);

  const { setScope } = useContext(CheckoutBuilderContext);

  const getRows = useCallback(() => {
    const device = form.getValues('device');

    return form.getValues(`${device}.rows`) || [];
  }, [form]);

  const setRows = useCallback(
    (rows) => {
      const device = form.getValues('device');

      form.setValue(`${device}.rows`, rows);
    },
    [form]
  );

  const moveComponent = useCallback(
    ({ from, to }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      if (from.rowIndex === to.rowIndex && from.columnIndex === to.columnIndex) {
        const [removed] = rows[from.rowIndex].columns[from.columnIndex].components.splice(
          from.componentIndex,
          1
        ) || [undefined];

        if (!removed) {
          return;
        }

        if (to.componentIndex > from.componentIndex) {
          to.componentIndex -= 1;
        }

        rows[from.rowIndex].columns[from.columnIndex].components.splice(
          to.componentIndex,
          0,
          removed
        );

        setRows(rows);

        return;
      }

      const [moved] = rows[from.rowIndex].columns[from.columnIndex].components.splice(
        from.componentIndex,
        1
      ) || [undefined];

      if (!moved) {
        return;
      }

      if (!rows[to.rowIndex].columns[to.columnIndex]) {
        rows[to.rowIndex].columns[to.columnIndex] = {
          id: v4(),
          type: 'column',
          components: [],
        };
      }

      rows[to.rowIndex].columns[to.columnIndex].components.splice(to.componentIndex, 0, moved);

      setRows(rows);
    },
    [getRows, setRows, setScope, setSelectedID]
  );

  const insertComponent = useCallback(
    ({ to, payload }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      if (!rows[to.rowIndex].columns[to.columnIndex]) {
        rows[to.rowIndex].columns[to.columnIndex] = {
          id: v4(),
          type: 'column',
          components: [],
        };
      }

      rows[to.rowIndex].columns[to.columnIndex].components.splice(
        to.componentIndex || 0,
        0,
        payload
      );

      setRows(rows);
    },
    [getRows, setRows, setScope, setSelectedID]
  );

  const removeComponent = useCallback(
    ({ from }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      rows[from.rowIndex].columns[from.columnIndex].components.splice(from.componentIndex, 1);
    },
    [getRows, setScope, setSelectedID]
  );

  const insertRow = useCallback(
    ({ to, payload }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      rows.splice(to.rowIndex, 0, payload);

      setRows(rows);
    },
    [getRows, setRows, setScope, setSelectedID]
  );

  const removeRow = useCallback(
    ({ from }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      rows.splice(from.rowIndex, 1);

      form.setValue('rows', rows || []);
    },
    [form, getRows, setScope, setSelectedID]
  );

  const moveRow = useCallback(
    ({ from, to }) => {
      setScope(FormScopes.INDEX);

      setSelectedID(undefined);

      const rows = getRows();

      const [moved] = rows.splice(from.rowIndex, 1) || [undefined];

      if (!moved) {
        return;
      }

      if (to.rowIndex > from.rowIndex) {
        to.rowIndex -= 1;
      }

      rows.splice(to.rowIndex, 0, moved);

      setRows(rows);
    },
    [getRows, setRows, setScope, setSelectedID]
  );

  const copyComponentFromDesktopToMobile = useCallback(() => {
    const settings = form.getValues('desktop.settings');

    const extra = form.getValues('desktop.extra');

    form.setValue('mobile.rows', []);

    form.setValue('mobile.settings', settings);

    form.setValue('mobile.extra', extra);

    const rows = [...(form.getValues('desktop.rows') || [])].map((row) => ({
      ...row,
      id: v4(),
      layout: [12],
      columns: [
        {
          id: v4(),
          type: 'column',
          components: row.columns?.reduce(
            (carry, column) => [
              ...carry,
              ...column.components.map((component) => ({ ...component, id: v4() })),
            ],
            []
          ),
        },
      ],
    }));

    form.setValue('mobile.rows', rows);
  }, [form]);

  return {
    moveComponent,
    insertComponent,
    removeComponent,
    insertRow,
    removeRow,
    moveRow,
    copyComponentFromDesktopToMobile,
  };
};

export default useDragAndDropRows;
