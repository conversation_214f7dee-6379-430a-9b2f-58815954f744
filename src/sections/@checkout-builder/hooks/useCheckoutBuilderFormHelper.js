import { useFormContext } from 'react-hook-form';

const useCheckoutBuilderFormHelper = (
  { index: { row, column, component } } = {
    index: {},
  }
) => {
  const form = useFormContext();

  return {
    settings: {
      getAttributeName: (attribute) => `${form.getValues('device')}.settings.${attribute}`,
    },
    extra: {
      getAttributeName: (type, attribute) =>
        `${form.getValues('device')}.extra.${type}.attributes.${attribute}`,
      getAttributesName: (type) => `${form.getValues('device')}.extra.${type}.attributes`,
    },
    row: {
      getAttributeName: (attribute) => `${form.getValues('device')}.rows[${row}].${attribute}`,
    },
    column: {
      getAttributeName: (attribute) =>
        `${form.getValues('device')}.rows[${row}].columns[${column}].${attribute}`,
    },
    component: {
      getRootProperty: (property) =>
        `${form.getValues(
          'device'
        )}.rows[${row}].columns[${column}].components[${component}].${property}`,
      getAttributeName: (attribute) =>
        `${form.getValues(
          'device'
        )}.rows[${row}].columns[${column}].components[${component}].attributes.${attribute}`,
      getAttributesName: () =>
        `${form.getValues(
          'device'
        )}.rows[${row}].columns[${column}].components[${component}].attributes`,
    },
  };
};

export default useCheckoutBuilderFormHelper;
