import { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { CheckoutBuilderComponentTypes, CheckoutBuilderDeviceTypes } from '../constants';

const defaultValue = {
  id: null,
  type: null,
  index: {
    row: null,
    column: null,
    component: null,
  },
};

export const useSelectedElementIndex = ({ selectedID }) => {
  const { getValues } = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const selected = useMemo(() => {
    if (!selectedID) {
      return defaultValue;
    }

    if (CheckoutBuilderComponentTypes[selectedID]?.extra) {
      return {
        id: selectedID,
        type: selectedID,
        index: { row: null, column: null, component: null },
      };
    }

    const rows =
      {
        [CheckoutBuilderDeviceTypes.desktop.id]: getValues('desktop.rows'),
        [CheckoutBuilderDeviceTypes.mobile.id]: getValues('mobile.rows'),
      }[device] || [];

    const components = rows.flatMap((row, rowIndex) => [
      { id: row.id, type: row.type, index: { row: rowIndex } },
      ...row.columns.flatMap((column, colIndex) => [
        { id: column.id, type: column.type, index: { row: rowIndex, column: colIndex } },
        ...column.components.map((component, compIndex) => ({
          id: component.id,
          type: component.type,
          index: { row: rowIndex, column: colIndex, component: compIndex },
        })),
      ]),
    ]);

    return components.find(({ id }) => id === selectedID);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedID]);

  return {
    selected,
  };
};
