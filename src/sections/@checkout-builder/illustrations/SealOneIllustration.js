import PropTypes from 'prop-types';

const SealOneIllustration = ({ title, subtitle, primaryColor, titleTextColor, darkMode }) => (
  <svg viewBox="0 0 448 353" fill={darkMode ? 'black' : 'none'} xmlns="http://www.w3.org/2000/svg">
    <path
      d="M406.035 244.316L440.582 220.32C440.582 220.32 424.898 217.803 396.074 217.289C367.25 216.775 358.137 220.32 358.137 220.32L310.451 273.244C310.451 273.244 339.592 265.896 375.092 265.999C410.592 266.102 440.582 273.244 440.582 273.244L406.035 244.316Z"
      fill={primaryColor}
      stroke="black"
      strokeWidth="5"
    />
    <path
      d="M41.9226 244.265L7.37646 220.269C7.37646 220.269 23.06 217.751 51.8838 217.238C80.7076 216.724 89.821 220.269 89.821 220.269L137.507 273.193C137.507 273.193 108.366 265.845 72.8659 265.948C37.3659 266.051 7.37646 273.193 7.37646 273.193L41.9226 244.265Z"
      fill={primaryColor}
      stroke="black"
      strokeWidth="5"
    />
    <path
      d="M92.7053 238.079L42.2637 214.718V197.385H92.7053V238.079Z"
      fill={primaryColor}
      stroke="black"
      strokeWidth="5"
    />
    <path
      d="M358.788 238.079L409.229 214.718V197.385H358.788V238.079Z"
      fill={primaryColor}
      stroke="black"
      strokeWidth="5"
    />
    <path
      d="M229.08 3.23022L115.265 36.626C115.265 36.626 112.883 50.5074 105.207 60.6669C97.5312 70.8265 72.5183 78.9742 72.5183 78.9742C72.5183 78.9742 71.0626 138.825 80.856 214.569C90.6494 290.313 229.08 350.164 229.08 350.164C229.08 350.164 364.335 290.715 374.923 214.569C385.51 138.423 383.128 78.9742 383.128 78.9742C383.128 78.9742 360.63 72.8383 350.836 62.4775C341.043 52.1168 340.514 36.626 340.514 36.626L229.08 3.23022Z"
      fill={darkMode ? 'black' : 'white'}
      stroke="black"
      strokeWidth="5"
    />
    <path
      d="M228.992 16.384L123.711 47.2672C123.711 47.2672 121.507 60.1042 114.407 69.4994C107.307 78.8945 84.1694 86.4293 84.1694 86.4293C84.1694 86.4293 82.8227 141.777 91.8818 211.822C100.941 281.868 228.992 337.215 228.992 337.215C228.992 337.215 354.105 282.24 363.899 211.822C373.692 141.405 371.489 86.4293 371.489 86.4293C371.489 86.4293 350.677 80.755 341.618 71.1737C332.559 61.5925 332.07 47.2672 332.07 47.2672L228.992 16.384Z"
      stroke={primaryColor}
      strokeWidth="5"
    />
    <path
      d="M79.8599 205.633H376.364L374.138 224.028C374.138 224.028 287.848 216.914 224.555 216.914C161.263 216.914 81.7982 223.926 81.7982 223.926L79.8599 205.633Z"
      fill={darkMode ? 'black' : 'white'}
      fillOpacity="0.2"
    />
    <mask id="path-8-inside-1" fill="white">
      <path d="M35.3525 114.584H418.964L410.91 215.499C410.91 215.499 307.589 210.412 227.264 210.412C146.939 210.412 40.757 215.499 40.757 215.499L35.3525 114.584Z" />
    </mask>
    <path
      d="M35.3525 114.584H418.964L410.91 215.499C410.91 215.499 307.589 210.412 227.264 210.412C146.939 210.412 40.757 215.499 40.757 215.499L35.3525 114.584Z"
      fill={primaryColor}
      stroke="black"
      strokeWidth="10"
      mask="url(#path-8-inside-1)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M214.29 63.7839C219.357 63.7157 224.228 61.6605 228 58C231.771 61.6612 236.643 63.7171 241.709 63.7858C241.902 65.0164 242 66.2848 242 67.5741C242 77.4664 236.155 85.8818 228 89C219.845 85.8799 214 77.4645 214 67.5722C214 66.2811 214.1 65.0164 214.29 63.7839ZM234.487 70.804C234.806 70.4469 234.982 69.9687 234.978 69.4723C234.974 68.9759 234.79 68.5011 234.466 68.15C234.141 67.799 233.703 67.5999 233.244 67.5956C232.785 67.5913 232.343 67.7821 232.013 68.127L226.25 74.3614L223.987 71.9135C223.657 71.5686 223.215 71.3778 222.756 71.3821C222.297 71.3864 221.859 71.5855 221.534 71.9365C221.21 72.2875 221.026 72.7624 221.022 73.2588C221.018 73.7552 221.194 74.2334 221.513 74.5905L225.013 78.377C225.341 78.7319 225.786 78.9313 226.25 78.9313C226.714 78.9313 227.159 78.7319 227.487 78.377L234.487 70.804Z"
      fill={darkMode ? 'white' : 'black'}
    />
    <path
      d="M196.771 64.261C197.064 63.3902 198.334 63.3902 198.625 64.261L199.669 67.3733C199.732 67.5629 199.856 67.7281 200.023 67.8452C200.189 67.9624 200.389 68.0255 200.595 68.0257H203.97C204.915 68.0257 205.307 69.198 204.543 69.7369L201.813 71.6599C201.647 71.7772 201.523 71.9426 201.459 72.1325C201.395 72.3224 201.395 72.527 201.459 72.7169L202.502 75.8293C202.794 76.7001 201.766 77.4252 201 76.8863L198.271 74.9633C198.104 74.8461 197.904 74.7829 197.698 74.7829C197.492 74.7829 197.292 74.8461 197.125 74.9633L194.395 76.8863C193.631 77.4252 192.603 76.7001 192.895 75.8293L193.938 72.7169C194.002 72.527 194.001 72.3224 193.938 72.1325C193.874 71.9426 193.75 71.7772 193.583 71.6599L190.854 69.7379C190.091 69.199 190.484 68.0266 191.428 68.0266H194.802C195.008 68.0267 195.208 67.9636 195.375 67.8464C195.541 67.7293 195.665 67.564 195.729 67.3743L196.772 64.2619L196.771 64.261Z"
      fill={primaryColor}
    />
    <path
      d="M255.691 64.261C255.983 63.3902 257.253 63.3902 257.545 64.261L258.588 67.3733C258.652 67.5629 258.776 67.7281 258.942 67.8452C259.108 67.9624 259.309 68.0255 259.514 68.0257H262.889C263.834 68.0257 264.226 69.198 263.463 69.7369L260.733 71.6599C260.566 71.7772 260.442 71.9426 260.378 72.1325C260.315 72.3224 260.315 72.527 260.378 72.7169L261.421 75.8293C261.714 76.7001 260.685 77.4252 259.92 76.8863L257.19 74.9633C257.024 74.8461 256.823 74.7829 256.617 74.7829C256.412 74.7829 256.211 74.8461 256.045 74.9633L253.315 76.8863C252.55 77.4252 251.523 76.7001 251.814 75.8293L252.857 72.7169C252.921 72.527 252.921 72.3224 252.857 72.1325C252.793 71.9426 252.669 71.7772 252.503 71.6599L249.774 69.7379C249.01 69.199 249.403 68.0266 250.347 68.0266H253.721C253.927 68.0267 254.128 67.9636 254.294 67.8464C254.461 67.7293 254.585 67.564 254.648 67.3743L255.692 64.2619L255.691 64.261Z"
      fill={primaryColor}
    />
    <text
      transform="translate(223 185)"
      id="title-seal"
      fontSize="55"
      fontWeight="bold"
      fill={titleTextColor}
    >
      <tspan textAnchor="middle">{title}</tspan>
    </text>
    <text
      transform="translate(223 250)"
      id="subtitle-seal"
      fontSize="22"
      fontWeight="bold"
      fill={darkMode ? 'white' : 'black'}
    >
      <tspan textAnchor="middle">{subtitle}</tspan>
    </text>
  </svg>
);

SealOneIllustration.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  primaryColor: PropTypes.string,
  titleTextColor: PropTypes.string,
  darkMode: PropTypes.bool,
};

export default SealOneIllustration;
