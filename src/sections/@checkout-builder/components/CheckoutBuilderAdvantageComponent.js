import { useState } from 'react';
import { Paper, Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { useFormContext } from 'react-hook-form';
import PropTypes from 'prop-types';
import {
  CheckoutBuilderAdvantageSizes,
  icons,
} from '../form/CheckoutBuilderAdvantageComponentForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const CheckoutBuilderAdvantageComponent = ({
  attributes: {
    icon,
    primaryColor,
    titleTextColor,
    size,
    darkMode,
    vertical,
    title,
    subtitle,
  } = {},
  index,
}) => {
  const Icon = icons[icon]?.Icon || icons.verified.Icon;

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [editableTitle] = useState(title);
  const [editableSubTitle] = useState(subtitle);

  const form = useFormContext();

  const handleChangeBoxText = (e) => {
    const { textContent } = e.target;
    const name = e.target.getAttribute('name');
    form.setValue(getAttributeName(name), textContent);
  };

  return (
    <ComponentWrapper display="flex" justifyContent="center">
      <Stack
        component={Paper}
        variant="outlined"
        direction={vertical ? 'column' : 'row'}
        alignItems="center"
        spacing={1}
        p={2}
        border={`2px solid ${primaryColor}`}
        backgroundColor={darkMode ? '#1A202C' : '#fff'}
        width={CheckoutBuilderAdvantageSizes[size]?.width || 1}
        maxWidth={1}
      >
        <Box p={1}>
          <Icon width={48} height={48} fill={primaryColor} />
        </Box>
        <Stack spacing={1}>
          <Typography
            variant="h5"
            sx={{
              color: titleTextColor,
              fontWeight: 'bold',
              wordBreak: 'break-word',
              outline: 'none',
              textAlign: vertical ? 'center' : 'left',
            }}
            contenteditable="true"
            name="title"
            onInput={handleChangeBoxText}
          >
            {editableTitle}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: primaryColor,
              wordBreak: 'break-word',
              outline: 'none',
              textAlign: vertical ? 'center' : 'left',
            }}
            contenteditable="true"
            name="subtitle"
            onInput={handleChangeBoxText}
          >
            {editableSubTitle}
          </Typography>
        </Stack>
      </Stack>
    </ComponentWrapper>
  );
};

CheckoutBuilderAdvantageComponent.propTypes = {
  attributes: PropTypes.object,
  index: PropTypes.number,
};

export default CheckoutBuilderAdvantageComponent;
