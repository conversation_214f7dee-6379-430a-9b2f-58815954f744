import { Grid, Paper } from '@mui/material';
import { Box, alpha } from '@mui/system';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';

const CheckoutBuilderRowCard = ({ draggable = true, layout, ...props }) => {
  const [{ isDragging }, drag, preview] = useDrag({
    item: () => ({
      layout,
      new: true,
    }),
    type: 'new-row',
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: () => draggable,
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 1,
        cursor: 'grab',
        width: 1,
      }}
      ref={drag}
      {...props}
    >
      <Grid container spacing={1}>
        {layout.map((col, index) => (
          <Grid item xs={col} key={index}>
            <Box
              sx={{
                transition: 'all 0.2s ease-in-out',
                bgcolor: 'background.neutral',
                borderRadius: 1,
                height: 100,
                color: 'grey.600',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                ...(isDragging && {
                  bgcolor: (theme) => alpha(theme.palette.primary.main, 0.08),
                  color: 'primary.main',
                }),
              }}
            >
              {index + 1}
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

CheckoutBuilderRowCard.propTypes = {
  layout: PropTypes.object,
  draggable: PropTypes.bool,
};

export { CheckoutBuilderRowCard };
