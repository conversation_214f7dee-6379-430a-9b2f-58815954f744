import { Card, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import { Stack } from '@mui/system';
import { AnimatePresence, motion, useIsPresent } from 'framer-motion';
import PropTypes from 'prop-types';
import { useWatch } from 'react-hook-form';
import Image from '../../../components/image';
import { CheckoutBuilderChatProviders } from '../form/CheckoutBuilderChatComponentForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderChatComponent = () => {
  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const provider = useWatch({
    name: getAttributeName('chat', 'provider'),
  });

  return (
    <Stack width={1} height={1} p={1} alignItems="flex-end" justifyContent="flex-end">
      <AnimatePresence>
        <ChatProviderCard key={provider} provider={provider} />
      </AnimatePresence>
    </Stack>
  );
};

export const ChatProviderCard = ({ provider }) => {
  const present = useIsPresent();

  const { label } = CheckoutBuilderChatProviders[provider] || {};

  const animations = {
    style: {
      position: present ? 'static' : 'absolute',
    },
    initial: {
      scale: 0,
      opacity: 0,
    },
    animate: {
      scale: 1,
      opacity: 1,
    },
    exit: {
      scale: 0,
      opacity: 0,
    },
    transition: {
      type: 'spring',
      stiffness: 500,
      damping: 40,
    },
  };

  return (
    <Card
      component={motion.div}
      {...animations}
      layout
      color="primary"
      sx={{
        width: 300,
        p: 1,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <ListItem>
        <ListItemIcon>
          <Image
            src={`/assets/icons/logos/${provider}.png`}
            sx={{
              width: 35,
              height: 35,
              alignSelf: 'center',
            }}
            alt={provider}
          />
        </ListItemIcon>

        <ListItemText primary={label} secondary="O chat aparecerá aqui" />
      </ListItem>
    </Card>
  );
};

ChatProviderCard.propTypes = {
  provider: PropTypes.string.isRequired,
};

export default CheckoutBuilderChatComponent;
