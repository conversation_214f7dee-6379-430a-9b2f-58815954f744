import { Box } from '@mui/system';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { CheckoutBuilderRowCard } from './CheckoutBuilderRowCard';

const CheckoutBuilderRowDragLayer = ({ visible, layout, ...props }) => {
  const variants = {
    hidden: {
      opacity: 0,
      height: 0,
    },
    visible: {
      opacity: 1,
      height: 'auto',
    },
  };
  return (
    <Box
      width={1}
      component={motion.div}
      initial="hidden"
      animate={visible ? 'visible' : 'hidden'}
      variants={variants}
      transition={{ duration: 0.2 }}
      my={visible ? 0.5 : 0}
      {...props}
    >
      <CheckoutBuilderRowCard layout={layout} />
    </Box>
  );
};

CheckoutBuilderRowDragLayer.propTypes = {
  visible: PropTypes.bool,
  layout: PropTypes.array,
};

export default CheckoutBuilderRowDragLayer;
