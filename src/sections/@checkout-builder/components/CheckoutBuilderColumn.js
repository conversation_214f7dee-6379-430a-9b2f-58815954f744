import { Fade, Paper, Typography, alpha } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { AnimatePresence } from 'framer-motion';
import PropTypes from 'prop-types';
import { useContext } from 'react';
import { useDrop } from 'react-dnd';
import { useFormContext, useWatch } from 'react-hook-form';
import { v4 } from 'uuid';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderContext } from '../../../contexts/CheckoutBuilderContext';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { FormScopes } from '../../../utils/form';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import useDragAndDropRows from '../hooks/useDragAndDropRowsHook';
import CheckoutBuilderDragLayer from './CheckoutBuilderDragLayer';
import CheckoutBuilderDraggableComponent from './CheckoutBuilderDraggableComponent';

const CheckoutBuilderColumn = ({ rowIndex, columnIndex, ...props }) => {
  const { setScope } = useContext(CheckoutBuilderContext);

  const { setSelectedID } = useContext(SelectedElementContext);

  const { moveComponent, insertComponent, removeComponent } = useDragAndDropRows();

  const form = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const {
    column: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index: {
      row: rowIndex,
      column: columnIndex,
    },
  });

  const components =
    useWatch({
      name: getAttributeName('components'),
    }) || [];

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ['component', 'new-component'],
    drop: (item, monitor) => {
      if (monitor.didDrop()) {
        return;
      }

      if (monitor.getItemType() === 'new-component') {
        const payload = {
          id: v4(),
          type: item.type,
          attributes: {
            ...CheckoutBuilderComponentTypes[item.type]?.attributes,
          },
        };

        requestAnimationFrame(() => {
          insertComponent({
            to: {
              rowIndex,
              columnIndex,
              componentIndex: components.length,
            },
            payload,
          });
        });

        return;
      }

      requestAnimationFrame(() => {
        moveComponent({
          from: {
            rowIndex: item.rowIndex,
            columnIndex: item.columnIndex,
            componentIndex: item.componentIndex,
          },
          to: {
            rowIndex,
            columnIndex,
            componentIndex: components.length,
          },
        });
      });
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({
        shallow: true,
      }),
      canDrop: monitor.canDrop(),
    }),
    canDrop: (item) => true,
  });

  return (
    <Stack
      ref={drop}
      component={Paper}
      display="flex"
      sx={{
        width: 1,
        height: 1,
        p: 0.5,
        border: '2px solid transparent',
        backgroundColor: 'transparent',
        transition: 'all .5s ease',
        ...(components.length === 0 && {
          border: (theme) => `2px dashed ${alpha(theme.palette.grey[500], 0.5)}`,
          backgroundColor: (theme) => theme.palette.background.paper,
          alignItems: 'center',
          justifyContent: 'center',
          ...(isOver &&
            canDrop && {
              border: (theme) => `2px dashed ${alpha(theme.palette.primary.main, 0.5)}`,
              backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.05),
            }),
        }),

        ...(components.length === 0 && {
          minHeight: 164,
        }),
        boxSizing: 'border-box !important',
        borderRadius: 1,
        gap: 1,
      }}
      position="relative"
      {...props}
    >
      {!components.length && (
        <>
          <Fade in={!isOver}>
            <Box>
              {!components.length && !isOver && (
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  Arraste componentes aqui
                </Typography>
              )}
            </Box>
          </Fade>
          <Fade in={isOver && canDrop}>
            <Box>
              {isOver && canDrop && (
                <Iconify
                  icon="material-symbols:library-add"
                  color="primary.light"
                  sx={{
                    width: 50,
                    height: 50,
                  }}
                />
              )}
            </Box>
          </Fade>
        </>
      )}

      <AnimatePresence>
        {components.map((component, componentIndex) => (
          <CheckoutBuilderDraggableComponent
            key={component.id}
            type={component.type}
            id={component.id}
            rowIndex={rowIndex}
            columnIndex={columnIndex}
            componentIndex={componentIndex}
            onRemove={() => {
              setScope(FormScopes.INDEX);
              setSelectedID(null);
              removeComponent({
                from: {
                  rowIndex,
                  columnIndex,
                  componentIndex,
                },
              });
            }}
            onCopy={() => {
              const copied = form.getValues(
                `${device}.rows[${rowIndex}].columns[${columnIndex}].components[${componentIndex}]`
              );
              insertComponent({
                to: {
                  rowIndex,
                  columnIndex,
                  componentIndex: componentIndex + 1,
                },
                payload: {
                  ...copied,
                  id: v4(),
                },
              });
            }}
            attributes={component.attributes}
          />
        ))}
      </AnimatePresence>

      <CheckoutBuilderDragLayer visible={isOver && components.length > 0} />
    </Stack>
  );
};

CheckoutBuilderColumn.propTypes = {
  children: PropTypes.node.isRequired,
  rowIndex: PropTypes.number.isRequired,
  columnIndex: PropTypes.number.isRequired,
};

export default CheckoutBuilderColumn;
