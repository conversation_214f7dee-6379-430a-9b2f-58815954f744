import { Fade, Grid, IconButton } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { motion, useIsPresent } from 'framer-motion';
import PropTypes from 'prop-types';
import { useContext, useEffect, useMemo } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useWatch, useFormContext } from 'react-hook-form';
import { v4 } from 'uuid';
import ConfirmationIconButton from '@/components/buttons/ConfirmationIconButton';
import Iconify from '@/components/iconify';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { SelectedElementContext } from '@/contexts/SelectedElementContext';
import { FormScopes } from '@/utils/form';
import useResponsive from '@/hooks/useResponsive';
import { CheckoutBuilderComponentTypes } from '../constants';
import useDragAndDropRows from '../hooks/useDragAndDropRowsHook';
import CheckoutBuilderColumn from './CheckoutBuilderColumn';
import CheckoutBuilderRowDragLayer from './CheckoutBuilderRowDragLayer';

const CheckoutBuilderDraggableRow = ({ id, rowIndex, onCopy, onDelete, ...props }) => {
  const { setScope, hovered, setHovered } = useContext(CheckoutBuilderContext);

  const { selectedID, setSelectedID } = useContext(SelectedElementContext);

  const isSelected = selectedID === id;

  const isHovered = hovered?.rowIndex === rowIndex && hovered?.type === 'row';

  const form = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const layout =
    useWatch({
      name: `${device}.rows[${rowIndex}].layout`,
    }) ?? [];

  // eslint-disable-next-line
  const columns =
    useWatch({
      name: `${device}.rows[${rowIndex}].columns`,
    }) ?? [];

  const { moveRow, insertRow } = useDragAndDropRows();

  const [{ isOver, canDrop, draggedRow }, drop] = useDrop({
    accept: ['row', 'new-row'],
    drop: (item, monitor) => {
      if (monitor.didDrop()) {
        return;
      }

      if (monitor.getItemType() === 'new-row') {
        insertRow({
          to: {
            rowIndex,
          },
          payload: {
            id: v4(),
            type: 'row',
            layout: item.layout,
            columns: item.layout.map(() => ({
              id: v4(),
              type: 'column',
              components: [],
            })),
          },
        });

        return;
      }

      moveRow({
        from: {
          rowIndex: item.rowIndex,
        },
        to: {
          rowIndex,
        },
      });
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
      draggedRow: monitor.getItem(),
    }),
    canDrop: (item) => id !== item.id,
  });

  const [{ isDragging }, drag, preview] = useDrag({
    item: () => ({
      id,
      rowIndex,
      layout,
    }),
    type: 'row',
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  const isMobile = useResponsive('down', 'sm');

  const onSettingsClick = (e) => {
    e.stopPropagation();
    setSelectedID(id);
    setScope(isMobile ? FormScopes.OPEN_SETTINGS : FormScopes.EDIT);
  };

  const hasCheckoutInColumns = useMemo(
    () =>
      columns.some((column) =>
        column.components.some(
          (component) => component.type === CheckoutBuilderComponentTypes.checkout.id
        )
      ),
    [columns]
  );

  const present = useIsPresent();

  const desktopRows = form.getValues('desktop.rows');
  const lastDesktopRowId = desktopRows[desktopRows.length - 1]?.id;

  const mobileRows = form.getValues('mobile.rows');
  const lastMobileRowId = mobileRows[mobileRows.length - 1]?.id;

  const lastRowId = device === 'desktop' ? lastDesktopRowId : lastMobileRowId;

  return (
    <Stack
      component={motion.div}
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
      exit={{
        opacity: 0,
      }}
      transition={{
        type: 'spring',
        stiffness: 400,
        damping: 30,
      }}
      layout="preserve-aspect"
      width={1}
      position={present ? 'static' : 'absolute'}
      ref={(node) => drop(node)}
      p={1}
      onMouseEnter={() => {
        setHovered({
          id,
          type: 'row',
          rowIndex,
        });
      }}
      onMouseLeave={() => {
        setHovered(null);
      }}
      onClick={() => {
        setSelectedID(id);
        setScope(FormScopes.EDIT);
      }}
    >
      <CheckoutBuilderRowDragLayer
        layout={draggedRow?.layout ?? layout}
        visible={isOver && canDrop}
      />
      <Grid
        container
        width={1}
        className="draggable-row"
        {...props}
        sx={{
          transition: 'all 0.2s ease-in-out',
          ...(isDragging && {
            opacity: 0.5,
          }),
          border: (theme) => {
            if (isSelected) {
              return `2px solid ${theme.palette.primary.main}`;
            }
            if (isHovered) {
              return `2px solid ${theme.palette.grey[500]}`;
            }
            return '2px solid transparent';
          },
          position: 'relative',
          borderRadius: 1,
          boxSizing: 'border-box !important',
          p: 1,
        }}
      >
        {layout.map((col, columnIndex) => (
          <Grid key={col.id} item xs={col}>
            <Box
              sx={{
                ...(layout.length > 1 && {
                  pl: columnIndex > 0 ? 0.5 : 0,
                  pr: columnIndex < layout.length - 1 ? 0.5 : 0,
                }),
              }}
              height={1}
            >
              <CheckoutBuilderColumn
                id={columns[columnIndex]?.id}
                rowIndex={rowIndex}
                columnIndex={columnIndex}
              />
            </Box>
          </Grid>
        ))}
        <Fade in={isSelected || isHovered}>
          <Box>
            {(isSelected || isHovered) && (
              <Stack
                direction="row"
                position="absolute"
                top={0}
                right={0}
                borderRadius={0.5}
                sx={{
                  backgroundColor: (theme) =>
                    isSelected ? theme.palette.primary.main : theme.palette.grey[500],
                }}
              >
                {!hasCheckoutInColumns && (
                  <>
                    <ConfirmationIconButton color="inherit" onConfirm={onDelete}>
                      <Iconify
                        icon="ion:trash"
                        sx={{
                          color: 'white',
                        }}
                      />
                    </ConfirmationIconButton>
                    <IconButton color="inherit" onClick={onCopy}>
                      <Iconify
                        icon="ion:copy"
                        sx={{
                          color: 'white',
                        }}
                      />
                    </IconButton>
                  </>
                )}
                <IconButton color="inherit" onClick={onSettingsClick}>
                  <Iconify
                    icon="ion:settings"
                    sx={{
                      color: 'white',
                    }}
                  />
                </IconButton>
                <IconButton
                  color="inherit"
                  sx={{ cursor: 'grab' }}
                  ref={(node) => drag(node)}
                  disabled={!isSelected ? true : lastRowId === selectedID}
                >
                  <Iconify
                    icon="ri:drag-move-2-fill"
                    sx={{
                      color: 'white',
                    }}
                  />
                </IconButton>
              </Stack>
            )}
          </Box>
        </Fade>
      </Grid>
    </Stack>
  );
};

CheckoutBuilderDraggableRow.propTypes = {
  children: PropTypes.node.isRequired,
  rowIndex: PropTypes.number.isRequired,
  onCopy: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  id: PropTypes.string.isRequired,
};

export default CheckoutBuilderDraggableRow;
