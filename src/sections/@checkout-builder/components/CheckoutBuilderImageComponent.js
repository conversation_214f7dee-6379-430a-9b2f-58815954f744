import { Box } from '@mui/material';
import PropTypes from 'prop-types';
import { useContext, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import ResizableCard from '../../../components/cards/ResizableCard';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { useImageAspectRatio } from '../../../hooks/useImageAspectRatio';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const FlexAlignments = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
};

const CheckoutBuilderImageComponent = ({
  id,
  attributes: { image, width, alignment, redirectUrl } = {},
}) => {
  const { selectedID, selected } = useContext(SelectedElementContext);

  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index: selected.index,
  });

  const isSelected = selectedID === id;

  const ref = useRef();

  const imgRef = useRef();

  const { aspectRatio } = useImageAspectRatio({ src: image?.preview });

  return (
    <ComponentWrapper
      direction="row"
      justifyContent={FlexAlignments[alignment]}
      alignItems="center"
      position="relative"
      gap={1}
      ref={ref}
    >
      <ResizableCard
        key={`image-${id}-${aspectRatio}`}
        width={width || 240}
        aspectRatio={aspectRatio || 1}
        enabled={isSelected}
        onResizeStop={(event, direction, _, delta, position) => {
          if (!delta.width) {
            return;
          }
          form.setValue(getAttributeName('width'), imgRef.current.offsetWidth || 0, {
            shouldValidate: true,
            shouldDirty: true,
          });
        }}
      >
        <Box
          ref={imgRef}
          width={1}
          height={1}
          component={redirectUrl && isSelected ? 'a' : 'div'}
          href={redirectUrl}
          target="_blank"
          display="flex"
          justifyContent="center"
          alignItems="center"
        >
          <img
            src={image?.preview || '/assets/images/checkout/empty-image.svg'}
            alt="checkout-builder"
            style={{
              width: image?.preview ? '100%' : 120,
              height: image?.preview ? '100%' : 120,
              objectFit: 'contain',
            }}
          />
        </Box>
      </ResizableCard>
    </ComponentWrapper>
  );
};

CheckoutBuilderImageComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
};

export default CheckoutBuilderImageComponent;
