import { Box, Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useFormContext } from 'react-hook-form';
import { useMemo, useState } from 'react';
import Image from '../../../components/image/Image';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const CheckoutBuilderHeaderComponent = ({
  attributes: {
    backgroundType,
    backgroundColor,
    backgroundImage,
    productImageType,
    productImage,
    productImageAlignment,
    titleFontSize,
    titleTextColor,
    titleText,
    showSubtitle,
    subtitleFontSize,
    subtitleTextColor,
    subtitleText,
  } = {},
  index,
}) => {
  const productImagePreview = useMemo(() => {
    const emptyImage = '/assets/images/checkout/empty-image.svg';
    if (productImageType === 'same') {
      return emptyImage;
    }
    if (productImageType === 'custom') {
      return productImage?.preview || emptyImage;
    }
    return null;
  }, [productImageType, productImage]);

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [editableTitle] = useState(titleText);
  const [editableSubTitle] = useState(subtitleText);

  const form = useFormContext();

  const handleChangeBoxText = (e) => {
    const { textContent } = e.target;
    const name = e.target.getAttribute('name');
    form.setValue(getAttributeName(name), textContent);
  };

  return (
    <ComponentWrapper
      width={1}
      sx={{
        backgroundImage: backgroundType === 'image' ? `url(${backgroundImage?.preview})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundColor: backgroundType === 'color' ? backgroundColor : 'transparent',
        minHeight: 120,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
        flexDirection: productImageAlignment === 'left' ? 'row' : 'row-reverse',
        gap: 2,
        p: 2,
        px: 20,
      }}
    >
      {productImageType !== 'none' && (
        <Box maxWidth="25%">
          <Image
            src={productImagePreview}
            sx={{
              width: 1,
              height: 1,
              objectFit: 'contain',
            }}
          />
        </Box>
      )}
      <Stack justifyContent="center" alignItems="flex-start" gap={1} flex={1}>
        <Box
          sx={{
            fontSize: `${titleFontSize || 24}px`,
            color: titleTextColor || '#000000',
            fontWeight: 'bold',
            outline: 'none',
            wordBreak: 'break-word',
          }}
          contenteditable="true"
          name="titleText"
          onInput={handleChangeBoxText}
        >
          {editableTitle}
        </Box>
        {showSubtitle && (
          <Box
            sx={{
              fontSize: `${subtitleFontSize || 14}px`,
              color: subtitleTextColor || '#000000',
              outline: 'none',
              wordBreak: 'break-word',
            }}
            contenteditable="true"
            name="subtitleText"
            onInput={handleChangeBoxText}
          >
            {editableSubTitle}
          </Box>
        )}
      </Stack>
    </ComponentWrapper>
  );
};

CheckoutBuilderHeaderComponent.propTypes = {
  attributes: PropTypes.object,
  index: PropTypes.number,
};

export default CheckoutBuilderHeaderComponent;
