import { Typography, alpha } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';

const CheckoutBuilderDragLayer = ({ visible, ...props }) => {
  const variants = {
    hidden: {
      opacity: 0,
      height: 0,
    },
    visible: {
      opacity: 1,
      height: 'auto',
    },
  };
  return (
    <Box
      width={1}
      component={motion.div}
      initial="hidden"
      animate={visible ? 'visible' : 'hidden'}
      variants={variants}
      transition={{ duration: 0.2 }}
      my={visible ? 0.5 : 0}
      {...props}
    >
      <Stack
        alignItems="center"
        width={1}
        p={2}
        borderRadius={1}
        position="relative"
        bgcolor={(theme) => alpha(theme.palette.primary.light, 0.05)}
        border={(theme) => `2px dashed ${theme.palette.primary.lighter}`}
      >
        <Typography variant="caption" color="primary.lighter" textAlign="center">
          Soltar aqui
        </Typography>
      </Stack>
    </Box>
  );
};

CheckoutBuilderDragLayer.propTypes = {
  visible: PropTypes.bool,
};

export default CheckoutBuilderDragLayer;
