import { Box } from '@mui/material';
import PropTypes from 'prop-types';
import { memo } from 'react';
import { useFormContext } from 'react-hook-form';
import BubbleEditor from '@/components/editor/BubbleEditor';
import ComponentWrapper from './ComponentWrapper';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderTextComponent = memo(
  ({
    id,
    attributes: { text, textColor, backgroundColor, borderColor, borderWidth, borderRadius } = {},
    index,
  }) => {
    const {
      component: { getAttributeName },
    } = useCheckoutBuilderFormHelper({
      index,
    });

    const form = useFormContext();

    const ContainerProps = {
      color: textColor,
      backgroundColor,
      borderColor,
      borderWidth: borderWidth ? `${borderWidth}px` : undefined,
      borderRadius: borderRadius ? `${borderRadius}px` : undefined,
      borderStyle: 'solid',
    };

    return (
      <ComponentWrapper sx={ContainerProps}>
        <BubbleEditor
          name={id || 'text'}
          value={text || ''}
          onChange={(value) => {
            form.setValue(getAttributeName('text'), value);
          }}
        />
      </ComponentWrapper>
    );
  }
);

CheckoutBuilderTextComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
  index: PropTypes.object,
};

export default CheckoutBuilderTextComponent;
