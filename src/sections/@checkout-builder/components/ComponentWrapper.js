import PropTypes from 'prop-types';
import { Stack } from '@mui/material';
import { useFormContext } from 'react-hook-form';

export default function ComponentWrapper({ children, ...rest }) {
  const form = useFormContext();

  const isMobileSelection = form.getValues('device') === 'mobile';

  const desktopRows = form.getValues('desktop.rows');
  const lastDesktopRows = desktopRows[desktopRows.length - 1]?.layout;

  const rowsToBreak = ['[4,4,4]', '[4,8]'];

  const hasBreakRows = rowsToBreak.includes(JSON.stringify(lastDesktopRows));

  const getWidth = () => {
    if (JSON.stringify(lastDesktopRows) === `[8,4]`) return 'w-full';

    if (!isMobileSelection && !hasBreakRows) {
      return 'w-[70%]';
    }

    return 'w-[100%]';
  };

  return (
    <div className={`${!hasBreakRows && 'flex justify-center items-center'}`}>
      <Stack className={getWidth()} {...rest}>
        {children}
      </Stack>
    </div>
  );
}

ComponentWrapper.propTypes = {
  children: PropTypes.node,
};
