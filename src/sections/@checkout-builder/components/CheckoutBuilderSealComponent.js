import { Stack } from '@mui/system';
import { parseInt } from 'lodash';
import PropTypes from 'prop-types';
import { useContext } from 'react';
import { useFormContext } from 'react-hook-form';
import ResizableCard from '../../../components/cards/ResizableCard';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { CheckoutBuilderSealTypes } from '../form/CheckoutBuilderSealComponentForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const FlexAlignments = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
};

const CheckoutBuilderSealComponent = ({
  id,
  attributes: {
    width = 250,
    topText,
    title,
    subtitle,
    primaryColor,
    titleTextColor,
    type,
    alignment,
    darkMode,
  } = {},
  index,
}) => {
  const Icon = CheckoutBuilderSealTypes[type]?.Icon || CheckoutBuilderSealTypes.one.Icon;

  const { selected } = useContext(SelectedElementContext);

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const form = useFormContext();

  return (
    <ComponentWrapper
      justifyContent={FlexAlignments[alignment]}
      alignItems="center"
      direction="row"
      width={1}
      px={20}
    >
      <ResizableCard
        width={width}
        aspectRatio={14 / 11}
        enabled={selected?.id === id}
        onResizeStop={(event, direction, _, delta, position) => {
          form.setValue(getAttributeName('width'), parseInt(width) + delta.width, {
            shouldValidate: true,
            shouldDirty: true,
          });
        }}
      >
        <Icon
          topText={topText}
          title={title}
          subtitle={subtitle}
          primaryColor={primaryColor}
          titleTextColor={titleTextColor}
          darkMode={darkMode}
        />
      </ResizableCard>
    </ComponentWrapper>
  );
};

CheckoutBuilderSealComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
  index: PropTypes.object,
};

export default CheckoutBuilderSealComponent;
