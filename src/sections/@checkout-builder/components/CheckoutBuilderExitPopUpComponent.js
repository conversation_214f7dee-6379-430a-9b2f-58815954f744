import { <PERSON>, <PERSON><PERSON>, Card, IconButton, Typography } from '@mui/material';
import { useState, useEffect } from 'react';
import { Stack, alpha } from '@mui/system';
import PropTypes from 'prop-types';
import { useWatch } from 'react-hook-form';
import ReactPlayer from 'react-player';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderExitPopUpTypes } from '../form/CheckoutBuilderExitPopUpComponentForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderExitPopUpComponent = ({ onClose }) => {
  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const [
    type,
    image,
    video,
    title,
    description,
    actionLabel,
    actionOnClick,
    url,
    offer,
    backgroundButtonColor,
    textButtonColor,
  ] = useWatch({
    name: [
      getAttributeName('exitPopup', 'type'),
      getAttributeName('exitPopup', 'image'),
      getAttributeName('exitPopup', 'video'),
      getAttributeName('exitPopup', 'title'),
      getAttributeName('exitPopup', 'description'),
      getAttributeName('exitPopup', 'actionLabel'),
      getAttributeName('exitPopup', 'actionOnClick'),
      getAttributeName('exitPopup', 'url'),
      getAttributeName('exitPopup', 'offer'),
      getAttributeName('exitPopup', 'backgroundButtonColor'),
      getAttributeName('exitPopup', 'textButtonColor'),
    ],
  });
  const [actionUrl, setActionUrl] = useState();

  useEffect(() => {
    if (actionOnClick === 'offer') {
      setActionUrl(`${process.env.REACT_APP_CHECKOUT_URL}/${offer}`);
      return;
    }

    if (actionOnClick === 'redirect') {
      setActionUrl(url);
      return;
    }

    setActionUrl('close');
  }, [actionOnClick, offer, url]);

  const handleClick = () => {
    if (actionUrl === 'close') {
      onClose();
    } else {
      document.getElementById('cakto-open-exit-popup').click();
    }
  };

  return (
    <Card
      sx={{
        width: 600,
        maxWidth: '100%',
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <Stack direction="row">
        {[
          CheckoutBuilderExitPopUpTypes.image.id,
          CheckoutBuilderExitPopUpTypes.imageAndText.id,
        ].includes(type) && (
          <Stack
            sx={{
              minWidth: type === CheckoutBuilderExitPopUpTypes.imageAndText.id ? '35%' : 1,
              maxWidth: type === CheckoutBuilderExitPopUpTypes.imageAndText.id ? '35%' : 1,
              minHeight: 300,
              flex: 1,
              position: 'relative',
              backgroundColor: (theme) => alpha(theme.palette.grey[400], 0.6),
              justifyContent: 'center',
              alignItems: 'center',
              ...(type === CheckoutBuilderExitPopUpTypes.image.id && {
                aspectRatio: '1/1',
              }),
              ...([
                CheckoutBuilderExitPopUpTypes.imageAndText.id,
                CheckoutBuilderExitPopUpTypes.image.id,
              ].includes(type) && {
                backgroundImage: image?.preview ? `url(${image?.preview})` : undefined,
                backgroundPositionX: 'center',
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
              }),
            }}
          >
            {!image && (
              <Iconify
                icon="ph:image-duotone"
                sx={{
                  width: '50%',
                  height: '50%',
                  color: 'text.disabled',
                }}
              />
            )}
            {type === 'image' && (
              <Button
                variant="contained"
                size="large"
                style={{
                  position: 'absolute',
                  bottom: 20,
                  background: backgroundButtonColor,
                  color: textButtonColor,
                  width: '90%',
                }}
                onClick={handleClick}
              >
                {actionLabel}
              </Button>
            )}
          </Stack>
        )}

        <Stack p={3} pt={5} gap={2} justifyContent="center" alignItems="start" width={1}>
          {type === CheckoutBuilderExitPopUpTypes.video.id && (
            <Stack gap={1} width={1}>
              <Typography
                variant="h4"
                sx={{
                  wordBreak: 'break-word',
                }}
              >
                {title}
              </Typography>
              <Stack
                width={1}
                justifyContent="center"
                alignItems="center"
                backgroundColor={(theme) => alpha(theme.palette.grey[400], 0.6)}
                borderRadius={1}
              >
                {video ? (
                  <Box
                    width={1}
                    sx={{
                      aspectRatio: '16/9',
                    }}
                  >
                    <ReactPlayer width="100%" height="100%" url={video} controls playing />
                  </Box>
                ) : (
                  <Iconify
                    icon="mdi:video-outline"
                    sx={{
                      width: '50%',
                      height: '50%',
                      color: 'text.disabled',
                    }}
                  />
                )}
              </Stack>
            </Stack>
          )}

          {type === CheckoutBuilderExitPopUpTypes.imageAndText.id && (
            <>
              <Typography
                variant="h4"
                sx={{
                  wordBreak: 'break-word',
                }}
              >
                {title}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontStyle: 'italic',
                  wordBreak: 'break-word',
                }}
              >
                {description}
              </Typography>
            </>
          )}
          <Button
            variant="contained"
            size="large"
            style={{
              background: backgroundButtonColor,
              color: textButtonColor,
              width: '100%',
            }}
            onClick={handleClick}
          >
            {actionLabel}
          </Button>
        </Stack>

        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            ...([
              CheckoutBuilderExitPopUpTypes.image.id,
              CheckoutBuilderExitPopUpTypes.video.id,
            ].includes(type) && {
              alignSelf: 'center',
            }),
          }}
        >
          <Iconify
            icon="mdi:close"
            sx={{
              color: 'text.disabled',
            }}
          />
        </IconButton>
      </Stack>
      <a
        style={{ display: 'none' }}
        id="cakto-open-exit-popup"
        href={actionUrl}
        target="_blank"
        rel="noreferrer"
      >
        link
      </a>
    </Card>
  );
};

CheckoutBuilderExitPopUpComponent.propTypes = {
  onClose: PropTypes.func.isRequired,
};

export default CheckoutBuilderExitPopUpComponent;
