import { Typography } from '@mui/material';
import { Stack } from '@mui/system';
import * as datefns from 'date-fns';
import PropTypes from 'prop-types';
import { useEffect, useMemo, useRef, useState } from 'react';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderComponentTypes } from '../constants';
import ComponentWrapper from './ComponentWrapper';

const CheckoutBuilderCountdownComponent = ({ attributes = {} }) => {
  const [counter, setCounter] = useState(1);

  const { type, time, date, backgroundColor, textColor, activeText, finishedText } = attributes;

  const interval = useRef(null);

  const finished = counter <= 0;

  useEffect(() => {
    interval.current = setInterval(() => {
      setCounter((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (type === 'date' && date) {
      setCounter(datefns.differenceInSeconds(new Date(date), new Date(0)));
    }
    if (type === 'time' && time) {
      setCounter(
        datefns.differenceInSeconds(
          datefns.parse(time || '00:00', 'mm:ss', new Date(0)),
          new Date(0)
        )
      );
    }
  }, [type, time, date]);

  const text = useMemo(() => {
    if (finished) {
      return finishedText || CheckoutBuilderComponentTypes.countdown.attributes.finishedText;
    }
    return activeText || CheckoutBuilderComponentTypes.countdown.attributes.activeText;
  }, [activeText, finished, finishedText]);

  return (
    <ComponentWrapper
      direction="row"
      justifyContent="center"
      alignItems="center"
      minHeight={72}
      gap={2}
      borderRadius={1}
      p={2}
      bgcolor={backgroundColor || '#e55858ff'}
      flexWrap="wrap"
    >
      <Typography
        variant="h3"
        color={textColor || 'white'}
        sx={{
          minWidth: 48,
        }}
      >
        {datefns.format(datefns.addSeconds(new Date(0), counter), 'mm:ss')}
      </Typography>
      <Iconify icon="ion:alarm" sx={{ width: 48, height: 48, color: textColor || 'white' }} />
      <Typography variant="body1" fontWeight={400} color={textColor || 'white'} textAlign="center">
        {text}
      </Typography>
    </ComponentWrapper>
  );
};

CheckoutBuilderCountdownComponent.propTypes = {
  attributes: PropTypes.object,
};

export default CheckoutBuilderCountdownComponent;
