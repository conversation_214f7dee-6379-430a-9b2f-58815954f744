import { Box, Stack } from '@mui/system';
import { parseInt } from 'lodash';
import PropTypes from 'prop-types';
import { useContext, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import ReactPlayer from 'react-player';
import ResizableCard from '../../../components/cards/ResizableCard';
import Iconify from '../../../components/iconify/Iconify';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const FlexAlignments = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
};

const CheckoutBuilderVideoComponent = ({
  id,
  attributes: { width, url, alignment, hideControls } = {},
}) => {
  const { selectedID, selected } = useContext(SelectedElementContext);

  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index: selected.index,
  });

  const isSelected = selectedID === id;

  const ref = useRef();

  return (
    <ComponentWrapper
      direction="row"
      justifyContent={FlexAlignments[alignment]}
      alignItems="center"
      position="relative"
      gap={1}
      ref={ref}
      bgcolor={!url && 'background.neutral'}
    >
      {url ? (
        <ResizableCard
          width={width}
          aspectRatio={16 / 9}
          enabled={isSelected}
          onResizeStop={(event, direction, _, delta, position) => {
            form.setValue(getAttributeName('width'), parseInt(width) + delta.width, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
        >
          <Box overflow="hidden" width={1} height={1}>
            <ReactPlayer
              key={`${id}-${url}-${hideControls}`}
              width="100%"
              height="100%"
              url={url}
              controls={!hideControls}
            />
          </Box>
        </ResizableCard>
      ) : (
        <Stack width={1} height={300} justifyContent="center" alignItems="center">
          <Iconify
            icon={CheckoutBuilderComponentTypes.video.icon}
            sx={{
              width: 80,
              height: 80,
              color: 'text.disabled',
            }}
          />
        </Stack>
      )}
    </ComponentWrapper>
  );
};

CheckoutBuilderVideoComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
};

export default CheckoutBuilderVideoComponent;
