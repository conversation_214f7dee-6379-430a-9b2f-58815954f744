import { memo, useContext, useEffect, useMemo } from 'react';
import { Box, Typography, Link, Tooltip } from '@mui/material';
import { useWatch, useFormContext } from 'react-hook-form';
import {
  ProductDescription,
  Card,
  AboutCpf,
  PaymentMethods,
  OrderBumps,
  OrderSummary,
  PayButton,
  Input,
  ProductPurchase,
} from '@/components/checkout';
import Iconify from '@/components/iconify';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { CheckCircle } from '@mui/icons-material';
import CaktoLogoIcon from '@/assets/icons/CaktoLogoIcon';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import { CheckoutBuilderComponentTypes } from '../constants';

const CheckoutBuilderCheckoutComponent = () => {
  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const form = useFormContext();

  const { product } = useContext(CheckoutBuilderContext);

  const iconColor = useWatch({
    name: getAttributeName('icon.color'),
  });

  const backgroundColor = useWatch({
    name: getAttributeName('form.background.color'),
  });

  const payButtonColor = useWatch({
    name: getAttributeName('payButton.color'),
  });
  const payButtonTextColor = useWatch({
    name: getAttributeName('payButton.text.color'),
  });

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const secondaryColor = useWatch({
    name: getAttributeName('text.color.secondary'),
  });
  const activeColor = useWatch({
    name: getAttributeName('text.color.active'),
  });

  const paymentMethodsProps = {
    selectedTextColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.selected.text.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.selected.text
        .color,
    unselectedTextColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.unselected.text.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.unselected.text
        .color,
    selectedBackgroundColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.selected.background.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.selected
        .background.color,
    unselectedBackgroundColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.unselected.background.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.unselected
        .background.color,
    selectedIconColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.selected.icon.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.selected.icon
        .color,
    unselectectedIconColor:
      useWatch({
        name: getAttributeName('paymentOptions.button.unselected.icon.color'),
      }) ||
      CheckoutBuilderComponentTypes.checkout.config.settings.paymentOptions.button.unselected.icon
        .color,
  };

  const isMobile = form.getValues('device') === 'mobile';

  const desktopRows = form.getValues('desktop.rows');
  const lastDesktopRows = desktopRows[desktopRows.length - 1]?.layout;

  const rowsToBreak = ['[4,4,4]', '[4,8]'];

  const hasBreakRows = rowsToBreak.includes(JSON.stringify(lastDesktopRows));

  const getWidth = () => {
    if (JSON.stringify(lastDesktopRows) === `[8,4]`) return 'w-full';

    if (!isMobile && !hasBreakRows) {
      return 'w-[70%]';
    }

    return 'w-[100%]';
  };

  useEffect(() => {
    if (!backgroundColor) {
      form.setValue(
        getAttributeName('form.background.color'),
        CheckoutBuilderComponentTypes.checkout.config.settings.form.background.color
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getContainerClassName = () => {
    if (isMobile) return '';

    if (JSON.stringify(lastDesktopRows) === '[12]') return 'grid grid-cols-6  gap-4';

    return '';
  };

  const getFormClassName = () => {
    if (isMobile) return '';

    if (JSON.stringify(lastDesktopRows) === '[12]') {
      return 'flex w-[1000px] justify-start items-end col-span-4';
    }

    if (!hasBreakRows) return 'flex  justify-center items-center ';

    return '';
  };

  const isProductPurchaseVisible = useMemo(() => {
    if (!isMobile) {
      return JSON.stringify(lastDesktopRows) === '[12]';
    }

    return false;
  }, [isMobile, lastDesktopRows]);

  return (
    <div className={getContainerClassName()}>
      <div className={getFormClassName()}>
        <Box
          className={getWidth()}
          position="relative"
          sx={{
            aspectRatio: '0/0.73',
            objectFit: 'cover',
            borderRadius: 2,
            backgroundColor,
          }}
        >
          <Card className={`p-5 w-[100%] bg-[${backgroundColor}]`}>
            <div className="flex w-full flex-col items-start gap-3">
              <ProductDescription
                primaryColor={primaryColor}
                secondaryColor={secondaryColor}
                activeColor={activeColor}
              />
              <div className="flex items-center my-4 font-bold">
                <Iconify
                  style={{ color: iconColor }}
                  icon="mdi-account-circle"
                  width={20}
                  height={20}
                  className="mr-2"
                />
                <Typography fontWeight="bold" fontSize={16} style={{ color: primaryColor }}>
                  Seus dados
                </Typography>
              </div>

              <Input value="Nome do comprador" label="Nome completo" />

              <div className={`flex w-full  ${isMobile ? 'flex-col gap-3' : 'flex-row gap-4'}`}>
                <Input value="<EMAIL>" label="Email" />
                {product?.confirmEmail && (
                  <Input value="<EMAIL>" label="Confirme seu e-mail" />
                )}
              </div>

              <div
                className={`flex w-full ${
                  isMobile || hasBreakRows ? 'flex-col' : 'flex-row'
                } gap-4`}
              >
                <Input value="304.761.160-23" label="CPF" />

                <Input value="+55 (99) 99999-9999" label="Celular" isCellphone />
              </div>

              <AboutCpf />

              <section className="mt-3 w-full">
                <div className="flex items-center my-4 font-bold">
                  <Iconify
                    style={{ color: iconColor }}
                    icon="heroicons:banknotes-20-solid"
                    width={20}
                    height={20}
                    className="mr-2"
                  />
                  <Typography fontWeight="bold" fontSize={16} style={{ color: primaryColor }}>
                    Pagamento
                  </Typography>
                </div>
                <PaymentMethods
                  iconColor={iconColor}
                  backgroundColor={backgroundColor}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  isMobile={isMobile}
                  lastDesktopRows={lastDesktopRows}
                  {...paymentMethodsProps}
                />
              </section>

              <OrderBumps />

              <section className="mt-3 w-full">
                <OrderSummary lastDesktopRows={lastDesktopRows} />
              </section>

              <section className="mt-3 flex w-full flex-row">
                <PayButton
                  payButtonColor={payButtonColor}
                  payButtonTextColor={payButtonTextColor}
                />
              </section>
            </div>

            <div className="flex flex-col justify-center gap-1 pt-6">
              <CaktoLogoIcon className="mx-auto w-20 grayscale" fill={secondaryColor} />

              <Typography
                variant="body2"
                style={{ color: primaryColor }}
                className="mt-3 text-center text-sm text-slate-400"
              >
                Cakto está processando este pagamento para o vendedor{' '}
                <Tooltip
                  title={`Enviar e-mail para ${product?.supportEmail}`}
                  placement="bottom-start"
                >
                  <Link
                    href={`mailto:${product?.supportEmail}`}
                    className="transititext-primary text-primary hover:text-primary-600 focus:text-primary-600 active:text-primary-700 dark:text-primary-400 dark:hover:text-primary-500 dark:focus:text-primary-500 dark:active:text-primary-600 transition duration-150 ease-in-out"
                    data-te-toggle="tooltip"
                    style={{ color: primaryColor, textDecoration: 'none' }}
                  >
                    {product?.producerName}
                  </Link>
                </Tooltip>
              </Typography>

              <Typography
                variant="caption"
                className="flex items-center justify-center mt-2 text-xs text-[#23CC67]"
              >
                <CheckCircle sx={{ width: 16 }} className="inline-block mr-1" />
                Compra 100% segura
              </Typography>

              <Typography
                variant="caption"
                style={{ color: primaryColor }}
                className="mt-3 text-center text-xs text-slate-400"
              >
                Este site é protegido pelo reCAPTCHA do Google
              </Typography>

              <Typography
                variant="caption"
                className="mt-1 text-center text-xs text-slate-400"
                style={{ color: primaryColor }}
              >
                <Link
                  className="font-bold"
                  href="https://cakto.com.br/politica-de-privacidade-2/"
                  style={{ color: primaryColor, fontWeight: 'bold', textDecoration: 'none' }}
                >
                  Política de privacidade
                </Link>{' '}
                e{' '}
                <Link
                  className="font-bold"
                  href="#"
                  style={{ color: primaryColor, fontWeight: 'bold', textDecoration: 'none' }}
                >
                  Termos de serviço
                </Link>
              </Typography>

              <Typography
                variant="caption"
                className="mt-1 text-center text-xs text-slate-400"
                style={{ color: primaryColor }}
              >
                * Parcelamento com acréscimo <br />
                <br />
                Ao continuar, você concorda com os{' '}
                <Link
                  className="font-bold"
                  href="#"
                  style={{ color: primaryColor, fontWeight: 'bold', textDecoration: 'none' }}
                >
                  Termos de Compra
                </Link>
              </Typography>
            </div>
          </Card>
        </Box>
      </div>

      {isProductPurchaseVisible && (
        <div className="block col-span-2">
          <ProductPurchase
            backgroundColor={backgroundColor}
            payButtonColor={payButtonColor}
            payButtonTextColor={payButtonTextColor}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
            activeColor={activeColor}
            product={product}
          />
        </div>
      )}
    </div>
  );
};

CheckoutBuilderCheckoutComponent.propTypes = {};

export default memo(CheckoutBuilderCheckoutComponent);
