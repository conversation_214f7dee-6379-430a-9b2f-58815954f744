import { Card, CardContent, Stack, Switch, Typography, alpha } from '@mui/material';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import Iconify from '../../../components/iconify';

const CheckoutBuilderComponentCard = ({
  draggable = true,
  config: { icon, name, id, done },
  enabled,
  onEnabledChange,
  ...props
}) => {
  const [{ isDragging }, drag, preview] = useDrag({
    item: () => ({
      type: id,
    }),
    type: 'new-component',
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: () => draggable,
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  return (
    <Card
      variant="outlined"
      sx={{
        transition: 'all 0.2s ease-in-out',
        width: 1,
        cursor: draggable ? 'grab' : 'pointer',
        ...(isDragging && {
          bgcolor: (theme) => alpha(theme.palette.primary.main, 0.04),
        }),
      }}
      className="checkout-builder-draggable-component"
      ref={drag}
      {...props}
    >
      <Stack component={CardContent} gap={1} alignItems="center" justifyContent="center">
        <Iconify
          icon={icon}
          sx={{ width: 32, height: 32, color: (theme) => theme.palette.grey[500] }}
        />
        <Typography variant="body2">{name}</Typography>
        {onEnabledChange && (
          <Switch
            size="medium"
            checked={enabled}
            onChange={(event) => onEnabledChange(event.target.checked)}
            onClick={(event) => event.stopPropagation()}
            sx={{
              mb: -1,
            }}
          />
        )}
      </Stack>
    </Card>
  );
};

CheckoutBuilderComponentCard.propTypes = {
  config: PropTypes.shape({
    id: PropTypes.string.isRequired,
    icon: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    done: PropTypes.bool,
  }).isRequired,
  enabled: PropTypes.bool,
  onEnabledChange: PropTypes.func,
  draggable: PropTypes.bool,
};

export default CheckoutBuilderComponentCard;
