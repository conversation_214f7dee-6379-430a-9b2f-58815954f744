import { Box, Stack } from '@mui/system';
import { parseInt } from 'lodash';
import PropTypes from 'prop-types';
import { useContext, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import ResizableCard from '../../../components/cards/ResizableCard';
import Iconify from '../../../components/iconify/Iconify';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import ComponentWrapper from './ComponentWrapper';

const FlexAlignments = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
};

const CheckoutBuilderMapComponent = ({ id, attributes: { address, alignment, width } = {} }) => {
  const { selectedID, selected } = useContext(SelectedElementContext);

  const form = useFormContext();

  const {
    component: { getAttributeName },
  } = useCheckoutBuilderFormHelper({
    index: selected.index,
  });

  const isSelected = selectedID === id;

  const src = useMemo(() => {
    const params = new URLSearchParams({
      q: address,
      key: process.env.REACT_APP_GOOGLE_API_KEY,
    });
    return `https://maps.google.com/maps/embed/v1/place?${params.toString()}`;
  }, [address]);

  return (
    <ComponentWrapper
      alignItems={FlexAlignments[alignment]}
      justifyContent="center"
      position="relative"
      gap={1}
      borderRadius={1}
    >
      {address && width ? (
        <ResizableCard
          width={width}
          aspectRatio={16 / 9}
          enabled={isSelected}
          onResizeStop={(event, direction, ref, delta, position) => {
            form.setValue(getAttributeName('width'), parseInt(width) + delta.width, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
        >
          <iframe
            width="100%"
            height="100%"
            title="map"
            loading="lazy"
            allowfullscreen
            referrerPolicy="no-referrer-when-downgrade"
            src={src}
            style={{
              ...(isSelected && {
                pointerEvents: 'none',
                display: 'block',
              }),
            }}
          />
        </ResizableCard>
      ) : (
        <Box
          width={1}
          height={1}
          display="flex"
          justifyContent="center"
          alignItems="center"
          borderRadius={1}
          minHeight={300}
          bgcolor="grey.200"
        >
          <Iconify
            icon="mdi:map-marker"
            color="grey.500"
            sx={{
              width: 120,
              height: 120,
            }}
          />
        </Box>
      )}
    </ComponentWrapper>
  );
};

CheckoutBuilderMapComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
};

export default CheckoutBuilderMapComponent;
