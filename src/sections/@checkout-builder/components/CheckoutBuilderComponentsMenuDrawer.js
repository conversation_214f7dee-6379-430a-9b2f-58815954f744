import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import {
  Box,
  Drawer,
  Grid,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Toolbar,
  Typography,
} from '@mui/material';
import { useContext, useState, useRef } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import DragHandleIcon from '@mui/icons-material/DragHandle';
import Scrollbar from '@/components/scrollbar/Scrollbar';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { SelectedElementContext } from '@/contexts/SelectedElementContext';
import { FormScopes } from '@/utils/form';
import useResponsive from '@/hooks/useResponsive';
import {
  CHECKOUT_BUILDER_DRAWER_WIDTH,
  CHECKOUT_BUILDER_DRAWER_HEIGHT,
  CheckoutBuilderComponentTypes,
  CheckoutBuilderDeviceTypes,
} from '../constants';
import CheckoutBuilderSettingsForm from '../form/CheckoutBuilderSettingsForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import CheckoutBuilderComponent from './CheckoutBuilderComponentCard';
import { CheckoutBuilderRowCard } from './CheckoutBuilderRowCard';

const CheckoutBuilderComponentsMenuDrawer = () => {
  const [tab, setTab] = useState('components');
  const [drawerHeight, setDrawerHeight] = useState(CHECKOUT_BUILDER_DRAWER_HEIGHT);

  const { setScope } = useContext(CheckoutBuilderContext);

  const { setSelectedID } = useContext(SelectedElementContext);

  const device = useWatch({
    name: 'device',
  });

  const isMobile = useResponsive('down', 'sm');

  const layouts = CheckoutBuilderDeviceTypes[device].layouts ?? [];

  const components = [
    CheckoutBuilderComponentTypes.text,
    CheckoutBuilderComponentTypes.image,
    CheckoutBuilderComponentTypes.advantage,
    CheckoutBuilderComponentTypes.seal,
    CheckoutBuilderComponentTypes.header,
    CheckoutBuilderComponentTypes.list,
    CheckoutBuilderComponentTypes.countdown,
    CheckoutBuilderComponentTypes.testimonial,
    CheckoutBuilderComponentTypes.video,
    CheckoutBuilderComponentTypes.facebook,
    CheckoutBuilderComponentTypes.map,
  ];

  const handleExtraComponentClick = ({ type }) => {
    setScope(FormScopes.EDIT);
    setSelectedID(type);
  };

  const {
    extra: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const form = useFormContext();

  const startY = useRef(0);
  const startHeight = useRef(drawerHeight);

  const handleTouchStart = (e) => {
    e.preventDefault();
    startY.current = e.touches[0].clientY;
    startHeight.current = drawerHeight;
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  };

  const handleTouchMove = (e) => {
    const deltaY = e.touches[0].clientY - startY.current;

    const newHeight = Math.max(100, Math.min(600, startHeight.current - deltaY));
    setDrawerHeight(newHeight);
  };

  const handleTouchEnd = () => {
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  };

  return (
    <div
      style={{
        height: drawerHeight,
        width: isMobile ? '100%' : CHECKOUT_BUILDER_DRAWER_WIDTH,
        position: 'relative',
      }}
    >
      <Drawer
        variant="permanent"
        anchor={isMobile ? 'bottom' : 'right'}
        sx={{
          width: isMobile ? '100%' : CHECKOUT_BUILDER_DRAWER_WIDTH,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: {
            width: isMobile ? '100%' : CHECKOUT_BUILDER_DRAWER_WIDTH,
            boxSizing: 'border-box',
            height: isMobile ? drawerHeight : undefined,
          },
        }}
      >
        {!isMobile && <Toolbar />}
        <Box sx={{ p: 1, mt: isMobile ? 2 : undefined }}>
          <TabContext value={tab}>
            <ToggleButtonGroup
              sx={{ width: 1, border: 'none' }}
              value={tab}
              exclusive
              onChange={(event, value) => setTab(value ?? tab)}
              color="primary"
            >
              <ToggleButton
                value="components"
                sx={{
                  width: 1,
                }}
              >
                Componentes
              </ToggleButton>
              <ToggleButton
                value="rows"
                sx={{
                  width: 1,
                }}
              >
                Linhas
              </ToggleButton>
              <ToggleButton
                value="settings"
                sx={{
                  width: 1,
                }}
              >
                Configurações
              </ToggleButton>
            </ToggleButtonGroup>

            <TabPanel value="components" sx={{ p: 1 }}>
              <Scrollbar
                sx={{ height: 'calc(100vh - 64px - 64px - 32px)', pr: 0.5 }}
                autoHide={false}
              >
                <Stack gap={2}>
                  <Stack width={1} gap={2}>
                    <Typography
                      variant="caption"
                      fontWeight={100}
                      color="text.secondary"
                      textAlign="center"
                    >
                      COMPONENTES
                    </Typography>
                    <Grid container spacing={1} width={1}>
                      {components.map((type) => (
                        <Grid key={type.id} item xs={6} sm={6}>
                          <CheckoutBuilderComponent config={type} />
                        </Grid>
                      ))}
                    </Grid>
                  </Stack>
                  <Stack width={1} gap={2} mt={2}>
                    <Typography
                      variant="caption"
                      fontWeight={100}
                      color="text.secondary"
                      textAlign="center"
                    >
                      COMPONENTES EXTRAS
                    </Typography>
                    <Grid container spacing={1} width={1}>
                      {[
                        CheckoutBuilderComponentTypes.exitPopup,
                        CheckoutBuilderComponentTypes.notification,
                        CheckoutBuilderComponentTypes.chat,
                      ].map(
                        (config) =>
                          config.devices.includes(device) && (
                            <Grid key={config.type} item xs={6} sm={6}>
                              <CheckoutBuilderComponent
                                config={config}
                                enabled={form.watch(getAttributeName(config.type, 'enabled'))}
                                onEnabledChange={() => {
                                  form.setValue(
                                    getAttributeName(config.type, 'enabled'),
                                    !form.getValues(getAttributeName(config.type, 'enabled'))
                                  );
                                }}
                                draggable={false}
                                onClick={() => {
                                  handleExtraComponentClick({
                                    type: config.type,
                                  });
                                }}
                              />
                            </Grid>
                          )
                      )}
                    </Grid>
                  </Stack>
                </Stack>
              </Scrollbar>
            </TabPanel>
            <TabPanel value="rows" sx={{ p: 1 }}>
              <Stack gap={2}>
                {layouts.map((layout, index) => (
                  <CheckoutBuilderRowCard key={index} layout={layout} />
                ))}
              </Stack>
            </TabPanel>
            <TabPanel value="settings" sx={{ p: 1 }}>
              <CheckoutBuilderSettingsForm device={device} />
            </TabPanel>
          </TabContext>
        </Box>
        {isMobile && (
          <button
            type="button"
            style={{
              position: 'absolute',
              top: 0,
              right: '50%',
              transform: 'translateX(50%)',
              cursor: 'ns-resize',
              padding: '8px',
            }}
            onTouchStart={handleTouchStart}
          >
            <DragHandleIcon />
          </button>
        )}
      </Drawer>
    </div>
  );
};

export default CheckoutBuilderComponentsMenuDrawer;
