import { Avatar, Card, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import Iconify from '../../../components/iconify/Iconify';

const CheckoutBuilderTestimonialComponent = ({
  attributes: { backgroundColor, textColor, text, author, rating, avatar, horizontal } = {},
}) => (
  <Stack alignItems="center">
    <Stack
      component={Card}
      borderRadius={1}
      direction={horizontal ? 'row' : 'column'}
      p={3}
      gap={horizontal ? 3 : 1}
      alignItems="center"
      width={horizontal ? 400 : 350}
      bgcolor={backgroundColor || '#fff'}
      maxWidth={1}
    >
      <Stack>
        <Avatar
          src={avatar?.preview || ''}
          sx={{
            width: 80,
            height: 80,
          }}
        />
      </Stack>
      <Stack alignItems={horizontal ? 'flex-start' : 'center'} justifyContent="center" gap={1}>
        {/* text */}
        <Typography
          variant="h5"
          color={textColor || 'text.primary'}
          textAlign={horizontal ? 'left' : 'center'}
        >
          {text}
        </Typography>
        {/* rating */}
        <Stack direction="row" flexWrap="nowrap">
          {Array.from({ length: rating || 5 }).map((_, index) => (
            <Iconify key={index} icon="eva:star-fill" color="#f7d538" />
          ))}
        </Stack>
        {/* author */}
        <Typography
          variant="body2"
          color={textColor || 'text.primary'}
          textAlign={horizontal ? 'left' : 'center'}
        >
          {author}
        </Typography>
      </Stack>
    </Stack>
  </Stack>
);

CheckoutBuilderTestimonialComponent.propTypes = {
  attributes: PropTypes.object,
};

export default CheckoutBuilderTestimonialComponent;
