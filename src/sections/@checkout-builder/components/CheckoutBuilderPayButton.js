import { useEffect, useMemo } from 'react';
import { Button } from '@mui/material';
import { useWatch, useFormContext } from 'react-hook-form';
import useResponsive from '@/hooks/useResponsive';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import { CheckoutBuilderComponentTypes } from '../constants';

export default function CheckoutBuilderPayButton() {
  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const form = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const backgroundColor = useWatch({
    name: getAttributeName('payButton.color'),
  });

  const color = useWatch({
    name: getAttributeName('payButton.text.color'),
  });

  const text = useWatch({
    name: getAttributeName('payButton.text.text'),
  });

  const isMobile = useResponsive('down', 'sm');

  useEffect(() => {
    if (!backgroundColor) {
      form.setValue(
        getAttributeName('payButton.color'),
        CheckoutBuilderComponentTypes.checkout.config.settings.payButton.color
      );
    }

    if (!color) {
      form.setValue(
        getAttributeName('payButton.text.color'),
        CheckoutBuilderComponentTypes.checkout.config.settings.payButton.text.color
      );
    }

    if (!text) {
      form.setValue(
        getAttributeName('payButton.text.text'),
        CheckoutBuilderComponentTypes.checkout.config.settings.payButton.text.text
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const buttonProps = useMemo(() => {
    const desktopRows = form.getValues('desktop.rows');
    const lastDesktopRows = desktopRows[desktopRows.length - 1]?.layout;

    if (isMobile) {
      if (JSON.stringify(lastDesktopRows) === '[12]') {
        return {
          left: 10,
          bottom: 100,
          width: '92%',
          height: 35,
          fontSize: 15,
        };
      }

      if (JSON.stringify(lastDesktopRows) === '[8,4]') {
        return {
          left: 5,
          bottom: 0,
          width: '92%',
          height: 35,
          fontSize: 15,
        };
      }

      return {
        left: 0,
        bottom: 0,
        width: '92%',
        height: 20,
        fontSize: 6,
      };
    }

    if (device === 'mobile') {
      return {
        left: 15,
        bottom: 145,
        width: 396,
        height: 45,
        fontSize: 15,
      };
    }

    if (JSON.stringify(lastDesktopRows) === '[12]') {
      return {
        left: 35,
        bottom: 340,
        width: 1000,
        height: 90,
        fontSize: 40,
      };
    }

    if (JSON.stringify(lastDesktopRows) === '[8,4]') {
      return {
        left: 25,
        bottom: 200,
        width: 650,
        height: 50,
        fontSize: 15,
      };
    }

    return {
      left: 15,
      bottom: 110,
      width: 310,
      height: 40,
      fontSize: 15,
    };
  }, [form, device, isMobile]);

  return (
    <Button
      sx={{
        position: 'relative',
        left: buttonProps.left,
        bottom: buttonProps.bottom,
        backgroundColor,
        ':hover': {
          opacity: 0.8,
          backgroundColor,
        },
        width: buttonProps.width,
        height: buttonProps.height,
      }}
    >
      <span style={{ color, fontSize: buttonProps.fontSize }}>{text}</span>
    </Button>
  );
}
