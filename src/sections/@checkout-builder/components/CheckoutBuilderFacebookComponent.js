import { Box } from '@mui/system';
import PropTypes from 'prop-types';
import { useEffect, useMemo } from 'react';
import Iconify from '../../../components/iconify/Iconify';
import {
  CheckoutBuilderFacebookOrderBy,
  CheckoutBuilderFacebookType,
} from '../form/CheckoutBuilderFacebookComponentForm';
import ComponentWrapper from './ComponentWrapper';

const CheckoutBuilderFacebookComponent = ({
  id,
  attributes: { type, url, size, count, orderBy, tabs, options = [] } = {},
}) => {
  const component = useMemo(
    () => ({
      [CheckoutBuilderFacebookType.commentSection.id]: (
        <CommentSection url={url} count={count} orderBy={orderBy} />
      ),
      [CheckoutBuilderFacebookType.page.id]: (
        <Page url={url} size={size} tabs={tabs} options={options} />
      ),
      [CheckoutBuilderFacebookType.singleComment.id]: <SingleComment url={url} />,
      [CheckoutBuilderFacebookType.post.id]: <Post url={url} />,
    }),
    [url, count, orderBy, size, tabs, options]
  );

  useEffect(() => {
    try {
      if (window.FB) {
        window?.FB?.XFBML?.parse?.();
      }
    } catch (error) {
      console.log(error);
    }
  }, [component]);

  return (
    <ComponentWrapper
      key={id}
      width={1}
      sx={{
        minHeight: 220,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        '& :first-child': {
          width: '100% !important',
        },
      }}
    >
      {url ? (
        component[type]
      ) : (
        <Box
          width={1}
          height={1}
          display="flex"
          justifyContent="center"
          alignItems="center"
          borderRadius={1}
          minHeight={300}
          bgcolor="grey.200"
        >
          <Iconify
            icon="mdi:facebook"
            color="grey.500"
            sx={{
              width: 120,
              height: 120,
            }}
          />
        </Box>
      )}
    </ComponentWrapper>
  );
};

const CommentSection = ({ url, size, count, orderBy }) => (
  <div
    className="fb-comments"
    data-href={url}
    data-width="100%"
    data-numposts={count}
    data-order-by={
      {
        [CheckoutBuilderFacebookOrderBy.oldest.id]: 'time',
        [CheckoutBuilderFacebookOrderBy.latest.id]: 'reverse_time',
        [CheckoutBuilderFacebookOrderBy.relevant.id]: 'social',
      }[orderBy]
    }
    data-colorscheme="light"
  />
);

const Page =
  (({ url, size, tabs, options }) => (
    <div
      className="fb-page"
      data-href={url}
      data-tabs={tabs?.join(',') || 'timeline'}
      data-width="100%"
      data-height=""
      data-small-header={options.includes('smallHeader')}
      data-hide-cover={!options.includes('showCover')}
      data-show-facepile={options.includes('facePile')}
      data-hide-cta={!options.includes('callToAction')}
      data-adapt-container-width="true"
    >
      <blockquote cite={url} className="fb-xfbml-parse-ignore">
        <a href={url}>Facebook</a>
      </blockquote>
    </div>
  ),
  (prevProps, nextProps) => prevProps.url === nextProps.url && prevProps.tabs === nextProps.tabs);

const SingleComment = ({ url, size }) => (
  <Box
    title="facebook"
    width="100%"
    className="fb-comment-embed"
    data-href={url}
    data-width="100%"
  />
);

const Post = ({ url, size }) => (
  <div
    className="fb-post"
    data-href={url}
    data-width="100%"
    data-show-text="true"
    data-show-images="true"
  />
);

Page.propTypes = {
  url: PropTypes.string,
  size: PropTypes.string,
  tabs: PropTypes.array,
  options: PropTypes.array,
};

CommentSection.propTypes = {
  url: PropTypes.string,
  size: PropTypes.string,
  count: PropTypes.string,
  orderBy: PropTypes.string,
};

SingleComment.propTypes = {
  url: PropTypes.string,
  size: PropTypes.string,
};

Post.propTypes = {
  url: PropTypes.string,
  size: PropTypes.string,
};

CheckoutBuilderFacebookComponent.propTypes = {
  id: PropTypes.string,
  attributes: PropTypes.object,
};

export default CheckoutBuilderFacebookComponent;
