import PropTypes from 'prop-types';
import { Card, IconButton, Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { AnimatePresence, motion, useIsPresent } from 'framer-motion';
import { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import Iconify from '@/components/iconify';
import Image from '@/components/image';
import { CheckoutBuilderNotificationTypes } from '../form/CheckoutBuilderNotificationComponentForm';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';

const CheckoutBuilderNotificationComponent = () => {
  const form = useFormContext();

  const {
    extra: { getAttributesName, getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const notification = useWatch({
    name: getAttributesName('notification'),
  });

  const enabledNotications = Object.values(CheckoutBuilderNotificationTypes).filter(
    ({ id }) => notification?.[id]?.enabled
  );

  return (
    <Stack width={1} height={1} p={3} alignItems="flex-end">
      <Stack gap={2}>
        <AnimatePresence>
          {enabledNotications.map(({ id }) => (
            <NoficationCard
              key={id}
              id={id}
              onClose={() => {
                form.setValue(getAttributeName('notification', `${id}.enabled`), false);
              }}
            />
          ))}
        </AnimatePresence>
      </Stack>
    </Stack>
  );
};

export const NoficationCard = ({ id, onClose }) => {
  const present = useIsPresent();

  const people = useMemo(() => Math.floor(Math.random() * 100), []);

  const { icon, message } = CheckoutBuilderNotificationTypes[id] || {};

  const animations = {
    style: {
      position: present ? 'static' : 'absolute',
    },
    initial: {
      scale: 0,
      opacity: 0,
    },
    animate: {
      scale: 1,
      opacity: 1,
    },
    exit: {
      scale: 0,
      opacity: 0,
    },
    transition: {
      type: 'spring',
      stiffness: 500,
      damping: 40,
    },
  };

  return (
    <Card
      component={motion.div}
      {...animations}
      layout
      color="primary"
      sx={{
        width: 300,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <Box position="relative">
        <Stack direction="row" gap={2} p={2.5}>
          <Image
            src={`/assets/icons/checkout/${icon}.png`}
            sx={{
              width: 50,
              height: 50,
              alignSelf: 'center',
            }}
            alt={icon}
          />
          <Typography
            variant="body2"
            sx={{
              flex: 1,
            }}
          >
            {message.replace('{value}', people)}
          </Typography>
        </Stack>
        <IconButton
          onClick={onClose}
          size="small"
          sx={{
            position: 'absolute',
            top: 3,
            right: 3,
          }}
        >
          <Iconify icon="mdi:close" />
        </IconButton>
      </Box>
    </Card>
  );
};

NoficationCard.propTypes = {
  id: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default CheckoutBuilderNotificationComponent;
