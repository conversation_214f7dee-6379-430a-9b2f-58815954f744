import { Stack } from '@mui/material';
import { Container } from '@mui/system';
import { memo } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { v4 } from 'uuid';
import useResponsive from '@/hooks/useResponsive';
import { CheckoutBuilderDeviceTypes } from '../constants';
import CheckoutBuilderDraggableRow from './CheckoutBuilderDraggableRow';

const CheckoutBuilderPanel = () => {
  const device = useWatch({
    name: 'device',
  });

  const isMobile = useResponsive('down', 'sm');

  const map = {
    [CheckoutBuilderDeviceTypes.desktop.id]: {
      font: useWatch({
        name: 'desktop.settings.font',
      }),
      background: useWatch({
        name: 'desktop.settings.background',
      }),
      container: 1200,
    },
    [CheckoutBuilderDeviceTypes.mobile.id]: {
      font: useWatch({
        name: 'mobile.settings.font',
      }),
      background: useWatch({
        name: 'mobile.settings.background',
      }),
      container: 550,
    },
  };

  const { font, background, container } = map[device] || {};

  return (
    <Container
      maxWidth={!isMobile}
      sx={{
        width: isMobile ? 1200 : container,
      }}
      key={`panel-${device}`}
    >
      <Stack
        sx={{
          borderRadius: 2,
          marginTop: isMobile ? 10 : undefined,
          marginBottom: isMobile ? 40 : undefined,
          p: 1,
          '& > *': {
            fontFamily: `${font.family} !important`,
          },
          backgroundColor: background?.color,
          backgroundImage: background?.image ? `url(${background?.image?.preview})` : undefined,
          backgroundPositionX: 'center',
          backgroundRepeat: background?.repeat ? 'repeat' : 'no-repeat',
          backgroundSize: background?.cover ? 'cover' : 'initial',
          backgroundAttachment: background?.fixed ? 'fixed' : 'initial',
        }}
      >
        <RowsContainer />
      </Stack>
    </Container>
  );
};

const RowsContainer = memo(() => {
  const form = useFormContext();

  const device = useWatch({
    name: 'device',
  });

  const map = {
    [CheckoutBuilderDeviceTypes.desktop.id]: useFieldArray({
      name: `desktop.rows`,
      keyName: 'key',
    }),
    [CheckoutBuilderDeviceTypes.mobile.id]: useFieldArray({
      name: `mobile.rows`,
      keyName: 'key',
    }),
  };

  const rows = map[device] || null;

  return (
    rows && (
      <Stack position="relative" key={device}>
        {rows.fields.map((row, rowIndex) => (
          <CheckoutBuilderDraggableRow
            rowIndex={rowIndex}
            id={row.id}
            key={`row-${row.id}-${device}-${rowIndex}`}
            onCopy={() => {
              const copied = form.getValues(`${device}.rows.${rowIndex}`);
              console.log(row);
              rows.insert(rowIndex + 1, {
                ...copied,
                id: v4(),
                type: 'row',
                columns: row.columns.map((column) => ({
                  ...column,
                  id: v4(),
                  type: 'column',
                  components: column.components.map((component) => ({ ...component, id: v4() })),
                })),
              });
            }}
            onDelete={() => {
              rows.remove(rowIndex);
            }}
          />
        ))}
      </Stack>
    )
  );
});

export default CheckoutBuilderPanel;
