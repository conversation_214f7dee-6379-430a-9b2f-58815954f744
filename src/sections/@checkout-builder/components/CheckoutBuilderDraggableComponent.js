import { Box, Fade, IconButton } from '@mui/material';
import { Stack } from '@mui/system';
import { motion, useIsPresent } from 'framer-motion';
import PropTypes from 'prop-types';
import { memo, useContext, useEffect } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useWatch } from 'react-hook-form';
import { v4 } from 'uuid';
import useResponsive from '@/hooks/useResponsive';
import Iconify from '../../../components/iconify';
import { CheckoutBuilderContext } from '../../../contexts/CheckoutBuilderContext';
import CheckoutBuilderTextComponent from './CheckoutBuilderTextComponent';
import ConfirmationIconButton from '../../../components/buttons/ConfirmationIconButton';
import { SelectedElementContext } from '../../../contexts/SelectedElementContext';
import { FormScopes } from '../../../utils/form';
import { CheckoutBuilderComponentTypes } from '../constants';
import useCheckoutBuilderFormHelper from '../hooks/useCheckoutBuilderFormHelper';
import useDragAndDropRows from '../hooks/useDragAndDropRowsHook';
import CheckoutBuilderAdvantageComponent from './CheckoutBuilderAdvantageComponent';
import CheckoutBuilderCheckoutComponent from './CheckoutBuilderCheckoutComponent';
import CheckoutBuilderCountdownComponent from './CheckoutBuilderCountdownComponent';
import CheckoutBuilderDragLayer from './CheckoutBuilderDragLayer';
import CheckoutBuilderFacebookComponent from './CheckoutBuilderFacebookComponent';
import CheckoutBuilderHeaderComponent from './CheckoutBuilderHeaderComponent';
import CheckoutBuilderImageComponent from './CheckoutBuilderImageComponent';
import CheckoutBuilderListComponent from './CheckoutBuilderListComponent';
import CheckoutBuilderMapComponent from './CheckoutBuilderMapComponent';
import CheckoutBuilderSealComponent from './CheckoutBuilderSealComponent';
import CheckoutBuilderTestimonialComponent from './CheckoutBuilderTestimonialComponent';
import CheckoutBuilderVideoComponent from './CheckoutBuilderVideoComponent';

const CheckoutBuilderDraggableComponent = ({
  rowIndex,
  columnIndex,
  componentIndex,
  onRemove,
  onCopy,
  ...props
}) => {
  const { hovered, setHovered, setScope } = useContext(CheckoutBuilderContext);

  const { selectedID, setSelectedID } = useContext(SelectedElementContext);

  const index = {
    row: rowIndex,
    column: columnIndex,
    component: componentIndex,
  };

  const {
    component: { getRootProperty },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [id, type] =
    useWatch({
      name: [getRootProperty('id'), getRootProperty('type')],
    }) || [];

  const isSelected = selectedID === id;

  const isHovered = hovered?.id === id;

  const { moveComponent, insertComponent } = useDragAndDropRows();

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ['component', 'new-component'],
    drop: (item, monitor) => {
      if (monitor.didDrop()) {
        return;
      }

      if (monitor.getItemType() === 'new-component') {
        const payload = {
          id: v4(),
          type: item.type,
          attributes: {
            ...CheckoutBuilderComponentTypes[item.type].attributes,
          },
        };

        requestAnimationFrame(() => {
          insertComponent({
            to: {
              rowIndex,
              columnIndex,
              componentIndex,
            },
            payload,
          });
        });

        return;
      }

      requestAnimationFrame(() => {
        moveComponent({
          from: {
            rowIndex: item.rowIndex,
            columnIndex: item.columnIndex,
            componentIndex: item.componentIndex,
          },
          to: {
            rowIndex,
            columnIndex,
            componentIndex,
          },
        });
      });
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({
        shallow: true,
      }),
      canDrop: monitor.canDrop(),
    }),
  });

  const [{ isDragging }, drag, preview] = useDrag({
    item: () => ({
      id,
      type,
      rowIndex,
      columnIndex,
      componentIndex,
    }),
    type: 'component',
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  const present = useIsPresent();

  const isMobile = useResponsive('down', 'sm');

  return (
    <Stack
      component={motion.div}
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
      exit={{
        opacity: 0,
      }}
      transition={{
        type: 'spring',
        stiffness: 400,
        damping: 50,
      }}
      layout="preserve-aspect"
      width={1}
      position={present ? 'relative' : 'absolute'}
      ref={(node) => drop(node)}
      onMouseEnter={(e) => {
        e.stopPropagation();
        if (!isDragging && !isOver) {
          setHovered({
            id,
            type,
            rowIndex,
            columnIndex,
            componentIndex,
          });
        }
      }}
      onMouseLeave={() => {
        setHovered({
          type: 'row',
          rowIndex,
        });
      }}
    >
      <CheckoutBuilderDragLayer visible={isOver && canDrop} />
      <Box
        p={0.5}
        width={1}
        borderRadius={1}
        sx={{
          transition: 'all 0.2s ease-in-out',
          ...(isDragging && {
            opacity: 0.2,
          }),
        }}
        border={(theme) => {
          if (isSelected) {
            return `2px solid ${theme.palette.primary.main}`;
          }
          if (isHovered) {
            return `2px solid ${theme.palette.grey[500]}`;
          }
          return '2px solid transparent';
        }}
        position="relative"
        className="checkout-builder-draggable-component"
        onClick={(e) => {
          e.stopPropagation();
          if (type === CheckoutBuilderComponentTypes.checkout.type) {
            return;
          }
          setSelectedID(id);
          setScope(FormScopes.EDIT);
        }}
        {...props}
      >
        <MemoizedComponent id={id} key={`component-${id}`} type={type} index={index} />
        <Fade in={isSelected || isHovered}>
          <Box>
            {(isSelected || isHovered) && !isMobile && (
              <Stack
                zIndex={2}
                direction="row"
                position="absolute"
                top={0}
                right={0}
                borderRadius={0.5}
                sx={{
                  backgroundColor: (theme) =>
                    isSelected ? theme.palette.primary.main : theme.palette.grey[500],
                }}
              >
                {isSelected && type !== CheckoutBuilderComponentTypes.checkout.type && (
                  <>
                    <ConfirmationIconButton color="inherit" onConfirm={onRemove}>
                      <Iconify
                        icon="ion:trash"
                        sx={{
                          color: 'white',
                        }}
                      />
                    </ConfirmationIconButton>
                    <IconButton color="inherit" onClick={onCopy}>
                      <Iconify
                        icon="ion:copy"
                        sx={{
                          color: 'white',
                        }}
                      />
                    </IconButton>
                    <IconButton
                      color="inherit"
                      sx={{ cursor: 'grab' }}
                      disableRipple
                      disableFocusRipple
                      disableTouchRipple
                      ref={drag}
                    >
                      <Iconify
                        icon="ri:drag-move-2-fill"
                        sx={{
                          color: 'white',
                        }}
                      />
                    </IconButton>
                  </>
                )}
              </Stack>
            )}
            {isMobile && (
              <Stack
                zIndex={2}
                direction="row"
                position="absolute"
                top={0}
                right={0}
                borderRadius={0.5}
                sx={{
                  backgroundColor: (theme) => theme.palette.primary.main,
                }}
              >
                <ConfirmationIconButton color="inherit" onConfirm={onRemove}>
                  <Iconify
                    icon="ion:trash"
                    sx={{
                      color: 'white',
                    }}
                  />
                </ConfirmationIconButton>
                <IconButton
                  color="inherit"
                  onClick={(e) => {
                    e.stopPropagation();
                    onCopy();
                  }}
                >
                  <Iconify
                    icon="ion:copy"
                    sx={{
                      color: 'white',
                    }}
                  />
                </IconButton>
                <IconButton
                  color="inherit"
                  onClick={(e) => {
                    e.stopPropagation();
                    setScope(FormScopes.OPEN_SETTINGS);
                  }}
                >
                  <Iconify
                    icon="ion:settings"
                    sx={{
                      color: 'white',
                    }}
                  />
                </IconButton>
              </Stack>
            )}
          </Box>
        </Fade>
      </Box>
    </Stack>
  );
};

const MemoizedComponent = memo(({ index, className, ...props }) => {
  const Components = {
    [CheckoutBuilderComponentTypes.text.id]: CheckoutBuilderTextComponent,
    [CheckoutBuilderComponentTypes.checkout.id]: CheckoutBuilderCheckoutComponent,
    [CheckoutBuilderComponentTypes.countdown.id]: CheckoutBuilderCountdownComponent,
    [CheckoutBuilderComponentTypes.header.id]: CheckoutBuilderHeaderComponent,
    [CheckoutBuilderComponentTypes.advantage.id]: CheckoutBuilderAdvantageComponent,
    [CheckoutBuilderComponentTypes.list.id]: CheckoutBuilderListComponent,
    [CheckoutBuilderComponentTypes.testimonial.id]: CheckoutBuilderTestimonialComponent,
    [CheckoutBuilderComponentTypes.seal.id]: CheckoutBuilderSealComponent,
    [CheckoutBuilderComponentTypes.image.id]: CheckoutBuilderImageComponent,
    [CheckoutBuilderComponentTypes.video.id]: CheckoutBuilderVideoComponent,
    [CheckoutBuilderComponentTypes.facebook.id]: CheckoutBuilderFacebookComponent,
    [CheckoutBuilderComponentTypes.map.id]: CheckoutBuilderMapComponent,
  };

  const {
    component: { getRootProperty },
  } = useCheckoutBuilderFormHelper({
    index,
  });

  const [id, type, attributes] = useWatch({
    name: [getRootProperty('id'), getRootProperty('type'), getRootProperty('attributes')],
  });

  const Component = Components[type];

  if (!Component) {
    return null;
  }

  return (
    <Component {...props} className={className} index={index} id={id} attributes={attributes} />
  );
});

MemoizedComponent.propTypes = {
  id: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  index: PropTypes.object.isRequired,
  className: PropTypes.object,
  attributes: PropTypes.object.isRequired,
};

CheckoutBuilderDraggableComponent.propTypes = {
  id: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  rowIndex: PropTypes.number.isRequired,
  columnIndex: PropTypes.number.isRequired,
  componentIndex: PropTypes.number.isRequired,
  onRemove: PropTypes.func.isRequired,
  onCopy: PropTypes.func.isRequired,
};

export default CheckoutBuilderDraggableComponent;
