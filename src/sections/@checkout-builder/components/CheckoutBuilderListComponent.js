import { Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useRef } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import uuidv4 from '../../../utils/uuidv4';
import {
  CheckoutBuilderListAlignment,
  CheckoutBuilderListStyle,
} from '../form/CheckoutBuilderListComponentForm';
import ComponentWrapper from './ComponentWrapper';

const CheckIcon = CheckoutBuilderListStyle.check.icon;

const FlexAlign = {
  [CheckoutBuilderListAlignment.left.id]: 'flex-start',
  [CheckoutBuilderListAlignment.center.id]: 'center',
  [CheckoutBuilderListAlignment.right.id]: 'flex-end',
};

const CheckoutBuilderListComponent = ({
  attributes: {
    textColor,
    backgroundColor,
    alignment,
    style,
    fontSize,
    showTitle,
    title,
    iconColor,
    items = [],
  } = {},
  index: { row, column, component },
}) => {
  const device = useWatch({
    name: 'device',
  });

  const form = useFormContext();

  const refs = useRef([]);

  const setFocus = (index) => {
    const range = document.createRange();

    range.selectNodeContents(refs.current[index]);

    const selection = window.getSelection();

    selection.removeAllRanges();

    selection.addRange(range);

    refs.current[index].focus();
  };

  const removeItem = (itemIndex) => {
    const newItems = items.filter((_, index) => index !== itemIndex);

    form.setValue(
      `${device}.rows[${row}].columns[${column}].components[${component}].attributes.items`,
      newItems
    );

    setTimeout(() => {
      if (newItems.length > 0) {
        setFocus(itemIndex - 1);
      }
    }, 0);
  };

  return (
    <ComponentWrapper
      gap={2}
      p={2}
      bgcolor={backgroundColor}
      borderRadius={1}
      alignItems={FlexAlign[alignment]}
    >
      {showTitle && (
        <Typography
          variant="h3"
          sx={{
            color: textColor,
            fontWeight: 'bold',
          }}
        >
          {title}
        </Typography>
      )}
      <Stack
        component="ul"
        sx={{
          listStyle: style === CheckoutBuilderListStyle.check.id ? 'none' : style,
          pl: [CheckoutBuilderListStyle.check.id, CheckoutBuilderListStyle.none.id].includes(style)
            ? 0
            : 3,
          gap: 2,
          width: 1,
        }}
        alignItems={FlexAlign[alignment]}
      >
        {items?.map((item, itemIndex) => (
          <Box
            component="li"
            key={item.id}
            sx={{
              width: 1,
              color: textColor,
              textAlign: alignment,
              fontSize: `${fontSize}px`,
            }}
          >
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
              width={1}
              justifyContent={FlexAlign[alignment]}
            >
              {style === CheckoutBuilderListStyle.check.id && (
                <Box minWidth={fontSize || 18} minHeight={fontSize || 18}>
                  <CheckIcon width={fontSize || 18} height={fontSize || 18} stroke={iconColor} />
                </Box>
              )}
              <Typography
                ref={(el) => {
                  refs.current[itemIndex] = el;
                }}
                variant="body1"
                fontSize={`${fontSize}px`}
                sx={{
                  display: 'inline-block',
                  outline: 'none',
                  wordBreak: 'break-word',
                  textAlign: alignment,
                }}
                autoFocus
                contentEditable
                suppressContentEditableWarning
                onBlur={(event) => {
                  const value = event.target.innerText;

                  form.setValue(
                    `${device}.rows[${row}].columns[${column}].components[${component}].attributes.items[${itemIndex}].text`,
                    value
                  );

                  if (value === '') {
                    removeItem(itemIndex);
                  }
                }}
                onKeyDown={(event) => {
                  if (event.key === 'Backspace' && event.target.innerText === '') {
                    event.preventDefault();

                    removeItem(itemIndex);
                  }
                  if (event.key === 'Enter') {
                    event.preventDefault();

                    if (items.length > 10) {
                      return;
                    }

                    form.setValue(
                      `${device}.rows[${row}].columns[${column}].components[${component}].attributes.items`,
                      [...items, { id: uuidv4(), text: 'Novo Item' }]
                    );

                    setTimeout(() => {
                      setFocus(itemIndex + 1);
                    }, 0);
                  }
                }}
                dangerouslySetInnerHTML={{ __html: item.text }}
              />
            </Stack>
          </Box>
        ))}
      </Stack>
    </ComponentWrapper>
  );
};

CheckoutBuilderListComponent.propTypes = {
  attributes: PropTypes.object,
  index: PropTypes.object.isRequired,
};

export default CheckoutBuilderListComponent;
