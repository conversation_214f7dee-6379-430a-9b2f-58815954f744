// hooks
import PropTypes from 'prop-types';
// @mui
import { LoadingButton } from '@mui/lab';
import { <PERSON><PERSON>, Drawer, IconButton, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import FormProvider from '../../../components/hook-form/FormProvider';
import RHFPercentageTextField from '../../../components/hook-form/RHFPercentageTextField';
import Iconify from '../../../components/iconify';

EditAffiliate.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  form: PropTypes.any,
  update: PropTypes.func,
  updating: PropTypes.bool,
};

export default function EditAffiliate({ open, onClose, form, update, updating }) {
  function onSubmit(values) {
    const { id, ...info } = values;
    update({ id, info }).then(() => onClose());
  }

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          p: 2,
          width: {
            xs: '100%',
            sm: 400,
          },
        },
      }}
    >
      <Stack direction="row" alignItems="center" gap={1}>
        <IconButton onClick={onClose}>
          <Iconify icon="eva:chevron-right-fill" />
        </IconButton>
        <Typography variant="subtitle1">Afiliado</Typography>
      </Stack>

      <Stack my={1}>
        <Typography color="text.secondary" variant="body2">
          Preencha as informações do seu afiliado.
        </Typography>
      </Stack>

      <FormProvider methods={form} onSubmit={form.handleSubmit(onSubmit)}>
        <Stack spacing={2} mt={2}>
          <RHFPercentageTextField label="Comissão" size="small" name="commission" />
        </Stack>

        <Stack direction="row" justifyContent="flex-end" alignItems="center" gap={1} mt={3}>
          <Button variant="outlined" color="inherit" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton variant="contained" color="primary" type="submit" loading={updating}>
            Salvar alterações
          </LoadingButton>
        </Stack>
      </FormProvider>
    </Drawer>
  );
}
