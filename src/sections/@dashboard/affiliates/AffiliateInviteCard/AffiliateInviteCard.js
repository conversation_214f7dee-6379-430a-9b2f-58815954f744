import { Tab<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabPanel } from '@mui/lab';
import {
  Button,
  Card,
  Link,
  ListItemText,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Stack } from '@mui/system';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import Image from '../../../../components/image';
import { fCurrency, formatPercentage } from '../../../../utils/formatNumber';
import useAffiliateInviteCard from './useAffiliateInviteCard';

const AffiliateInviteCard = () => {
  const [searchParams] = useSearchParams();

  const { pathname } = useLocation();

  const navigate = useNavigate();

  const { product, userEmail, productId, createAffiliate } = useAffiliateInviteCard();

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'product');

  const handleChangeTab = (_, newTab) => {
    setTabValue(newTab);
    navigate(`${pathname}?tab=${newTab}`);
  };

  const formatType = (type) =>
    ({
      subscription: 'Inscrição',
      unique: 'Venda única',
    }[type]);

  const formatAffiliateClick = (type) =>
    ({
      last: 'Último clique',
      first: 'Primeiro clique',
    }[type]);

  const formatCookieDuration = (cookie) => {
    const cookiesMap = {
      '-1': 'Eterno',
      1: '1 dia',
      30: '30 dias',
      60: '60 dias',
      90: '90 dias',
      180: '180 dias',
    };

    return cookiesMap[cookie.toString()];
  };
  return (
    <Card sx={{ width: 500 }}>
      <Stack p={3} pb={0} gap={3}>
        <Image
          src={product?.image}
          alt="Convite de Afiliado"
          sx={{
            width: 200,
            height: 200,
            borderRadius: 1,
          }}
        />
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          flexWrap="wrap"
          gap={2}
        >
          <Stack spacing={1}>
            <Typography data-testid="product-name" variant="h5" fontWeight="bold">
              {product?.name}
            </Typography>
            <Typography data-testid="producer-name" variant="caption" color="text.secondary">
              {product?.producerName}
            </Typography>
          </Stack>
          <Button
            variant="contained"
            onClick={() => createAffiliate({ userEmail, productPk: productId })}
            color="primary"
            sx={{
              width: {
                xs: 1,
                sm: 'auto',
              },
            }}
          >
            Solicitar afiliação
          </Button>
        </Stack>
      </Stack>
      <TabContext value={tabValue}>
        <TabList value={tabValue} onChange={handleChangeTab} sx={{ p: 1, px: 3 }}>
          <Tab value="product" label="Produto" />
          <Tab value="details" label="Detalhes" />
          <Tab value="offers" label="Ofertas" />
        </TabList>
        <TabPanel sx={{ pt: 1 }} value="product">
          <Stack spacing={2}>
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Tipo
                </Typography>
              }
              secondary={
                <Typography data-testid="product-type" variant="body2" color="text.secondary">
                  {formatType(product?.type)}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Comissão
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-affiliate-commission"
                  variant="body2"
                  color="success.main"
                  fontWeight="bold"
                >
                  {formatPercentage(product?.affiliateCommission || 0, '0.00')}
                </Typography>
              }
            />
          </Stack>
        </TabPanel>
        <TabPanel sx={{ pt: 1 }} value="details">
          <Stack spacing={2}>
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Atribuição
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-affiliate-click"
                  variant="body2"
                  color="text.secondary"
                >
                  {formatAffiliateClick(product?.affiliateClick)}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Duração dos cookies
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-cookie-time"
                  variant="body2"
                  color="text.secondary"
                >
                  {product?.cookieTime
                    ? formatCookieDuration(product?.cookieTime)
                    : 'Não definido.'}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Recebe Order Bump
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-affiliate-share-bump"
                  variant="body2"
                  color="text.secondary"
                >
                  {product?.affiliateShareBump ? 'Sim' : 'Não'}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Recebe Upsell
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-affiliate-share-upsell"
                  variant="body2"
                  color="text.secondary"
                >
                  {product?.affiliateShareUpsell ? 'Sim' : 'Não'}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Página de vendas
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-sales-page"
                  variant="body2"
                  component={Link}
                  color="primary.main"
                >
                  {product?.salesPage  || 'Não cadastrada'}
                </Typography>
              }
            />
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="bold">
                  Email de suporte para afiliados
                </Typography>
              }
              secondary={
                <Typography
                  data-testid="product-affiliate-support-email"
                  variant="body2"
                  component={Link}
                  color="primary.main"
                >
                  {product?.supportEmail || 'Não cadastrado'}
                </Typography>
              }
            />
          </Stack>
        </TabPanel>
        <TabPanel sx={{ px: 0, pt: 1 }} value="offers">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    Nome
                  </Typography>
                </TableCell>

                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    Preço
                  </Typography>
                </TableCell>

                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    Você recebe
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {product?.offers?.length > 0 &&
                product.offers.map(({ price, name, id }, index) => (
                  <TableRow key={id}>
                    <TableCell>
                      <Typography
                        data-testid={`offer[${index}].name`}
                        variant="body2"
                        color="text.secondary"
                      >
                        {name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        data-testid={`offer[${index}].price`}
                        variant="body2"
                        color="text.secondary"
                      >
                        {fCurrency(+price)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        data-testid={`offer[${index}].commission`}
                        variant="body2"
                        color="success.main"
                        fontWeight="bold"
                      >
                        {fCurrency(
                          product?.affiliateCommission
                            ? (price * product.affiliateCommission) / 100
                            : 0
                        )}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}

              {product?.offers?.length === 0 && (
                <TableRow>
                  <TableCell colSpan={3}>
                    <Typography data-testid="not-offers" variant="caption" color="text.secondary">
                      Nenhuma oferta encontrada
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TabPanel>
      </TabContext>
    </Card>
  );
};

export default AffiliateInviteCard;
