import 'whatwg-fetch';
import { renderHook } from '@testing-library/react-hooks';
import { useParams } from 'react-router';
import { useAuthContext } from '../../../../auth/useAuthContext';
import useAffiliateInviteCard from './useAffiliateInviteCard';

jest.mock('../../../../auth/useAuthContext', () => ({
  useAuthContext: jest.fn(),
}));

jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useParams: jest.fn(),
}));

describe('useAffiliateInviteCard', () => {
  beforeEach(() => {
    useAuthContext.mockReturnValue({ user: { email: '<EMAIL>' } });

    useParams.mockReturnValue({ id: 1 });

    jest.clearAllMocks();
  });

  it('should return the correct values', () => {
    const { result } = renderHook(() => useAffiliateInviteCard(), { wrapper: global.wrapper });

    expect(result.current.userEmail).toEqual('<EMAIL>');
    expect(result.current.productId).toEqual(1);
  });
});
