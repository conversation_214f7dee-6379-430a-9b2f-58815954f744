import { render, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { fCurrency } from '../../../../utils/formatNumber';
import AffiliateInviteCard from './AffiliateInviteCard';
import * as useAffiliateInviteCard from './useAffiliateInviteCard';

const product = {
  id: 'eb115726-0c76-41e6-a116-d1600ac29b25',
  name: 'PRODUTO TESTE',
  type: 'unique',
  description: 'TEST',
  category: '2a98f4b4-0208-42dc-960f-47ffe4e8170f',
  price: '10.00',
  image: '/image',
  affiliate: false,
  affiliateCommission: '10.00',
  affiliateContact: false,
  affiliateDescription: '',
  affiliateSupportEmail: '',
  affiliateMarketplace: false,
  affiliateClick: 'last',
  cookieTime: '-1',
  affiliateShareBump: true,
  affiliateShareUpsell: false,
  offers: [
    {
      id: '7HJMpSs',
      name: 'PRODUTO TESTE',
      price: '10.00',
      default: true,
      status: 'active',
    },
  ],
  producerName: 'Produtor',
  salesPage: '',
  supportEmail: '<EMAIL>',
};

const Component = () => (
  <MemoryRouter>
    <AffiliateInviteCard />
  </MemoryRouter>
);

const createAffiliateMock = jest.fn();

describe('AffiliateInviteCard', () => {
  beforeEach(() => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product,
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    jest.clearAllMocks();
  });

  it('renders the component with the correct values in the product tab', () => {
    const { getByAltText, getByTestId, getByRole } = render(<Component />);

    expect(getByAltText('Convite de Afiliado')).toHaveAttribute('src', '/image');
    expect(getByTestId('product-name').textContent).toBe('PRODUTO TESTE');
    expect(getByTestId('producer-name').textContent).toBe('Produtor');
    expect(getByRole('button').textContent).toBe('Solicitar afiliação');
    expect(getByTestId('product-type').textContent).toBe('Venda única');
    expect(getByTestId('product-affiliate-commission').textContent).toBe('10%');
  });

  it('renders the component with the correct values in the product tab when the product type is equals subscription', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValueOnce({
      product: {
        ...product,
        type: 'subscription',
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByTestId } = render(<Component />);

    expect(getByTestId('product-type').textContent).toBe('Inscrição');
  });

  it('renders the component with the correct values in the product tab when the affiliateCommission is null', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValueOnce({
      product: {
        ...product,
        affiliateCommission: null,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByTestId } = render(<Component />);

    expect(getByTestId('product-affiliate-commission').textContent).toBe('0%');
  });

  it('renders the component with the correct values in the product tab when the affiliateCommission is equals 10.02', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValueOnce({
      product: {
        ...product,
        affiliateCommission: 10.02,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByTestId } = render(<Component />);

    expect(getByTestId('product-affiliate-commission').textContent).toBe('10,02%');
  });

  it('renders the component with the correct values in the product tab when the affiliateCommission is equals 0', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValueOnce({
      product: {
        ...product,
        affiliateCommission: 0,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByTestId } = render(<Component />);

    expect(getByTestId('product-affiliate-commission').textContent).toBe('0%');
  });

  it('renders the component with the correct values in the details tab', () => {
    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-affiliate-click').textContent).toBe('Último clique');
    expect(getByTestId('product-cookie-time').textContent).toBe('Eterno');
    expect(getByTestId('product-affiliate-share-upsell').textContent).toBe('Não');
    expect(getByTestId('product-sales-page').textContent).toBe('Não cadastrada');
    expect(getByTestId('product-affiliate-support-email').textContent).toBe('<EMAIL>');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is null', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: null,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('Não definido.');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is equals 1', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: 1,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('1 dia');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is equals 30', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: 30,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('30 dias');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is equals 60', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: 60,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('60 dias');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is equals 90', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: 90,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('90 dias');
  });

  it('renders the component with the correct values in the details tab when the cookieTime is equals 180', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        cookieTime: 180,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-cookie-time').textContent).toBe('180 dias');
  });

  it('renders the component with the correct values in the details tab when the affiliateShareBump is equals false', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        affiliateShareBump: false,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-affiliate-share-bump').textContent).toBe('Não');
  });

  it('renders the component with the correct values in the details tab when the affiliateShareUpsell is equals true', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        affiliateShareUpsell: true,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-affiliate-share-upsell').textContent).toBe('Sim');
  });

  it('renders the component with the correct values in the details tab when the upsellPage is not undefined', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        salesPage: 'https://www.google.com/',
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-sales-page').textContent).toBe('https://www.google.com/');
  });

  it('renders the component with the correct values in the details tab when the supportEmail is not defined', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        supportEmail: null,
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Detalhes'));

    expect(getByTestId('product-affiliate-support-email').textContent).toBe('Não cadastrado');
  });

  it('renders the component with the correct values in the offers tab', () => {
    const useAuthCreatePasswordSpy = jest.spyOn(useAffiliateInviteCard, 'default');
    useAuthCreatePasswordSpy.mockReturnValue({
      product: {
        ...product,
        offers: [],
      },
      userEmail: '<EMAIL>',
      productId: 1,
      createAffiliate: createAffiliateMock,
    });

    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Ofertas'));

    expect(getByTestId(`not-offers`).textContent).toBe('Nenhuma oferta encontrada');
  });

  it('renders the component with the correct values in the offers tab if there is no offer', () => {
    const { getByText, getByTestId } = render(<Component />);

    fireEvent.click(getByText('Ofertas'));

    expect(getByTestId(`offer[0].name`).textContent).toBe('PRODUTO TESTE');
    expect(getByTestId(`offer[0].price`).textContent).toBe(fCurrency(10));
    expect(getByTestId(`offer[0].commission`).textContent).toBe(fCurrency(1));
  });

  it('should call createAffiliate function when "Solicitar Afiliação" button will be clicked', () => {
    const { getByRole } = render(<Component />);

    fireEvent.click(getByRole('button'));

    expect(createAffiliateMock).toHaveBeenCalledWith({ userEmail: '<EMAIL>', productPk: 1 });
  });
});
