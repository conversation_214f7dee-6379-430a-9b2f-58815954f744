import { useContext } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router';
import { useAuthContext } from '../../../../auth/useAuthContext';
import { AffiliatesContext } from '../../../../contexts/AffiliatesContext';
import { getPublicProductService } from '../../../../services/products';

export default function useAffiliateInviteCard() {
  const { id } = useParams();

  const {
    user: { email: userEmail },
  } = useAuthContext();

  const { createAffiliate } = useContext(AffiliatesContext);

  const { data: product } = useQuery({
    queryKey: ['affiliate-product', { id }],
    queryFn: () => getPublicProductService({ id }),
  });

  return {
    product,
    userEmail,
    productId: id,
    createAffiliate,
  };
}
