import PropTypes from 'prop-types';
// hooks
import { RHFMultiCheckbox, RHFMultiSelect } from '../../../../components/hook-form';
import TableFilterDrawer from '../../../../components/table/TableFilterDrawer';
import { AffiliateStatus, AffiliatesDefaultFilter } from '../../../../contexts/AffiliatesContext';
// custom hooks
import useAffiliateFilter from './useAffiliatesFilter';

AffiliatesFilter.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default function AffiliatesFilter({ open, onClose }) {
  const { form, fetchingProducts, productsOptions, onSubmit } = useAffiliateFilter({ onClose });

  return (
    <TableFilterDrawer
      open={open}
      onClose={onClose}
      form={form}
      defaultFilter={AffiliatesDefaultFilter}
      onSubmit={onSubmit}
    >
      <RHFMultiSelect
        size="small"
        checkbox
        chip
        hiddenLabel
        name="products"
        label="Produtos"
        options={productsOptions}
        disabled={fetchingProducts}
      />
      <RHFMultiCheckbox
        size="small"
        name="status"
        label="Status"
        disabled
        options={Object.values(AffiliateStatus).map(({ label, value }) => ({
          label,
          value,
        }))}
      />
    </TableFilterDrawer>
  );
}
