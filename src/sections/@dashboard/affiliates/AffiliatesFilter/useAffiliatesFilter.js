// components
import { yupResolver } from '@hookform/resolvers/yup';
import { useContext, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { AffiliatesContext } from '../../../../contexts/AffiliatesContext';
// validations
import { schema } from './validations';

export default function useAffiliateFilter({ onClose }) {
  const { products, fetchingProducts, table, status } = useContext(AffiliatesContext);

  const { formState, ...form } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      products: table.filter.products,
      status: table.filter.status,
    },
  });

  const { dirtyFields } = formState;

  const productsOptions = useMemo(
    () => products?.map((product) => ({ label: product.name, value: product.id })) || [],
    [products]
  );

  useEffect(() => {
    form.setValue('status', status);
  }, [form, status]);

  const onSubmit = (data) => {
    onClose();
    table.setDirtyFilters(Object.keys(dirtyFields).length);
    table.setFilter(data);
  };

  return {
    form,
    fetchingProducts,
    onSubmit,
    productsOptions,
  };
}
