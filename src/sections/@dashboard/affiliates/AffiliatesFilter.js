import PropTypes from 'prop-types';
// @mui
// components
import { yupResolver } from '@hookform/resolvers/yup';
import { useContext, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
// hooks
import { RHFMultiCheckbox, RHFMultiSelect } from '../../../components/hook-form';
import TableFilterDrawer from '../../../components/table/TableFilterDrawer';
import {
  AffiliateStatus,
  AffiliatesContext,
  AffiliatesDefaultFilter,
} from '../../../contexts/AffiliatesContext';

AffiliatesFilter.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default function AffiliatesFilter({ open, onClose }) {
  const { products, fetchingProducts, table, status } = useContext(AffiliatesContext);

  const schema = yup.object().shape({
    products: yup.array().of(yup.string()),
    status: yup.array().of(yup.string()),
  });

  const { formState, ...form } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      products: table.filter.products,
      status: table.filter.status,
    },
  });

  const { dirtyFields } = formState;

  const productsOptions = useMemo(
    () => products?.map((product) => ({ label: product?.name, value: product?.id })) || [],
    [products]
  );

  useEffect(() => {
    form.setValue('status', status);
  }, [form, status]);

  return (
    <TableFilterDrawer
      open={open}
      onClose={onClose}
      form={form}
      defaultFilter={AffiliatesDefaultFilter}
      onSubmit={(data) => {
        onClose();
        table.setDirtyFilters(Object.keys(dirtyFields).length);
        table.setFilter(data);
      }}
    >
      <RHFMultiSelect
        size="small"
        checkbox
        chip
        hiddenLabel
        name="products"
        label="Produtos"
        options={productsOptions}
        disabled={fetchingProducts}
      />
      <RHFMultiCheckbox
        size="small"
        name="status"
        label="Status"
        disabled
        options={Object.values(AffiliateStatus).map(({ label, value }) => ({
          label,
          value,
        }))}
      />
    </TableFilterDrawer>
  );
}
