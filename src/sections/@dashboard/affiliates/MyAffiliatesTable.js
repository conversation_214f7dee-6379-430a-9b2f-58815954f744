/* eslint-disable react/prop-types */
// @mui
import { Button, IconButton, MenuItem, TableCell, Typography } from '@mui/material';
// components
import { useContext, useState } from 'react';
import Iconify from '../../../components/iconify/Iconify';
import Label from '../../../components/label';
import MenuPopover from '../../../components/menu-popover/MenuPopover';
import SearchableTable from '../../../components/table/SearchableTable';
import { AffiliateStatus, AffiliatesContext } from '../../../contexts/AffiliatesContext';
import { formatPercentage } from '../../../utils/formatNumber';
import { fDate } from '../../../utils/formatTime';
import AffiliatesFilter from './AffiliatesFilter';
import EditAffiliate from './EditAffiliate';

const labels = [
  { id: 'date', label: 'Data' },
  { id: 'name', label: 'Nome' },
  { id: 'email', label: 'E-mail' },
  { id: 'product', label: 'Produto' },
  { id: 'commission', label: 'Comissão' },
  { id: 'status', label: 'Status' },
  { id: 'actions', label: '' },
];

MyAffiliatesTable.propTypes = {};

export default function MyAffiliatesTable() {
  const {
    tabValue,
    affiliates,
    table,
    count,
    fetching,
    acceptAffiliate,
    blockAffiliate,
    unblockAffiliate,
    rejectAffiliate,
    form,
    update,
    updating,
  } = useContext(AffiliatesContext);
  const itemsSelected = table.selected;

  const [anchorEl, setAnchorEl] = useState(null);

  const [editAnchorElement, setEditAnchorElement] = useState(null);

  const [isEditAffiliateOpen, setIsEditAffiliateOpen] = useState(false);

  const [affiliate, setAffiliate] = useState();

  const handleBlock = async () => {
    await blockAffiliate(itemsSelected);
  };

  const handleUnbBlock = async () => {
    await unblockAffiliate(itemsSelected);
  };

  const handleAccept = async () => {
    await acceptAffiliate(itemsSelected);
  };
  const handleRefused = async () => {
    await rejectAffiliate(itemsSelected);
  };
  return (
    <>
      <MenuPopover open={anchorEl} onClose={() => setAnchorEl(null)} disabledArrow>
        {tabValue === 'active' && (
          <MenuItem
            onClick={() => {
              handleBlock();
              setAnchorEl(null);
            }}
            disabled={table.selected.length === 0}
          >
            <Iconify icon="eva:close-circle-fill" width={20} height={20} />
            Bloquear
          </MenuItem>
        )}
        {tabValue === 'pending' && (
          <>
            <MenuItem
              onClick={() => {
                handleAccept();
                setAnchorEl(null);
              }}
              disabled={table.selected.length === 0}
            >
              <Iconify icon="eva:checkmark-circle-2-fill" width={20} height={20} />
              Aceitar
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleRefused();
                setAnchorEl(null);
              }}
              disabled={table.selected.length === 0}
            >
              <Iconify icon="eva:alert-triangle-fill" width={20} height={20} />
              Recusar
            </MenuItem>
          </>
        )}
        {tabValue === 'disabled' && (
          <MenuItem
            onClick={() => {
              handleUnbBlock();
              setAnchorEl(null);
            }}
            disabled={table.selected.length === 0}
          >
            <Iconify icon="tabler:refresh" width={20} height={20} />
            Desbloquear
          </MenuItem>
        )}
      </MenuPopover>

      <AffiliatesFilter open={table.showFilter} onClose={() => table.setShowFilter(false)} />

      <MenuPopover open={editAnchorElement} onClose={() => setEditAnchorElement(null)}>
        <MenuItem
          onClick={() => {
            setIsEditAffiliateOpen(true);
            setEditAnchorElement(null);
            form.setValue('commission', affiliate.commission);
            form.setValue('id', affiliate.id);
          }}
        >
          <Iconify icon="eva:edit-fill" />
          <Typography variant="body2">Editar</Typography>
        </MenuItem>
      </MenuPopover>
      <SearchableTable
        selectable
        count={count}
        fetching={fetching}
        labels={labels}
        rows={affiliates}
        table={table}
        filterable
        Actions={
          <Button
            variant="contained"
            onClick={(event) => {
              setAnchorEl(event.currentTarget);
            }}
            startIcon={<Iconify icon="eva:more-vertical-fill" />}
          >
            Ações
          </Button>
        }
        renderRow={(row) => (
          <MyAffiliatesTableRow
            row={row}
            setEditAnchorElement={(event) => {
              setEditAnchorElement(event.currentTarget);
              setAffiliate(row);
            }}
          />
        )}
      />

      <EditAffiliate
        open={isEditAffiliateOpen}
        onClose={() => setIsEditAffiliateOpen(false)}
        form={form}
        update={update}
        updating={updating}
      />
    </>
  );
}

function MyAffiliatesTableRow({ row, setEditAnchorElement }) {
  return (
    <>
      <TableCell>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {fDate(row.createdAt || new Date(), 'dd/MM/yyyy')}
        </Typography>
      </TableCell>

      <TableCell>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {row.user?.first_name || '-'}
        </Typography>
      </TableCell>

      <TableCell>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {row.user?.email || '-'}
        </Typography>
      </TableCell>

      <TableCell>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {row.product?.name || '-'}
        </Typography>
      </TableCell>

      <TableCell>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {formatPercentage(row.commission)}
        </Typography>
      </TableCell>

      <TableCell>
        <Label variant="soft" color={AffiliateStatus[row.status]?.color}>
          {AffiliateStatus[row.status]?.label}
        </Label>
      </TableCell>

      <TableCell width={40}>
        {['active', 'pending'].includes(row.status) && (
          <IconButton
            onClick={(event) => {
              event.stopPropagation();
              setEditAnchorElement(event);
            }}
          >
            <Iconify icon="mi:options-vertical" />
          </IconButton>
        )}
      </TableCell>
    </>
  );
}
