import { Alert, MenuItem, Paper, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { useCallback, useContext, useEffect } from 'react';
import { useFieldArray } from 'react-hook-form';
import { RHFCheckbox, RHFSelect, RHFTextField, RHFUpload } from '@/components/hook-form';
import RHFCurrencyTextField from '@/components/hook-form/RHFCurrencyTextField';
import RHFMaskedTextField from '@/components/hook-form/RHFMaskedTextField';
import { DocumentType, IdentityVerificationContext } from '@/contexts/IdentityVerificationContext';
import uuidv4 from '@/utils/uuidv4';
import { companyTypeOptions } from './utils';

const IdentityVerificationStep2 = () => {
  const { form, deleteDocuments, companyData } = useContext(IdentityVerificationContext);
  const resubmit =
    companyData?.status === 'resubmission_requested' &&
    form.watch('documentType') === DocumentType.CNPJ.value;

  console.log(resubmit);
  const companyDocuments = useFieldArray({
    control: form.control,
    name: 'companyDocuments',
  });

  const handleDropCompanyDocuments = useCallback(
    (acceptedFiles) => {
      const newFiles = acceptedFiles.map((file) =>
        Object.assign(file, {
          uuid: uuidv4(),
          preview: URL.createObjectURL(file),
        })
      );
      companyDocuments.append(newFiles);
    },
    [companyDocuments]
  );

  const handleRemoveCompanyDocuments = useCallback(
    (inputFile) => {
      companyDocuments.fields.forEach((file, index) => {
        if (file.uuid && file.uuid === inputFile.uuid) {
          companyDocuments.remove(index);
        }
      });

      companyData.files.forEach((_file) => {
        if (_file.id === inputFile.id) {
          deleteDocuments(_file.id);
          companyDocuments.fields.forEach((file, index) => {
            if (file.path && file.path === inputFile.path) {
              companyDocuments.remove(index);
            }
          });
        }
      });
    },
    [companyData.files, companyDocuments, deleteDocuments]
  );

  useEffect(() => {
    form.trigger('companyDocuments');
  }, [form, companyDocuments]);

  const handleClearError = (e) => {
    form.clearErrors(e.target.name);
  };

  const handleClearAutorizeCompanyManagerError = () => {
    form.clearErrors('autorizeCompanyManager');
  };

  const handleClearNoLastNameError = () => {
    form.clearErrors('name');
    form.clearErrors('noLastName');
  };

  return (
    <Stack spacing={2}>
      {form.watch('documentType') === DocumentType.CNPJ.value && (
        <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
          <Typography variant="body2" color="text.primary" fontWeight="bold">
            Dados da empresa
          </Typography>
          <Stack spacing={2} mt={2}>
            <RHFTextField
              name="companyName"
              label="Nome da empresa"
              size="small"
              onChange={handleClearError}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <RHFMaskedTextField
              name="cnpj"
              label="CNPJ"
              mask="99.999.999/9999-99"
              size="small"
              onKeyDown={handleClearError}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <RHFCurrencyTextField
              name="averageRevenue"
              label="Receita Média"
              size="small"
              onChange={() =>
                handleClearError({
                  target: {
                    name: 'averageRevenue',
                  },
                })
              }
              InputLabelProps={{
                shrink: true,
              }}
            />
            <RHFSelect
              name="companyType"
              label="Tipo de Empresa"
              placeholder="Selecione um tipo"
              fullWidth
              onChange={handleClearError}
              size="small"
            >
              {companyTypeOptions.map((company) => (
                <MenuItem key={company.value} value={company.value}>
                  <Stack direction="row" alignItems="center" gap={1}>
                    <span>{company.label}</span>
                  </Stack>
                </MenuItem>
              ))}
            </RHFSelect>
          </Stack>
        </Stack>
      )}
      <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
        {form.watch('documentType') === DocumentType.CNPJ.value && (
          <Typography variant="body2" color="text.primary" fontWeight="bold">
            Dados do administrador da empresa
          </Typography>
        )}
        <Stack spacing={2} mt={2}>
          <RHFTextField
            name="name"
            label="Nome completo (como no documento)"
            size="small"
            onChange={handleClearError}
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
          <RHFCheckbox
            divClassName="!mt-2"
            name="noLastName"
            onChange={handleClearNoLastNameError}
            label={
              <Typography variant="caption" color="text.primary">
                Não possuo sobrenome
              </Typography>
            }
            size="small"
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
          <RHFMaskedTextField
            name="cpf"
            label="CPF"
            mask="999.999.999-99"
            size="small"
            onKeyDown={handleClearError}
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
          <RHFMaskedTextField
            name="birthDate"
            label="Data de nascimento"
            mask="99/99/9999"
            size="small"
            onKeyDown={handleClearError}
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
          <RHFMaskedTextField
            name="phone"
            label="Telefone"
            mask="(99) 99999-9999"
            size="small"
            onKeyDown={handleClearError}
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
          <RHFTextField
            name="motherName"
            label="Nome completo da mãe"
            size="small"
            onChange={handleClearError}
            InputLabelProps={{
              shrink: true,
            }}
            disabled={resubmit}
          />
        </Stack>
      </Stack>
      {form.watch('documentType') === DocumentType.CNPJ.value && (
        <>
          <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
            <Typography variant="body2" color="text.primary" fontWeight="bold">
              Documentos da empresa
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1 }}>
              Por favor, faça o upload do contrato social atualizado da empresa (ou comprovante de
              MEI). Caso o administrador da empresa não esteja no contrato social, também é
              necessário o envio de uma procuração autorizando ele a representar a empresa.
            </Typography>
            <RHFUpload
              multiple
              name="companyDocuments"
              maxSize={10000000}
              onRemove={handleRemoveCompanyDocuments}
              onDrop={handleDropCompanyDocuments}
              accept={{ 'image/*': [], 'application/pdf': [] }}
              helperText="Formatos aceitos: JPG, PNG ou PDF. Tamanho máximo: 10MB"
            />
          </Stack>
          <RHFCheckbox
            name="autorizeCompanyManager"
            onChange={handleClearAutorizeCompanyManagerError}
            label={
              <Typography variant="caption" color="text.primary">
                Confirmo que o responsável acima está autorizado(a) a representar a empresa conforme
                o contrato social ou procuração
              </Typography>
            }
            size="small"
            InputLabelProps={{
              shrink: true,
            }}
          />
        </>
      )}
      <Stack sx={{ pb: 2 }}>
        <Alert severity="warning" variant="standard" sx={{ mb: 2 }}>
          Você não poderá alterar os dados após o envio.
        </Alert>
      </Stack>
    </Stack>
  );
};

export default IdentityVerificationStep2;
