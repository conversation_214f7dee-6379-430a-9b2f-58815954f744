import { Alert, Paper, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { useContext } from 'react';
import { RHFCheckbox } from '../../../components/hook-form';
import MultiFilePreview from '../../../components/upload/preview/MultiFilePreview';
import {
  DocumentType,
  IdentityVerificationContext,
} from '../../../contexts/IdentityVerificationContext';
import { DisplayInfo } from './components';

const IdentityVerificationStep2 = () => {
  const { form } = useContext(IdentityVerificationContext);

  return (
    <Stack spacing={2}>
      <Stack>
        <Alert severity="warning" variant="standard">
          Confirme abaixo se seus dados cadastrais estão corretos
        </Alert>
      </Stack>
      {form.watch('documentType') === DocumentType.CNPJ.value && (
        <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
          <Typography variant="body2" color="text.primary" fontWeight="bold">
            Dad<PERSON> da empresa
          </Typography>
          <Stack spacing={2} mt={2}>
            <DisplayInfo form={form} name="companyName" label="Nome da empresa" />
            <DisplayInfo form={form} name="cnpj" label="CNPJ" />
            <DisplayInfo form={form} isCompanyType name="companyType" label="Tipo da empresa" />
          </Stack>
        </Stack>
      )}
      <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
        {form.watch('documentType') === DocumentType.CNPJ.value && (
          <Typography variant="body2" color="text.primary" fontWeight="bold">
            Dados do administrador da empresa
          </Typography>
        )}
        <Stack spacing={2} mt={2}>
          <DisplayInfo form={form} name="name" label="Nome completo" />
          <DisplayInfo form={form} name="cpf" label="CPF" />
          <DisplayInfo
            form={form}
            name="verificationDocumentType"
            label="Tipo de Documento"
            isUpperCase
          />
          <DisplayInfo form={form} name="verificationDocument" label="Número do documento" />
          <DisplayInfo form={form} name="birthDate" label="Data de nascimento" />
          <DisplayInfo form={form} name="phone" label="Telefone" />
          <DisplayInfo form={form} name="motherName" label="Nome completo da mãe" />
        </Stack>
      </Stack>
      {form.watch('documentType') === DocumentType.CNPJ.value && (
        <>
          <Stack component={Paper} gap={1} variant="outlined" sx={{ p: 2 }}>
            <Typography variant="body2" color="text.primary" fontWeight="bold">
              Documentos da empresa
            </Typography>

            <MultiFilePreview files={form.getValues().companyDocuments} />
          </Stack>
          <RHFCheckbox
            name="autorizeCompanyManager"
            disabled
            label={
              <Typography variant="caption" color="text.primary">
                Confirmo que o responsável acima está autorizado(a) a representar a empresa conforme
                o contrato social ou procuração
              </Typography>
            }
            size="small"
            InputLabelProps={{
              shrink: true,
            }}
          />
        </>
      )}
    </Stack>
  );
};

export default IdentityVerificationStep2;
