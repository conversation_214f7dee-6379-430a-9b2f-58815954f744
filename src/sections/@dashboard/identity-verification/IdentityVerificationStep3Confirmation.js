import { Link, Paper, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { useContext } from 'react';
import { RHFCheckbox } from '../../../components/hook-form';
import { DisplayInfo } from './components';
import { IdentityVerificationContext } from '../../../contexts/IdentityVerificationContext';

const IdentityVerificationStep3 = () => {
  const { form } = useContext(IdentityVerificationContext);

  return (
    <Stack component={Paper} spacing={2} p={2} variant="outlined">
      <DisplayInfo form={form} name="cep" label="CEP" />
      <DisplayInfo form={form} name="address" label="Endereço" />
      <DisplayInfo form={form} name="number" label="Número" />
      <DisplayInfo form={form} name="complement" label="Complemento" />
      <DisplayInfo form={form} name="neighborhood" label="Bairro" />
      <DisplayInfo form={form} name="city" label="Cidade" />
      <DisplayInfo form={form} name="state" label="Estado" />
      <RHFCheckbox
        name="acceptedTerms"
        disabled
        label={
          <Typography variant="caption">
            Eu li e concordo com os <Link href="#">Termos de Uso</Link>,{' '}
            <Link href="#">Termos de Licença de uso de Software</Link> e{' '}
            <Link href="#">Política de Conteúdo</Link> da <Link href="#">Cakto</Link>.
          </Typography>
        }
        size="small"
      />
    </Stack>
  );
};

export default IdentityVerificationStep3;
