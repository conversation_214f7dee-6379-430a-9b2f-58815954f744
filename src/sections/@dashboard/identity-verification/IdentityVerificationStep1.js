import { useAuthContext } from '@/auth/useAuthContext';
import { useContext } from 'react';

import { RHFRadioGroup } from '../../../components/hook-form';
import {
  DocumentType,
  IdentityVerificationContext,
} from '../../../contexts/IdentityVerificationContext';

const IdentityVerificationStep1 = () => {
  const { companyData } = useContext(IdentityVerificationContext);

  const resubmit = companyData?.status === 'resubmission_requested';

  // Condicional para definir os rótulos e valores
  const options = [
    {
      value: DocumentType.CNPJ.value,
      label: resubmit
        ? 'Refazer CNPJ para a minha empresa'
        : 'Eu quero receber na minha empresa. Já tenho um CNPJ aberto',
    },
    {
      value: DocumentType.CPF.value,
      label: resubmit
        ? 'Refazer CPF para minha conta pessoa física'
        : 'Eu quero receber na minha conta pessoa física (CPF)',
    },
  ];

  return <RHFRadioGroup options={options} name="documentType" size="small" />;
};

export default IdentityVerificationStep1;
