import { useState, useContext, useEffect, useLayoutEffect } from 'react';
import { LoadingButton } from '@mui/lab';
import {
  Button,
  CircularProgress,
  Step,
  StepContent,
  StepLabel,
  Stepper,
  Typography,
} from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import FormProvider from '@/components/hook-form/FormProvider';
import Iconify from '@/components/iconify/Iconify';
import { IdentityVerificationContext, DocumentType } from '@/contexts/IdentityVerificationContext';
import { replaceNullWithUndefined } from '@/utils/object';
import { fDate } from '@/utils/formatTime';
import IdentityVerificationApproved from './IdentityVerificationApproved';
import IdentityVerificationStep1 from './IdentityVerificationStep1';
import IdentityVerificationStep2 from './IdentityVerificationStep2';
import IdentityVerificationConfirmation from './IdentityVerificationStep2Confirmation';
import IdentityVerificationStep3 from './IdentityVerificationStep3';
import IdentityVerificationStep3Confirmation from './IdentityVerificationStep3Confirmation';
import IdentityVerificationSendDocuments from './IdentityVerificationSendDocuments';

const IdentityVerificationStepPhotos = ({ setTokenIdWall }) => {
  useEffect(() => {
    const tokenIdWall = process.env.REACT_APP_TOKEN_IDWALL;
    /* global idwSDKWeb */
    idwSDKWeb({
      token: tokenIdWall,
      onRender: () => {
        const iframe = document.querySelector('iframe');
        if (iframe) {
          iframe.style.setProperty('display', 'block', 'important');
        }
      },
      onComplete: ({ token }) => {
        setTokenIdWall(token);
      },
      onError: (error) => {
        console.log(`Erro ao renderizar SDK: ${error}`);
      },
    });
  }, [setTokenIdWall]);

  return (
    <div>
      <h3>Fotos de Verificação de Identidade</h3>
      <div className="flex flex-col items-center justify-center mt-2">
        <div data-idw-sdk-web />
      </div>
    </div>
  );
};
IdentityVerificationStepPhotos.propTypes = {
  setTokenIdWall: PropTypes.string,
};
const IdentityVerificationStepper = () => {
  const { form, step, setStep, send, addDocuments, sending, companyData, isLoadingCompany } =
    useContext(IdentityVerificationContext);

  const [tokenIdWall, setTokenIdWall] = useState(null);
  useEffect(() => {
    if (companyData?.status === 'pending') {
      setStep(0);
    }
  }, [companyData?.status, setStep]);
  const Steps = [
    {
      label: 'Para onde vamos enviar o dinheiro?',
      content: <IdentityVerificationStep1 />,
      fields: ['documentType'],
    },
    {
      label: 'Dados cadastrais',
      content: <IdentityVerificationStep2 />,
      fields: [
        'name',
        'email',
        'cpf',
        'phone',
        'birthDate',
        'motherName',
        'cnpj',
        'companyName',
        'companyDocuments',
        'autorizeCompanyManager',
        'verificationDocumentType',
        'verificationDocument',
      ],
    },
    {
      label: 'Confirmar Dados Cadastrais',
      content: <IdentityVerificationConfirmation />,
      fields: [],
    },
    {
      label: 'Endereço',
      content: <IdentityVerificationStep3 />,
      fields: ['cep', 'address', 'number', 'neighborhood', 'city', 'state', 'acceptedTerms'],
    },
    {
      label: 'Confirmar Endereço',
      content: <IdentityVerificationStep3Confirmation />,
      fields: [],
    },
  ];

  const onSubmit = (values) => {
    console.log(values.verificationDocument);
    const [day, month, year] = values.birthDate.split('/');
    const data = {
      ...values,
      birthDate: `${year}-${month}-${day}`,
      cep: values.cep.replace(/[^a-zA-Z0-9]/g, ''),
      completeName: values.name,
      cpf: values.cpf.replace(/[^a-zA-Z0-9]/g, ''),
      phone: values.phone.replace(/[^a-zA-Z0-9]/g, ''),
      street: values.address,
      user_agreement_accepted: values.autorizeCompanyManager,
    };
    send(data).then(() => {
      const documents = values?.companyDocuments.filter((document) => !document.id);
      if (documents?.length > 0) addDocuments({ files: documents, submitForApproval: false });
    });
  };

  const handleSubmit = async () => {
    const values = form.getValues();

    const allFields = Steps.reduce((acc, item) => {
      acc.push(...item.fields);

      return acc;
    }, []).filter((field) => !['autorizeCompanyManager', 'companyDocuments'].includes(field));

    const isValid = await form.trigger(allFields);

    const { errors } = form.formState;

    const errorsKeys = Object.keys(errors);

    let stepWithError = null;

    for (let index = 0; index < Steps.length; index += 1) {
      const item = Steps[index];
      if (item.fields.some((element) => errorsKeys.includes(element))) {
        stepWithError = index;
        break;
      }
    }

    if (stepWithError) {
      setStep(stepWithError);
    }
    if (isValid) {
      await onSubmit(values);
    }
  };

  const Actions = (
    <Stack direction="row" justifyContent="space-between" mt={2}>
      {step > 0 && step < 6 && (
        <Button size="small" variant="outlined" onClick={() => setStep(step - 1)} color="inherit">
          Voltar
        </Button>
      )}
      {step === 5 && <div />}
      <Stack direction="row" justifyContent="flex-end">
        {' '}
        {/* Alinhamento à direita */}
        {step < 4 && (
          <LoadingButton
            size="small"
            variant="contained"
            loading={sending}
            onClick={() => {
              form.trigger(Steps[step].fields).then(async (valid) => {
                if (valid) {
                  if (step === 4) {
                    const { idwallSdkToken, ...values } = form.getValues();
                    await onSubmit(values);
                  }
                  setStep(step + 1);
                  form.clearErrors();
                }
              });
            }}
          >
            Continuar
          </LoadingButton>
        )}
        {step === 4 && (
          <LoadingButton
            size="small"
            style={{ marginRight: 20 }}
            variant="contained"
            type="button"
            onClick={handleSubmit}
            loading={sending}
          >
            Enviar
          </LoadingButton>
        )}
      </Stack>
    </Stack>
  );

  const lastSteps = ['gateway_pending', 'approved', 'blocked'];
  const sendToListDocuments =
    step === 6 ||
    (companyData?.status === 'waiting_documents' && companyData.acceptedTerms) ||
    lastSteps.includes(companyData?.status);

  const validateForm = async () => {
    const allFields = Steps.reduce((acc, item) => {
      acc.push(...item.fields);
      return acc;
    }, []).filter((field) => !['autorizeCompanyManager', 'companyDocuments'].includes(field));

    await form.trigger(allFields);

    const { errors } = form.formState;
    const errorsKeys = Object.keys(errors);

    let stepWithError = null;

    for (let index = 0; index < Steps.length; index += 1) {
      const item = Steps[index];
      if (item.fields.some((element) => errorsKeys.includes(element))) {
        stepWithError = index;
        break;
      }
    }

    // Se o status for "resubmission_requested", sempre abrir no primeiro step
    if (companyData?.status === 'resubmission_requested') {
      setStep(0);
    } else if (stepWithError !== null) {
      setStep(stepWithError);
    }
  };

  useLayoutEffect(() => {
    if (companyData) {
      form.reset(replaceNullWithUndefined(companyData));

      if (companyData?.completeName) {
        form.setValue('name', companyData?.completeName);
      }

      if (companyData?.street) {
        form.setValue('address', companyData?.street);
      }

      if (companyData?.birthDate) {
        form.setValue('birthDate', fDate(companyData.birthDate, 'dd/MM/yyyy'));
      }

      if (companyData?.type !== 'company') {
        form.setValue('documentType', DocumentType.CPF.value);
      }

      if (companyData?.type === 'company') {
        form.setValue('documentType', DocumentType.CNPJ.value);
        form.setValue('companyDocuments', companyData?.files);
        form.setValue('cnpj', companyData?.companyCnpj);
      }

      form.setValue('autorizeCompanyManager', !!companyData?.autorizeCompanyManager);
    }

    if (companyData?.acceptedTerms) {
      setStep(5);
    }

    if (Steps[1]?.fields?.some((field) => !!companyData[field]?.length)) {
      validateForm();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [companyData, isLoadingCompany]);

  if (isLoadingCompany) {
    return (
      <Stack direction="row" justifyContent="center" alignItems="center" sx={{ height: 250 }}>
        <CircularProgress />
      </Stack>
    );
  }

  if (companyData?.status === 'approved') return <IdentityVerificationApproved />;
  if (['gateway_pending', 'blocked', 'rejected', 'pre_approved'].includes(companyData?.status))
    return <IdentityVerificationSendDocuments />;

  if (
    step > 0 && // Usuário já avançou no fluxo
    form.getValues('documentType') === DocumentType.CPF.value && // CPF foi escolhido no primeiro step
    companyData?.status === 'resubmission_requested' // O status deve ser 'resubmission_requested'
  ) {
    return <IdentityVerificationSendDocuments />;
  }
  return (
    <FormProvider methods={form} onSubmit={form.handleSubmit(onSubmit)}>
      {step < 5 && (
        <Stepper orientation="vertical" activeStep={step}>
          {Steps.map(({ label, content }, index) => (
            <Step key={label} completed={step > index}>
              <StepLabel StepIconComponent={CustomStepperIcon}>{label}</StepLabel>
              <StepContent>
                {content}
                {Actions}
              </StepContent>
            </Step>
          ))}
        </Stepper>
      )}
      {step === 5 && (
        <Stack>
          <Typography variant="body2" paddingTop={2} align="left" fontWeight="thin" fontSize={20}>
            Verificação de Identidade
          </Typography>
          <IdentityVerificationStepPhotos setTokenIdWall={setTokenIdWall} />
          {Actions}
        </Stack>
      )}
    </FormProvider>
  );
};

const CustomStepperIcon = ({ active, completed }) => {
  if (active) {
    return (
      <Iconify
        sx={{
          width: 25,
          height: 25,
        }}
        icon="mdi:edit-circle"
        color="text.primary"
      />
    );
  }
  if (completed) {
    return (
      <Iconify
        sx={{
          width: 25,
          height: 25,
        }}
        icon="eva:checkmark-circle-2-fill"
        color="success.main"
      />
    );
  }
  return (
    <Iconify
      sx={{
        width: 25,
        height: 25,
      }}
      icon="eva:plus-circle-outline"
      color="text.secondary"
    />
  );
};

CustomStepperIcon.propTypes = {
  active: PropTypes.bool,
  completed: PropTypes.bool,
};

export default IdentityVerificationStepper;
