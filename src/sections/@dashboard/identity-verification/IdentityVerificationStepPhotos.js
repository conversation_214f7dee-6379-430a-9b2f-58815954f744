import { IdentityVerificationContext } from '@/contexts/IdentityVerificationContext';
import { Button, Container } from '@mui/material';

const PropTypes = require('prop-types');
const { useEffect, useContext } = require('react');

export const IdentityVerificationStepPhotos = () => {
  const { send, isFetchingCompany } = useContext(IdentityVerificationContext);
  useEffect(() => {
    const tokenIdWall = process.env.REACT_APP_TOKEN_IDWALL;
    /* global idwSDKWeb */
    idwSDKWeb({
      token: tokenIdWall,
      onRender: () => {
        const iframe = document.querySelector('iframe');
        if (iframe) {
          iframe.style.setProperty('display', 'block', 'important');
        }
      },
      onComplete: ({ token }) => {
        send({ idwallSdkToken: token, user_agreement_accepted: true });
        isFetchingCompany();
      },
      onError: (error) => {
        console.log(`Erro ao renderizar SDK: ${error}`);
      },
    });
  }, [isFetchingCompany, send]);

  return (
    <div
      style={{
        width: '350px',
        display: 'flex',
        flexDirection: 'column', // Alinha os itens no eixo vertical
        justifyContent: 'space-between', // Distribui o espaço entre os itens
        alignItems: 'center',
      }}
    >
      <h3 style={{ marginBottom: 'auto' }}>Fotos de Verificação de Identidade</h3>{' '}
      {/* Alinha o texto no topo */}
      <div data-idw-sdk-web style={{ marginTop: 'auto' }} /> {/* Coloca o div no fundo */}
    </div>
  );
};
