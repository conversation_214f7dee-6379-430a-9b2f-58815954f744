import { useContext } from 'react';
import { Alert } from '@mui/material';
import { IdentityVerificationContext } from '../../../contexts/IdentityVerificationContext';
import { useAuthContext } from '../../../auth/useAuthContext';

const IdentityVerificationWarning = () => {
  const { user } = useAuthContext();

  const { companyData } = useContext(IdentityVerificationContext);

  const reasons = companyData?.rejectedReasons;

  const plural = reasons?.length > 1 ? 's' : '';

  const verificationErrorMessage = reasons
    ? `A verificação de identidade falhou pelo${plural} motivo${plural}:
    - ${reasons.join('\n- ')}
    \nPor favor, envie novamente observando estes pontos e conferindo todos os dados.`
    : `A verificação de identidade falhou. Possíveis razões para a falha podem incluir:
    - Falta de visibilidade do documento ou selfie.
    - Dados incorretos, faltantes ou ilegíveis.
    - Fotos enviadas de baixa qualidade.
    \nPor favor, envie novamente observando estes pontos e conferindo todos os dados.`;

  // Split the message by '\n' and map to render each line
  const formattedMessage = verificationErrorMessage.split('\n').map((line, index) => (
    <span key={index}>
      {line}
      <br />
    </span>
  ));

  return (
    <>
      {['rejected'].includes(user?.companyStatus) && (
        <Alert severity="error" variant="standard">
          {formattedMessage}
        </Alert>
      )}
    </>
  );
};

export default IdentityVerificationWarning;
