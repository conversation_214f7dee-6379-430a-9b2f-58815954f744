import { InputAdornment, Link, Paper, Typography, Alert, CircularProgress } from '@mui/material';
import { Stack } from '@mui/system';
import { useContext } from 'react';

import { RHFCheckbox, RHFTextField } from '../../../components/hook-form';
import RHFMaskedTextField from '../../../components/hook-form/RHFMaskedTextField';
import Iconify from '../../../components/iconify';
import { IdentityVerificationContext } from '../../../contexts/IdentityVerificationContext';

const IdentityVerificationStep3 = () => {
  const { form, fetchAddress, isFetchingAddress } = useContext(IdentityVerificationContext);
  const handleClearError = (e) => {
    form.clearErrors(e.target.name);
  };

  const handleClearAcceptedTermsError = () => {
    form.clearErrors('acceptedTerms');
  };

  return (
    <Stack component={Paper} spacing={2} p={2} variant="outlined">
      <RHFMaskedTextField
        name="cep"
        label="CEP"
        mask="99999-999"
        size="small"
        onKeyDown={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
        onBlur={(e) => {
          fetchAddress();
        }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              {isFetchingAddress ? (
                <CircularProgress size={20} />
              ) : (
                <Iconify icon="eva:search-outline" />
              )}
            </InputAdornment>
          ),
        }}
      />
      <RHFTextField
        name="address"
        label="Endereço"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <RHFTextField
        name="number"
        label="Número"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <RHFTextField
        name="complement"
        label="Complemento"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <RHFTextField
        name="neighborhood"
        label="Bairro"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <RHFTextField
        name="city"
        label="Cidade"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <RHFTextField
        name="state"
        label="Estado"
        size="small"
        onChange={handleClearError}
        InputLabelProps={{
          shrink: true,
        }}
      />
      <Stack>
        <Alert severity="warning" variant="standard">
          Você não poderá alterar os dados após o envio.
        </Alert>
      </Stack>
      <RHFCheckbox
        name="acceptedTerms"
        onChange={handleClearAcceptedTermsError}
        label={
          <Typography variant="caption">
            Eu li e concordo com os <Link href="#">Termos de Uso</Link>,{' '}
            <Link href="#">Termos de Licença de uso de Software</Link> e{' '}
            <Link href="#">Política de Conteúdo</Link> da <Link href="#">Cakto</Link>.
          </Typography>
        }
        size="small"
      />
    </Stack>
  );
};

export default IdentityVerificationStep3;
