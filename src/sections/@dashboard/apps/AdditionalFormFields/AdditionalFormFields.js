import PropTypes from 'prop-types';
import { Fragment, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  MenuItem,
  Stack,
  Skeleton,
} from '@mui/material';
import { RHFMultiSelect, RHFTextField, RHFSelect } from '@/components/hook-form';

const AdditionalFormFields = ({ plataform }) => {
  useEffect(() => {
    plataform?.initialFunction();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (plataform?.isLoading)
    return (
      <Stack data-testid="loading">
        <Skeleton sx={{ height: 60 }} />
        <Skeleton sx={{ height: 60 }} />
        <Skeleton sx={{ height: 60 }} />
      </Stack>
    );

  // eslint-disable-next-line react/jsx-no-useless-fragment
  if (!plataform?.isVisible) return <></>;

  return plataform?.fields?.map((field, index) => {
    if (field.fieldType === 'radioWithInput') {
      return (
        <FormControl key={index}>
          <FormLabel>{field.label}</FormLabel>

          <RadioGroup value={field.value} onChange={field.onChange}>
            {field.options.map((option) => (
              <Fragment key={option.label}>
                <FormControlLabel value={option.value} control={<Radio />} label={option.label} />

                {option.input?.type === 'select' && field?.value === option?.value && (
                  <RHFSelect
                    label={option.input.placeholder}
                    size="small"
                    name={`fields.${option.input.name}`}
                    inputProps={{
                      'data-testid': 'select',
                    }}
                  >
                    {option.input.options.length > 0 ? (
                      option.input.options.map(({ value, label }) => (
                        <MenuItem key={label} value={value}>
                          <Stack direction="row" alignItems="center" gap={1}>
                            <span>{label}</span>
                          </Stack>
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem key="no-options" value={undefined} disabled>
                        <Stack direction="row" alignItems="center" gap={1}>
                          <span data-testid="no-options">Nenhuma opção disponível</span>
                        </Stack>
                      </MenuItem>
                    )}
                  </RHFSelect>
                )}

                {option?.input?.type === 'multiSelect' && field?.value === option?.value && (
                  <RHFMultiSelect
                    size="small"
                    chip
                    checkbox
                    options={option.input.options}
                    name={`fields.${option.input.name}`}
                    placeholder={option.input.placeholder}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                )}
              </Fragment>
            ))}
          </RadioGroup>
        </FormControl>
      );
    }

    if (field.fieldType === 'select') {
      return (
        <RHFSelect
          key={index}
          label={field.label}
          size="small"
          name={`fields.${field.name}`}
          inputProps={{
            'data-testid': 'select',
          }}
          value={field.value}
          onChange={field.onChange}
        >
          {field.options?.length > 0 ? (
            field.options.map(({ value, label }) => (
              <MenuItem key={label} value={value}>
                <Stack direction="row" alignItems="center" gap={1}>
                  <span>{label}</span>
                </Stack>
              </MenuItem>
            ))
          ) : (
            <MenuItem key="no-options" value="disabled" disabled>
              <Stack direction="row" alignItems="center" gap={1}>
                <span data-testid="no-options">Nenhuma opção disponível</span>
              </Stack>
            </MenuItem>
          )}
        </RHFSelect>
      );
    }

    return (
      <RHFTextField
        key={index}
        size="small"
        name={`fields.${field.name}`}
        label={field.label}
        type={field.type}
        placeholder={field.placeholder}
        InputLabelProps={{
          shrink: true,
        }}
      />
    );
  });
};

AdditionalFormFields.propTypes = {
  plataform: PropTypes.shape({
    isLoading: PropTypes.bool,
    isVisible: PropTypes.bool,
    fields: PropTypes.array,
    initialFunction: PropTypes.func,
  }),
};

export default AdditionalFormFields;
