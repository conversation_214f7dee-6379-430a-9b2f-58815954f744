import { render, screen, fireEvent } from '@testing-library/react';
import AdditionalFormFields from './AdditionalFormFields';

describe('AdditionalFormFields', () => {
  const mockInitialFunction = jest.fn();

  const mockPlataform = {
    isLoading: false,
    isVisible: true,
    fields: [
      {
        fieldType: 'radioWithInput',
        label: 'Choose option',
        value: 'option1',
        onChange: jest.fn(),
        options: [
          {
            label: 'Option 1',
            value: 'option1',
            input: {
              type: 'select',
              name: 'selectOption',
              placeholder: 'Select something',
              options: [
                { value: 'value1', label: 'Value 1' },
                { value: 'value2', label: 'Value 2' },
                { value: undefined, label: '' },
              ],
            },
          },
          {
            label: 'Option 2',
            value: 'option2',
          },
        ],
      },
      {
        fieldType: 'text',
        name: 'textField',
        label: 'Enter text',
        type: 'text',
        placeholder: 'Type something',
      },
      {
        fieldType: 'select',
        name: 'select<PERSON>ield',
        label: 'Select an option',
        placeholder: 'Select an option',
        options: [
          { value: 'option1', label: 'Option 11' },
          { value: 'option2', label: 'Option 22' },
          { value: undefined, label: '' },
        ],
      },
    ],
    initialFunction: mockInitialFunction,
  };

  it('should call initialFunction on mount', () => {
    render(<AdditionalFormFields plataform={mockPlataform} />);
    expect(mockInitialFunction).toHaveBeenCalled();
  });

  it('should display loading skeleton when isLoading is true', () => {
    render(<AdditionalFormFields plataform={{ ...mockPlataform, isLoading: true }} />);
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('should not render anything when isVisible is false', () => {
    const { container } = render(
      <AdditionalFormFields plataform={{ ...mockPlataform, isVisible: false }} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render radioWithInput fields correctly', () => {
    render(<AdditionalFormFields plataform={mockPlataform} />);

    expect(screen.getByText('Choose option')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 1')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 2')).toBeInTheDocument();
  });

  it('should render RHFSelect when option1 is selected', () => {
    render(<AdditionalFormFields plataform={mockPlataform} />);

    expect(screen.getByLabelText('Select something')).toBeInTheDocument();
  });

  it('should render RHFTextField for text field', () => {
    render(<AdditionalFormFields plataform={mockPlataform} />);
    expect(screen.getByLabelText('Select something')).toBeInTheDocument();
  });

  it('should handle radio button change', () => {
    render(<AdditionalFormFields plataform={mockPlataform} />);

    fireEvent.click(screen.getByLabelText('Option 2'));
    expect(mockPlataform.fields[0].onChange).toHaveBeenCalled();
  });

  it('should render "Nenhuma opção disponível" when select options are empty', async () => {
    const platformWithEmptySelectOptions = {
      isLoading: false,
      isVisible: true,
      fields: [
        {
          fieldType: 'radioWithInput',
          label: 'Choose option',
          value: 'option1',
          onChange: jest.fn(),
          options: [
            {
              label: 'Option 1',
              value: 'option1',
              input: {
                type: 'select',
                name: 'selectOption',
                placeholder: 'Select something',
                options: [],
              },
            },
          ],
        },
      ],
      initialFunction: mockInitialFunction,
    };

    render(<AdditionalFormFields plataform={platformWithEmptySelectOptions} />);
    const select = screen.getByTestId('select');
    expect(select).toBeInTheDocument();
  });
});
