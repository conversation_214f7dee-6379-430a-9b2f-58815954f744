import ConfirmDialog from '@/components/confirm-dialog/ConfirmDialog';
import Iconify from '@/components/iconify';
import MenuPopover from '@/components/menu-popover/MenuPopover';
import { WebhookTableModal } from '@/components/modal-log-webhook';
import { FormScopes } from '@/utils/form';
import {
  Button,
  Dialog,
  DialogContent,
  Drawer,
  IconButton,
  MenuItem,
  TableCell,
  Typography,
} from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useMemo, useState } from 'react';

import AppSendTestEventModal from '../AppSendTestEventModal';
import AppsIntegrationForm from '../AppsIntegrationForm';
import AppsIntegrationsTable from '../AppsIntegrationsTable';

import useAppsIntegrationsDrawer from './useAppsIntegrationsDrawer';

AppsIntegrationsDrawer.propTypes = {
  platform: PropTypes.object,
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default function AppsIntegrationsDrawer({ platform, open, onClose }) {
  const {
    scope,
    setScope,
    table,
    selectedApp,
    setSelectedApp,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    deleteApp,
    isLoadingDeleteApp,
    onSubmit,
  } = useAppsIntegrationsDrawer(platform);

  const [anchorEl, setAnchorEl] = useState(null);
  const [openLogs, setOpenLogs] = useState(false);

  const defaultValues = useMemo(
    () => ({
      name: selectedApp?.name || '',
      url: selectedApp?.url || '',
      products: selectedApp?.products?.map(({ id }) => id) || [],
      events: selectedApp?.events?.map(({ custom_id }) => custom_id) || [],
      fields: selectedApp?.fields || {},
    }),
    [selectedApp]
  );
  const handleCloseLogs = () => {
    setOpenLogs(false);
  };
  return (
    <>
      <MenuPopover open={anchorEl} onClose={() => setAnchorEl(null)}>
        <MenuItem
          onClick={() => {
            setSelectedApp(selectedApp);
            setScope(FormScopes.SEND_TEST_EVENT);
            setAnchorEl(null);
          }}
        >
          <Iconify icon="material-symbols:send" />
          <Typography variant="body2">Enviar evento de teste</Typography>
        </MenuItem>
        <MenuItem
          onClick={() => {
            setSelectedApp(selectedApp);
            setScope(FormScopes.EDIT);
            setAnchorEl(null);
          }}
        >
          <Iconify icon="eva:edit-2-fill" />
          <Typography variant="body2">Editar</Typography>
        </MenuItem>
        <MenuItem
          onClick={() => {
            setOpenLogs(true);
            setAnchorEl(null);
          }}
        >
          <Iconify icon="foundation:list-number" />
          <Typography variant="body2">Logs</Typography>
        </MenuItem>
        <MenuItem
          onClick={() => {
            setIsDeleteModalOpen(true);
            setAnchorEl(null);
          }}
        >
          <Iconify icon="eva:trash-2-fill" />
          <Typography variant="body2">Excluir</Typography>
        </MenuItem>
      </MenuPopover>
      <AppSendTestEventModal
        scope={scope}
        setScope={setScope}
        platform={platform}
        selectedApp={selectedApp}
      />
      {openLogs && (
        <WebhookTableModal open={openLogs} onClose={handleCloseLogs} id={selectedApp.id} />
      )}

      <Dialog
        fullWidth
        maxWidth="sm"
        open={[FormScopes.CREATE, FormScopes.EDIT].includes(scope)}
        onClose={() => setScope(FormScopes.INDEX)}
        data-testid="create-edit-dialog"
      >
        <DialogContent sx={{ py: 2 }}>
          <AppsIntegrationForm
            scope={scope}
            platform={platform}
            onSubmit={onSubmit[scope]}
            onCancel={() => setScope(FormScopes.INDEX)}
            defaultValues={scope === FormScopes.EDIT && defaultValues}
            table={table}
          />
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        action={
          <Button
            variant="contained"
            color="error"
            startIcon={<Iconify icon="eva:trash-2-outline" />}
            onClick={() => deleteApp({ appId: selectedApp.id })}
            disabled={isLoadingDeleteApp}
            data-testid="remove-button"
          >
            Remover
          </Button>
        }
        title="Remover integração"
        content={`Tem certeza que deseja remover a integração ${selectedApp?.name}?`}
      />
      <Drawer
        anchor="right"
        open={open}
        onClose={onClose}
        PaperProps={{
          sx: {
            p: 2,
            width: {
              xs: '100%',
              sm: 600,
            },
          },
        }}
      >
        <Stack gap={2}>
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton data-testid="close-drawer" onClick={onClose}>
              <Iconify icon="eva:chevron-right-fill" />
            </IconButton>
            <Stack>
              <Typography variant="subtitle1">{platform.name}</Typography>
              <Typography variant="caption" color="text.secondary">
                Configure as integrações com os seus apps
              </Typography>
            </Stack>
          </Stack>

          <Stack direction="row" justifyContent="flex-end" spacing={2}>
            <Button
              variant="contained"
              color="primary"
              onClick={() => setScope(FormScopes.CREATE)}
              startIcon={<Iconify icon="eva:plus-fill" />}
              data-testid="add-button"
            >
              Adicionar
            </Button>
          </Stack>
          <AppsIntegrationsTable
            table={table}
            platform={platform}
            onRowClick={(row) => {
              setSelectedApp(row);
              setScope(FormScopes.EDIT);
            }}
            renderRow={(row) => (
              <>
                <TableCell align="left" width={10}>
                  <IconButton
                    size="small"
                    data-testid="open-drawer"
                    onClick={(event) => {
                      setAnchorEl(event.currentTarget);
                      setSelectedApp(row);
                      event.stopPropagation();
                    }}
                  >
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                </TableCell>
                <TableCell>{row.name}</TableCell>
                <TableCell>{row.url}</TableCell>
              </>
            )}
          />
        </Stack>
      </Drawer>
    </>
  );
}
