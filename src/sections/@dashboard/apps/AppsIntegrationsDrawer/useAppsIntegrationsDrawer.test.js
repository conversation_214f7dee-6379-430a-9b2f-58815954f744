import { AppErrorRendererCard } from '@/components/snackbar/AppErrorRenderer';
import { AppError } from '@/utils/api';
import { FormScopes } from '@/utils/form';
import { afterEach, beforeEach, describe, expect, it, jest } from '@jest/globals';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { waitFor } from '@testing-library/dom';
import { act, renderHook } from '@testing-library/react-hooks';
import { useSnackbar } from 'notistack';
import useAppsIntegrationsDrawer from './useAppsIntegrationsDrawer';

jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQueryClient: jest.fn(),
  useMutation: jest.fn(),
}));

describe('useAppsIntegrationsDrawer', () => {
  beforeEach(() => {
    useMutation.mockImplementation(({ onSuccess, mutationFn }) => ({
      mutateAsync() {
        mutationFn({ test: 'test' });
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return the correct values', () => {
    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    expect(result.current.scope).toEqual('index');
    expect(result.current.isDeleteModalOpen).toEqual(false);
    expect(result.current.isLoadingDeleteApp).toEqual(false);
  });

  it('should call queryClient.invalidateQueries function in case of success when createApp function will be called', () => {
    const invalidateQueriesMock = jest.fn();
    const queryClientMock = { invalidateQueries: invalidateQueriesMock };
    useQueryClient.mockReturnValue(queryClientMock);

    useMutation.mockImplementation(({ onSuccess }) => ({
      mutateAsync() {
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    const queryKeyMock = ['apps', 'test', 1, 10, '', { products: [] }];

    act(() => {
      result.current.onSubmit[FormScopes.CREATE]();
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith('Integração criada com sucesso', {
      variant: 'success',
    });
    expect(invalidateQueriesMock).toHaveBeenCalledWith(queryKeyMock);
  });

  it('should call enqueueSnackbar function with an error message in case of error when createApp function will be called', () => {
    const invalidateQueriesMock = jest.fn();
    const queryClientMock = { invalidateQueries: invalidateQueriesMock };
    useQueryClient.mockReturnValue(queryClientMock);

    const expectedError = new AppError({
      response: {
        status: 400,
      },
      message: 'Erro ao processar requisição',
    });

    useMutation.mockImplementation(({ onError }) => ({
      mutateAsync() {
        onError(expectedError);
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    act(() => {
      result.current.onSubmit[FormScopes.CREATE]();
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith(
      <AppErrorRendererCard dataTestId="api-error-renderer" message={expectedError.message} />,
      {
        variant: 'error',
      }
    );
  });

  it('should call queryClient.invalidateQueries function in case of success when updateApp function will be called', () => {
    const invalidateQueriesMock = jest.fn();
    const queryClientMock = { invalidateQueries: invalidateQueriesMock };
    useQueryClient.mockReturnValue(queryClientMock);

    useMutation.mockImplementation(({ onSuccess }) => ({
      mutateAsync() {
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    const queryKeyMock = ['apps', 'test', 1, 10, '', { products: [] }];

    act(() => {
      result.current.onSubmit[FormScopes.EDIT]();
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith(
      'Integração atualizada com sucesso',
      {
        variant: 'success',
      }
    );
    expect(invalidateQueriesMock).toHaveBeenCalledWith(queryKeyMock);
  });

  it('should call queryClient.invalidateQueries function and the enqueueSnackbar function with an error message in case of error when updateApp function will be called', () => {
    const invalidateQueriesMock = jest.fn();
    const queryClientMock = { invalidateQueries: invalidateQueriesMock };
    useQueryClient.mockReturnValue(queryClientMock);

    const expectedError = new AppError({
      response: {
        status: 400,
      },
      message: 'Erro ao processar requisição',
    });

    useMutation.mockImplementation(({ onError }) => ({
      mutateAsync() {
        onError(expectedError);
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    const queryKeyMock = ['apps', 'test', 1, 10, '', { products: [] }];

    act(() => {
      result.current.onSubmit[FormScopes.EDIT]();
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith(
      <AppErrorRendererCard dataTestId="api-error-renderer" message={expectedError.message} />,
      {
        variant: 'error',
      }
    );
    expect(invalidateQueriesMock).toHaveBeenCalledWith(queryKeyMock);
  });

  it('should call enqueueSnackbar function with a success message in case of success when deleteApp function will be called', () => {
    const setQueryDataMock = jest.fn();
    const previousData = {
      results: [
        {
          id: 'abc123',
        },
      ],
    };
    const queryClientMock = {
      invalidateQueries: jest.fn(),
      cancelQueries: jest.fn(),
      setQueryData: setQueryDataMock,
      getQueryData: jest.fn().mockReturnValue(previousData),
    };
    useQueryClient.mockReturnValue(queryClientMock);

    useMutation.mockImplementation(({ onSuccess, onMutate, mutationFn }) => ({
      mutateAsync() {
        mutationFn({ appId: 'abc123' });
        onMutate({ appId: 'abc123' });
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    act(() => {
      result.current.deleteApp();
    });

    waitFor(() => {
      const queryKey = ['apps', 'test', 1, 10, '', { products: [] }];
      const results = { results: [] };

      expect(setQueryDataMock).toHaveBeenCalledWith(queryKey, results);
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith('Integração excluída com sucesso', {
      variant: 'success',
    });
  });

  it('should call queryClient.invalidateQueries function and the enqueueSnackbar function with an error message in case of error when deleteApp function will be called', () => {
    const invalidateQueriesMock = jest.fn();
    const queryClientMock = { invalidateQueries: invalidateQueriesMock };
    useQueryClient.mockReturnValue(queryClientMock);

    const expectedError = new AppError({
      response: {
        status: 400,
      },
      message: 'Erro ao processar requisição',
    });

    useMutation.mockImplementation(({ onError }) => ({
      mutateAsync() {
        onError(expectedError);
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationsDrawer({ type: 'test' }), {
      wrapper: global.wrapper,
    });

    const queryKeyMock = ['apps', 'test', 1, 10, '', { products: [] }];

    act(() => {
      result.current.deleteApp();
    });

    expect(useSnackbar().enqueueSnackbar).toHaveBeenCalledWith(
      <AppErrorRendererCard dataTestId="api-error-renderer" message={expectedError.message} />,
      {
        variant: 'error',
      }
    );
    expect(invalidateQueriesMock).toHaveBeenCalledWith(queryKeyMock);
  });
});
