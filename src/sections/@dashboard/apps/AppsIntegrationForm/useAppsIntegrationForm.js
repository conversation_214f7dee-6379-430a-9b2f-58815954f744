import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { FormScopes } from '@/utils/form';
import { getMemberkitData, getActiveCampaignData } from '@/services/apps';
import { getProductsService } from '@/services/products';
import { getContentDeliveryType } from '../utils';
import { BaseSchema, FieldsSchema } from './validations';

export default function useAppsIntegrationForm({ platform, scope, defaultValues }) {
  const [error, setError] = useState('');

  const form = useForm({
    context: {
      platform,
    },
    resolver: yupResolver(
      BaseSchema.concat(
        yup.object().shape({
          fields: FieldsSchema[platform.type] || yup.object().shape({}),
        })
      )
    ),
    defaultValues: {
      name: '',
      url: '',
      secret: '',
      products: [],
      events: [],
      fields: {},
    },
  });

  const {
    mutate: mutateGetActiveCampaignData,
    data: activeCampaignData,
    isLoading: isLoadingActiveCampaignData,
    isSuccess: isGetActiveCampaignSuccess,
  } = useMutation({
    mutationFn: getActiveCampaignData,
    onSuccess() {},
    onError() {
      setError('Ocorreu um erro. Favor verificar se os dados estão corretos.');
    },
  });

  const {
    mutate: mutateGetMemeberKitData,
    data: memberkitData,
    isLoading: isLoadingMemberkitData,
    isSuccess: isGetMemberkitDataSuccess,
  } = useMutation({
    mutationFn: getMemberkitData,
    onSuccess() {
      if (scope === FormScopes.CREATE) {
        form.setValue('fields.access.classroom_ids', []);
        form.setValue('fields.access.membership_level_id', null);
      }
    },
    onError() {
      setError('Ocorreu um erro. Favor verificar se sua chave de secreta é uma chave válida');
    },
  });

  const { data } = useQuery({
    queryKey: ['products-external', { contentDeliveries: getContentDeliveryType(platform.type) }],
    queryFn: () =>
      getProductsService({
        status: 'active',
        page: 1,
        limit: 1000,
        contentDeliveries: getContentDeliveryType(platform.type),
      }),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });

  const products = data?.products;

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValues]);

  return {
    mutateGetMemeberKitData,
    memberkitData,
    isLoadingMemberkitData,
    isGetMemberkitDataSuccess,
    mutateGetActiveCampaignData,
    activeCampaignData,
    isLoadingActiveCampaignData,
    isGetActiveCampaignSuccess,
    products,
    error,
    setError,
    form,
  };
}
