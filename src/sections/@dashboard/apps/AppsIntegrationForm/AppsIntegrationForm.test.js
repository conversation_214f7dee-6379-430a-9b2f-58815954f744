import React from 'react';
import { jest, it, expect } from '@jest/globals';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { FormScopes } from '@/utils/form';
import { PLATFORMS } from '@/consts/dashboardApps';
import * as notistash from 'notistack';
import * as useCopyToClipboard from '@/hooks/useCopyToClipboard';
import AppsIntegrationForm from './AppsIntegrationForm';
import * as useAppsIntegrationForm from './useAppsIntegrationForm';

jest.mock('@/components/hook-form', () => ({
  RHFMultiSelect: jest.fn(({ name }) => <input data-testid={name} />),
  FormProvider: jest.fn(({ children, methods, onSubmit }) => (
    <form onSubmit={onSubmit}>{children}</form>
  )),
}));

jest.mock('@/hooks/useCopyToClipboard', () =>
  jest.fn(() => ({
    copy: jest.fn(),
  }))
);

jest.mock('notistack', () => ({
  useSnackbar: jest.fn(),
}));

jest.mock('../AdditionalFormFields', () => {
  return jest.fn((props) => {
    if (props?.plataform?.initialFunction) {
      props?.plataform?.initialFunction();
    }

    if (props?.plataform?.fields) {
      const input = props.plataform.fields.find(({ fieldType }) => fieldType === 'radioWithInput');

      return <input data-testid="additional-input" onChange={input.onChange} />;
    }
    return <div>Mocked AdditionalFormFields</div>;
  });
});

const defaultUseIntegrationFormSpyValues = {
  mutateGetMemeberKitData: jest.fn(),
  memberkitData: {},
  isLoadingMemberkitData: false,
  isGetMemberkitDataSuccess: false,
  products: [{ id: 1, name: 'prodict 1' }],
  error: '',
  setError: jest.fn(),
  form: {
    handleSubmit: jest.fn((fn) => (e) => {
      e.preventDefault();
      fn();
    }),
    watch: jest.fn().mockReturnValue([1]),
    setValue: jest.fn(),
    formState: { isSubmitting: false },
  },
};

describe('AppsIntegrationForm', () => {
  const platformMock = {
    type: PLATFORMS.memberkit,
    name: 'MemberKit',
  };

  const defaultValuesMock = {
    fields: {
      access: {
        classroom_ids: [],
        membership_level_id: null,
        unlimited: false,
      },
      secret_key: 'secret123',
    },
  };

  const onSubmitMock = jest.fn();
  const onCancelMock = jest.fn();

  let useAppsIntegrationFormSpy;

  beforeEach(() => {
    global.navigator.clipboard = {
      writeText: jest.fn(),
    };

    jest.spyOn(notistash, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: jest.fn(),
    });
    useAppsIntegrationFormSpy = jest.spyOn(useAppsIntegrationForm, 'default');
    useAppsIntegrationFormSpy.mockReturnValue(defaultUseIntegrationFormSpyValues);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the form with the correct fields', () => {
    const { container } = render(
      <AppsIntegrationForm
        platform={platformMock}
        defaultValues={defaultValuesMock}
        scope={FormScopes.CREATE}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    expect(screen.getByText(/Adicionar MemberKit/i)).toBeInTheDocument();
    expect(container.querySelector('[name="name"]')).toBeInTheDocument();
    expect(screen.getByLabelText('Nome')).toBeInTheDocument();
    expect(screen.getByTestId('products')).toBeInTheDocument();
  });

  it('should show the Chip component and copy the id product by the onClick event when the plataform is equals "cademi"', async () => {
    const platform = {
      type: PLATFORMS.cademi,
      name: 'Cademi',
      events: [{ custom_id: 'abc123', name: 'test_event' }],
    };

    const enqueueSnackbarMock = jest.fn();

    jest.spyOn(notistash, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: enqueueSnackbarMock,
    });

    render(
      <AppsIntegrationForm
        platform={platform}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    await navigator.clipboard.writeText.mockResolvedValueOnce();

    fireEvent.click(screen.getByTestId('platform-chip'));

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(1);

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalledWith('Copiado com sucesso', {
        variant: 'success',
      });
    });
  });

  it('should show the Chip component and throw an error if was occurred an error in the onClick event when the platform is equals "cademi"', async () => {
    const platform = {
      type: PLATFORMS.cademi,
      name: 'Cademi',
    };

    const enqueueSnackbarMock = jest.fn();

    jest.spyOn(notistash, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: enqueueSnackbarMock,
    });

    render(
      <AppsIntegrationForm
        platform={platform}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    await navigator.clipboard.writeText.mockRejectedValueOnce();

    fireEvent.click(screen.getByTestId('platform-chip'));

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(1);

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalledWith('Falha ao copiar', { variant: 'error' });
    });
  });

  it('should call handleChangeMemberkitIntegration function when the input is changed', () => {
    const mockSetMemberkitIntegration = jest.fn();

    jest.spyOn(React, 'useState').mockImplementation((init) => [init, mockSetMemberkitIntegration]);

    const mockSetValue = jest.fn();
    const mockForm = {
      setValue: mockSetValue,
      watch: jest.fn().mockReturnValue([]),
      handleSubmit: jest.fn(),
      formState: { isSubmitting: false },
    };

    useAppsIntegrationFormSpy = jest.spyOn(useAppsIntegrationForm, 'default');
    useAppsIntegrationFormSpy.mockReturnValue({
      ...defaultUseIntegrationFormSpyValues,
      form: mockForm,
      memberkitData: {
        subscriptions: [{ id: 1, name: 'test' }],
        courses: [{ id: 1, name: 'test' }],
      },
    });

    const defaultValues = {
      fields: {
        access: {
          classroom_ids: ['abc123'],
          membership_level_id: 'abc123',
          unlimited: true,
        },
        secret_key: 'secret123',
      },
    };

    render(
      <AppsIntegrationForm
        platform={platformMock}
        defaultValues={defaultValues}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );
    const input = screen.getByTestId('additional-input');
    fireEvent.change(input, { target: { value: 'unlimited' } });

    expect(mockSetValue).toHaveBeenCalledWith('fields.access.classroom_ids', []);
    expect(mockSetValue).toHaveBeenCalledWith('fields.access.membership_level_id', null);
    expect(mockSetValue).toHaveBeenCalledWith('fields.access.unlimited', true);
    expect(mockSetMemberkitIntegration).toHaveBeenCalledWith('unlimited');
  });

  it('should call mutateGetMemeberKitData in the onChange secret_key input if the value has length equals 24 in the memberkit platform', () => {
    const platform = {
      type: PLATFORMS.memberkit,
    };
    const mutateGetMemeberKitDataMock = jest.fn();

    useAppsIntegrationFormSpy.mockReturnValue({
      ...defaultUseIntegrationFormSpyValues,
      mutateGetMemeberKitData: mutateGetMemeberKitDataMock,
    });

    const { container } = render(
      <AppsIntegrationForm
        platform={platform}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    fireEvent.change(container.querySelector('[name="fields.secret_key"]'), {
      target: { value: '111111111111111111111111' },
    });

    expect(mutateGetMemeberKitDataMock).toHaveBeenCalledWith('secret123');
  });

  it('should call copy function when copy button is clicked', () => {
    const platform = {
      type: PLATFORMS.webhooks,
      name: 'Webhooks',
    };

    const enqueueSnackbarMock = jest.fn();

    jest.spyOn(notistash, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: enqueueSnackbarMock,
    });

    const copyMock = jest.fn();

    jest.spyOn(useCopyToClipboard, 'default').mockReturnValue({
      copy: copyMock,
    });

    render(
      <AppsIntegrationForm
        platform={platform}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    fireEvent.click(screen.getByTestId('copy-button'));

    expect(enqueueSnackbarMock).toHaveBeenCalledWith('Chave secreta copiada com sucesso!');

    expect(copyMock).toHaveBeenCalled();
  });

  it('should show input content when the handleShowInputContent function is triggered', async () => {
    const platform = {
      type: PLATFORMS.webhooks,
      name: 'Webhooks',
    };

    const { container } = render(
      <AppsIntegrationForm
        platform={platform}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    fireEvent.click(screen.getByTestId('show-input-button'));

    const input = container.querySelector('[name="fields.secret"]');

    waitFor(() => {
      expect(input).toHaveAttribute('type', 'text');
    });
  });

  it('should show an error message when there is an error', () => {
    useAppsIntegrationFormSpy.mockReturnValue({
      ...defaultUseIntegrationFormSpyValues,
      error: 'Erro ao carregar dados',
    });

    render(
      <AppsIntegrationForm
        platform={platformMock}
        defaultValues={defaultValuesMock}
        scope={FormScopes.CREATE}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    expect(screen.getByText(/Erro ao carregar dados/i)).toBeInTheDocument();
  });

  it('should call onSubmit when the form is submitted', async () => {
    render(
      <AppsIntegrationForm
        platform={platformMock}
        defaultValues={defaultValuesMock}
        scope={FormScopes.EDIT}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    fireEvent.submit(screen.getByRole('button', { name: /Salvar/i }));

    await waitFor(() => {
      expect(onSubmitMock).toHaveBeenCalled();
    });
  });

  it('should call onCancel when the cancel button is clicked', () => {
    render(
      <AppsIntegrationForm
        platform={platformMock}
        defaultValues={defaultValuesMock}
        scope={FormScopes.CREATE}
        onSubmit={onSubmitMock}
        onCancel={onCancelMock}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /Cancelar/i }));

    expect(onCancelMock).toHaveBeenCalled();
  });
});
