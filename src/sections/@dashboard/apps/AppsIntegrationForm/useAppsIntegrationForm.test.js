import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';
import { renderHook, act } from '@testing-library/react-hooks';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { FormScopes } from '@/utils/form';
import useAppsIntegrationForm from './useAppsIntegrationForm';

jest.mock('react-hook-form', () => ({
  useForm: jest.fn(),
}));

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
  useMutation: jest.fn(),
}));

describe('useAppsIntegrationForm', () => {
  beforeEach(() => {
    useQuery.mockImplementation(({ queryFn }) => {
      queryFn = jest.fn();
      return {
        data: {
          products: [],
        },
      };
    });

    useMutation.mockImplementation(({ onSuccess }) => ({
      mutate() {
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return the correct values', () => {
    const { result } = renderHook(() => useAppsIntegrationForm({ platform: { type: 'test' } }), {
      wrapper: global.wrapper,
    });

    expect(result.current.products).toEqual([]);
    expect(result.current.error).toEqual('');
  });

  it('should call form.setValue with correct arguments if the form scope will be equals to "create"', () => {
    const setValue = jest.fn();

    useForm.mockReturnValue({
      setValue,
    });

    const { result } = renderHook(
      () => useAppsIntegrationForm({ platform: { type: 'test' }, scope: FormScopes.CREATE }),
      {
        wrapper: global.wrapper,
      }
    );

    result.current.mutateGetMemeberKitData();

    expect(setValue).toHaveBeenCalledWith('fields.access.classroom_ids', []);
    expect(setValue).toHaveBeenCalledWith('fields.access.membership_level_id', null);
  });

  it('should call form.reset with the correct values if there is defaultValues', () => {
    const reset = jest.fn();

    useForm.mockReturnValue({
      reset,
    });

    const { result } = renderHook(
      () => useAppsIntegrationForm({ platform: { type: 'test' }, defaultValues: {} }),
      {
        wrapper: global.wrapper,
      }
    );

    result.current.mutateGetMemeberKitData();

    expect(reset).toHaveBeenCalledWith({});
  });

  it('should returns an error if the mutateGetMemeberKitData function throws  an error', () => {
    useMutation.mockImplementation(({ onError }) => ({
      mutate() {
        onError();
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(() => useAppsIntegrationForm({ platform: { type: 'test' } }), {
      wrapper: global.wrapper,
    });

    act(() => {
      result.current.mutateGetMemeberKitData();
    });

    expect(result.current.error).toEqual(
      'Ocorreu um erro. Favor verificar se sua chave de secreta é uma chave válida'
    );
  });
});
