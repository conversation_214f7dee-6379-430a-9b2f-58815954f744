import * as yup from 'yup';
import { PLATFORMS } from '@/consts/dashboardApps';

export const FieldsSchema = {
  [PLATFORMS.memberkit]: yup.object().shape({
    secret_key: yup
      .string()
      .required('Chave secreta do memberkit é obrigatório')
      .matches(
        /^[a-zA-Z0-9]{24}$/,
        'A chave secreta deve ter 24 caracteres alfanuméricos. Ex: A1b2C3d4E5f6G7h8I9j0K1L'
      ),
  }),
  [PLATFORMS.active_campaign]: yup.object().shape({
    token: yup
      .string()
      .required('API Key é obrigatório')
      .matches(
        /^[a-zA-Z0-9]{72}$/,
        'A chave secreta deve ter 72 caracteres alfanuméricos. Ex: e65b7cf3ccb18eb115335e128f454f853a666b152379872fe5ede206d5d9b5f71c07f015'
      ),
    customAction: yup
      .string()
      .required('Custom Action é obrigatório'),
  }),
  [PLATFORMS.spedy]: yup.object().shape({
    secret: yup.string().required('Chave secreta é obrigatório'),
  }),
  [PLATFORMS.cademi]: yup.object().shape({
    token: yup
      .string()
      .required('Token é obrigatório')
      .matches(/^[a-zA-Z0-9]{32}$/, {
        message:
          'O Token deve conter 32 caracteres alfanuméricos. Ex: 1234567890abcdef1234567890abcdef',
      }),
    api_key: yup
      .string()
      .required('API Key é obrigatório')
      .matches(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/, {
        message: 'A API Key deve estar no formato UUID. Ex: 12345678-1234-1234-1234-1234567890ab',
      }),
    domain: yup.string().required('Dominío Cademí é obrigatório'),
  }),
  [PLATFORMS.voxui]: yup.object().shape({
    token: yup
      .string()
      .required('Token API é obrigatório')
      .matches(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/, {
        message: 'Token API deve estar no formato UUID. Ex: 12345678-1234-1234-1234-1234567890ab',
      }),
    plan_id: yup
      .string()
      .required('Plan Id é obrigatório')
      .matches(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{6}$/, {
        message: 'A API Key deve estar no formato UUID. Ex: 6b516b66-41ee-4e27-aba6-e85678',
      }),
  }),
  [PLATFORMS.utmify]: yup.object().shape({
    token: yup
      .string()
      .required('Token API é obrigatório')
      .matches(/^[a-zA-Z0-9]{36}$/, {
        message:
          'Token API inválido, deve conter 36 caracteres (letras e números). Ex: 6hVxzQxRfD2lmRjb6HAYPaBUr35woQgEj9kp',
      }),
  }),
};

const UrlPattern = {
  [PLATFORMS.astron_members]: {
    regex: /^https:\/\/webhook\.astronmembers\.com\.br\/cakto-webhook\/[a-zA-Z0-9]{15}$/,
    example: 'https://webhook.astronmembers.com.br/cakto-webhook/1234567890abcde',
  },
  [PLATFORMS.spedy]: {
    regex:
      /^https:\/\/api\.spedy\.com\.br\/v1\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\/integrations\/cakto\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    example: 'https://api.spedy.com.br/v1/8649d81c-e94a-405c-a1b5-117043f74664/integrations/cakto/d0aef325-97bb-4280-9d1b-574362342c1e',
  },
  [PLATFORMS.webhooks]: {
    regex: /^(https?:\/\/)?([\w.-]+)(:\d+)?(\/[\w.%&=-]*)*(\?[&\w.%=-]*)?$/,
    example: "https://exemplo.com/webhook?token=abc%20123&param=valor",
  },
  [PLATFORMS.voxui]: {
    regex:
      /^https:\/\/sistema\.voxuy\.com\/api\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\/webhooks\/voxuy\/transaction$/,
    example:
      'https://sistema.voxuy.com/api/50a4fcf2-fafc-4401-a240-3e9546761673/webhooks/voxuy/transaction',
  },
  [PLATFORMS.smsfunnel]: {
    regex:
      /^https:\/\/v[12]\.smsfunnel\.com\.br\/integrations\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    example: 'https://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',
  },
};

export const BaseSchema = yup.object().shape({
  name: yup.string().required('Nome é obrigatório'),
  url: yup
    .string()
    .when('$platform.type', {
      is: (type) => !['utmify', 'cademi', 'memberkit'].includes(type),
      then: yup.string().required('URL é obrigatório'),
    })
    // eslint-disable-next-line func-names
    .test('url-pattern', 'URL inválida', function (value, { options }) {
      const { platform } = options.context;

      if (!(platform.type in UrlPattern)) {
        return true;
      }

      const { regex, example } = UrlPattern[platform.type];

      if (regex.test(value)) {
        return true;
      }

      return this.createError({
        path: 'url',
        message: `URL não corresponde ao padrão esperado. Ex: ${example}`,
      });
    }),
  products: yup
    .array()
    .of(yup.string())
    .required('Produtos são obrigatórios')
    .when('$platform.type', {
      is: (type) => ['astron_members'].includes(type),
      then: yup
        .array()
        .required('Produtos são obrigatórios')
        .min(1, 'Selecione ao menos um produto')
        .max(1, 'Selecione apenas um produto'),
      otherwise: yup
        .array()
        .required('Produtos são obrigatórios')
        .min(1, 'Selecione ao menos um produto'),
    }),
  events: yup
    .array()
    .of(yup.string())
    .when('$platform.type', {
      is: (type) => !['cademi', 'memberkit', 'astron_members', 'spedy'].includes(type),
      then: yup.array().required('Eventos são obrigatórios').min(1, 'Selecione ao menos um evento'),
    }),
});
