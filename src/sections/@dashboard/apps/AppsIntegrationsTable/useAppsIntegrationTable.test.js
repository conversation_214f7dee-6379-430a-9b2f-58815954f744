import { jest, describe, it } from '@jest/globals';
import { useQuery } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react-hooks';
import useAppsIntegrationTable from './useAppsIntegrationTable';

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

describe('useAppsIntegrationTable', () => {
  const platform = { type: 'someType', id: 'platformId' };
  const table = { page: 1, rowsPerPage: 10, search: '', filter: '' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return the correct values', () => {
    const mockProductsData = { products: [{ id: 1, name: 'Product 1' }] };
    const mockAppsData = { results: [{ id: 1, name: 'App 1' }], count: 1 };

    useQuery.mockImplementation(({ queryKey, queryFn }) => {
      queryFn();
      if (queryKey[0] === 'products-external') {
        return {
          data: mockProductsData,
          isLoading: false,
        };
      }

      if (queryKey[0] === 'apps') {
        return {
          data: mockAppsData,
          isFetching: false,
        };
      }

      return {};
    });

    const { result } = renderHook(() => useAppsIntegrationTable({ platform, table }));

    expect(result.current.products).toEqual(mockProductsData.products);
    expect(result.current.apps).toEqual(mockAppsData.results);
    expect(result.current.count).toBe(mockAppsData.count);
    expect(result.current.isLoadingProducts).toBe(false);
    expect(result.current.isFetching).toBe(false);
  });
});
