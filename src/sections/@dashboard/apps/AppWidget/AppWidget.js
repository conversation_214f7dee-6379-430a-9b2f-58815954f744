import PropTypes from 'prop-types';
// @mui
import { Card, styled } from '@mui/material';

// ----------------------------------------------------------------------

AppWidget.propTypes = {
  sx: PropTypes.object,
  image: PropTypes.string,
  onClick: PropTypes.func,
};

export default function AppWidget({ image, onClick, sx, ...other }) {
  return (
    <Card
      data-testid="app-widget-card"
      onClick={onClick}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        p: 3,
        ...sx,
      }}
      {...other}
    >
      <StyledRoot src={image} />
    </Card>
  );
}

const StyledRoot = styled('div')(({ theme, src }) => ({
  position: 'relative',
  backgroundSize: 'contain',
  backgroundPosition: 'center',
  backgroundImage: `url("${src}")`,
  backgroundRepeat: 'no-repeat',
  height: 80,
  width: 220,
}));
