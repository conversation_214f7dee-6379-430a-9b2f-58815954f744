import { describe, it } from '@jest/globals';
import { render, screen, fireEvent } from '@testing-library/react';
import AppWidget from './AppWidget';

describe('AppWidget', () => {
  describe('AppWidget', () => {
    const defaultProps = {
      image: 'test-image.jpg',
      onClick: jest.fn(),
      sx: { backgroundColor: 'red' },
    };

    it('should render the component with the correct image', () => {
      render(<AppWidget {...defaultProps} />);

      const card = screen.getByTestId('app-widget-card');
      expect(card).toHaveStyle('cursor: pointer');
      expect(card).toHaveStyle('background-color: red');

      const imageDiv = card.querySelector('div');
      expect(imageDiv).toHaveStyle('background-image: url("test-image.jpg")');
    });

    it('should call onClick when the card is clicked', () => {
      render(<AppWidget {...defaultProps} />);

      const card = screen.getByTestId('app-widget-card');
      fireEvent.click(card);

      expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
    });

    it('should apply additional styles via sx prop', () => {
      render(<AppWidget {...defaultProps} sx={{ margin: '10px' }} />);

      const card = screen.getByTestId('app-widget-card');
      expect(card).toHaveStyle('margin: 10px');
    });

    it('should spread additional props to the Card component', () => {
      render(<AppWidget {...defaultProps} data-testid="app-widget-card" />);

      const card = screen.getByTestId('app-widget-card');
      expect(card).toBeInTheDocument();
    });
  });
});
