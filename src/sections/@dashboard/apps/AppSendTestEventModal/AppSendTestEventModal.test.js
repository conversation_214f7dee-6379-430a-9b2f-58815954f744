import { render, screen, fireEvent } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { FormScopes } from '@/utils/form';
import * as useAppSendTestEventModal from './useAppSendTestEventModal';
import AppSendTestEventModal from './AppSendTestEventModal';

jest.mock('../../../../components/hook-form', () => ({
  RHFMultiSelect: jest.fn(({ options, ...props }) => (
    <select data-testid="RHFMultiSelect" {...props}>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )),
  FormProvider: jest.fn(({ children, methods, onSubmit }) => (
    <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
  )),
}));

describe('AppSendTestEventModal', () => {
  const setScopeMock = jest.fn();
  const platformMock = {
    events: [
      { custom_id: '1', name: 'Event 1' },
      { custom_id: '2', name: 'Event 2' },
    ],
  };
  const selectedAppMock = { id: 'appId' };

  let useAppSendTestEventModalSpy;

  const onSubmitMock = jest.fn();
  const formResetMock = jest.fn();

  const defaultUseWebhookSendTestSpyValues = {
    form: {
      handleSubmit: jest.fn((fn) => fn),
      reset: formResetMock,
    },
    onSubmit: onSubmitMock,
    isLoading: false,
  };

  beforeEach(() => {
    useAppSendTestEventModalSpy = jest.spyOn(useAppSendTestEventModal, 'default');
    useAppSendTestEventModalSpy.mockReturnValue(defaultUseWebhookSendTestSpyValues);

    useForm.mockReturnValue({
      handleSubmit: jest.fn((fn) => fn),
      reset: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the form correctly', () => {
    render(
      <AppSendTestEventModal
        scope={FormScopes.SEND_TEST_EVENT}
        setScope={setScopeMock}
        platform={{ name: 'Platform Test' }}
        selectedApp={selectedAppMock}
      />
    );

    expect(screen.getByText('Enviar evento de teste de Platform Test')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Selecione os eventos')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /enviar/i })).toBeInTheDocument();
  });

  it('should call onSubmit when the form is submitted', () => {
    render(
      <AppSendTestEventModal
        scope={FormScopes.SEND_TEST_EVENT}
        setScope={setScopeMock}
        platform={platformMock}
        selectedApp={selectedAppMock}
      />
    );

    fireEvent.submit(screen.getByRole('button', { name: /enviar/i }));

    expect(onSubmitMock).toHaveBeenCalledTimes(1);
  });

  it('should call setScope and reset the form when the dialog is closed', () => {
    render(
      <AppSendTestEventModal
        scope={FormScopes.SEND_TEST_EVENT}
        setScope={setScopeMock}
        platform={platformMock}
        selectedApp={selectedAppMock}
      />
    );

    fireEvent.keyDown(screen.getByRole('dialog'), { key: 'Escape', code: 'Escape' });

    expect(setScopeMock).toHaveBeenCalledWith(FormScopes.INDEX);
    expect(formResetMock).toHaveBeenCalledTimes(1);
  });

  it('should render the events correctly in the selector', () => {
    render(
      <AppSendTestEventModal
        scope={FormScopes.SEND_TEST_EVENT}
        setScope={setScopeMock}
        platform={platformMock}
        selectedApp={selectedAppMock}
      />
    );

    const select = screen.getByTestId('RHFMultiSelect');
    expect(select.children.length).toBe(2);
    expect(select.children[0].textContent).toBe('Event 1');
    expect(select.children[1].textContent).toBe('Event 2');
  });
});
