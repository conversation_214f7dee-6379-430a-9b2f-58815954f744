import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';
import { renderHook } from '@testing-library/react-hooks';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import * as notistack from 'notistack';
import useAppSendTestEventModal from './useAppSendTestEventModal';
import { waitFor } from '@testing-library/dom';

const formResetMock = jest.fn();

const enqueueSnackbarMock = jest.fn();

const mockMethods = {
  handleSubmit: jest.fn((onSubmit) => onSubmit),
  reset: formResetMock,
};

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
  useMutation: jest.fn(),
}));

describe('useAppSendTestEventModal', () => {
  const platform = {
    name: 'Test',
  };

  beforeEach(() => {
    jest.spyOn(notistack, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: enqueueSnackbarMock,
    });

    useMutation.mockImplementation(({ onSuccess }) => ({
      mutate() {
        onSuccess();
        return { data: {} };
      },
      isLoading: false,
    }));

    useForm.mockReturnValue(mockMethods);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return the correct values', () => {
    const { result } = renderHook(
      () => useAppSendTestEventModal({ appId: 1, setScope: () => {}, platform }),
      {
        wrapper: global.wrapper,
      }
    );

    expect(result.current.isLoading).toEqual(false);
  });

  it('should call a success message in case of submit success', async () => {
    const { result } = renderHook(
      () => useAppSendTestEventModal({ appId: 1, setScope: () => {}, platform }),
      {
        wrapper: global.wrapper,
      }
    );

    waitFor(() => {
      result.current.onSubmit();
    });

    expect(enqueueSnackbarMock).toHaveBeenCalledWith(
      'Evento de teste de Test enviado com sucesso!',
      {
        variant: 'success',
      }
    );
    expect(formResetMock).toHaveBeenCalled();
  });

  it('should call an error message in case of submit error', async () => {
    useMutation.mockImplementation(({ onError }) => ({
      mutate() {
        onError();
        return { data: {} };
      },
      isLoading: false,
    }));

    const { result } = renderHook(
      () => useAppSendTestEventModal({ appId: 1, setScope: () => {}, platform }),
      {
        wrapper: global.wrapper,
      }
    );

    waitFor(() => {
      result.current.onSubmit();
    });

    expect(enqueueSnackbarMock).toHaveBeenCalledWith(
      'Ocorreu um erro ao tentar enviar o evento de teste de Test',
      { variant: 'error' }
    );
  });
});
