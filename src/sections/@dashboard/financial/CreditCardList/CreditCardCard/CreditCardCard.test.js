import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CreditCardCard from './CreditCardCard';
import useCreditCardCard from './useCreditCardCard/useCreditCardCard';

jest.mock('react-router', () => ({
  __esModule: true,
  useNavigate: jest.fn(),
}));

jest.mock('./useCreditCardCard/useCreditCardCard', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// jest.mock('@/components/confirm-dialog', () => ({
//   __esModule: true,
//   default: ({ open, onClose, onConfirm }) =>
//     open ? (
//       <div>
//         <p>Are you sure you want to delete this card?</p>
//         <button aria-label="close" onClick={onClose}>
//           Close
//         </button>
//         <button onClick={onConfirm}>Confirm</button>
//       </div>
//     ) : null,
// }));

jest.mock('../CreditCardModalForm', () => ({
  __esModule: true,
  default: ({ open, onClose }) =>
    open ? (
      <div>
        <p>Edit Card</p>
        <button aria-label="close" onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
}));

describe('CreditCardCard Component', () => {
  const mockDeleteCreditCard = jest.fn();
  const mockUseCreditCardCard = {
    deleteCreditCard: mockDeleteCreditCard,
    isLoading: false,
  };

  beforeEach(() => {
    useCreditCardCard.mockReturnValue(mockUseCreditCardCard);
  });

  const props = {
    id: '1',
    holder: 'John Doe',
    number: '****************',
    cvv: '123',
    expirationDate: '12/24',
    limit: 5000,
    type: 'Visa',
    name: 'Personal Card',
  };

  it('should render the CreditCardCard component', () => {
    render(<CreditCardCard {...props} />);
    expect(screen.getByText(props.holder)).toBeInTheDocument();
    expect(screen.getByText(/Personal Card/)).toBeInTheDocument();
  });

  it('should open the remove modal when the remove button is clicked', () => {
    render(<CreditCardCard {...props} />);
    fireEvent.click(screen.getByTestId('delete'));
    expect(
      screen.getByText('Tem certeza que deseja remover o cartão Personal Card?')
    ).toBeInTheDocument();
  });

  it('should open the edit modal when the edit button is clicked', () => {
    render(<CreditCardCard {...props} />);
    fireEvent.click(screen.getByTestId('edit'));
    expect(screen.getByText(/Edit Card/)).toBeInTheDocument();
  });

  it('should close the edit modal when the close button is clicked', async () => {
    render(<CreditCardCard {...props} />);
    fireEvent.click(screen.getByTestId('edit'));
    fireEvent.click(screen.getByLabelText('close'));
    await waitFor(() => {
      expect(screen.queryByText(/Edit Card/)).not.toBeInTheDocument();
    });
  });

  it('should close the remove modal when the close button is clicked', async () => {
    render(<CreditCardCard {...props} />);
    fireEvent.click(screen.getByTestId('delete'));
    fireEvent.click(screen.getByTestId('confirm-dialog-close-button'));
    await waitFor(() => {
      expect(
        screen.queryByText(/Are you sure you want to delete this card?/)
      ).not.toBeInTheDocument();
    });
  });

  it('should call deleteCreditCard when the confirm remove button is clicked', async () => {
    render(<CreditCardCard {...props} />);
    fireEvent.click(screen.getByTestId('delete'));
    fireEvent.click(screen.getByTestId('remove-button'));
    await waitFor(() => {
      expect(mockDeleteCreditCard).toHaveBeenCalledWith(props.id);
    });
  });
});
