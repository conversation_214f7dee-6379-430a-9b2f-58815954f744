import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { LoadingButton } from '@mui/lab';
import { Drawer, Stack, IconButton, Typography, Button, InputAdornment } from '@mui/material';
import FormProvider from '../../../../components/hook-form/FormProvider';
import { RHFTextField, RHFMultiSelect, RHFSwitch } from '../../../../components/hook-form';
import RHFDateTimePicker from '../../../../components/hook-form/RHFDateTimePicker';
import Iconify from '../../../../components/iconify/Iconify';
import { FormScopes } from '../../../../utils/form';
import useCouponsForm from './useCouponsForm';

CouponsForm.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  scope: PropTypes.string,
  coupon: PropTypes.object,
};

export function CouponsForm({ open, onClose, scope, coupon }) {
  const { form, onSubmit, isLoading, products, isFetchingProducts } = useCouponsForm({ onClose });

  const onCloseDrawer = () => {
    onClose();
  };

  useEffect(() => {
    form.reset(coupon);

    form.setValue(
      'products',
      coupon?.products.map(({ id }) => id)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onCloseDrawer}
      PaperProps={{
        sx: {
          p: 2,
          width: {
            xs: '100%',
            sm: 400,
          },
        },
      }}
    >
      <Stack direction="row" alignItems="center" gap={1}>
        <IconButton onClick={onClose}>
          <Iconify icon="eva:chevron-right-fill" />
        </IconButton>
        <Typography variant="subtitle1">
          {scope === FormScopes.CREATE ? 'Adicionar' : 'Editar'} Cupom
        </Typography>
      </Stack>

      <Stack my={1}>
        <Typography color="text.secondary" variant="body2">
          {scope === FormScopes.CREATE
            ? 'Adicione aqui os cupons para os seus produtos.'
            : 'Edite aqui os dados do seu cupom.'}
        </Typography>
      </Stack>

      <FormProvider methods={form} onSubmit={onSubmit}>
        <Stack direction="column" spacing={2}>
          <RHFTextField name="code" label="Código" placeholder="Digite o código do seu Cupom" />
          <RHFMultiSelect
            checkbox
            chip
            fullWidth
            name="products"
            label="Produtos"
            options={products}
            disabled={isFetchingProducts}
            sx={{ width: '100%' }}
          />

          <RHFTextField
            name="discount"
            label="Desconto"
            type="number"
            placeholder="Digite o valor do disconto do cupom"
            InputProps={{
              endAdornment: <InputAdornment position="end">%</InputAdornment>,
            }}
          />

          <RHFDateTimePicker name="startTime" defaultValue={null} label="Data de início do cupom" />

          <RHFDateTimePicker
            name="endTime"
            defaultValue={null}
            label="Data de expiração do cupom"
            helperText="Deixe sem  valor para a validade ser eterna"
          />

          <RHFSwitch name="applyOnBumps" label="Aplicar desconto aos Order Bumps" />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" alignItems="center" gap={1} mt={3}>
          <Button variant="outlined" color="inherit" onClick={onCloseDrawer}>
            Cancelar
          </Button>
          <LoadingButton variant="contained" color="primary" type="submit" loading={isLoading}>
            {scope === FormScopes.CREATE ? 'Adicionar' : 'Editar'}
          </LoadingButton>
        </Stack>
      </FormProvider>
    </Drawer>
  );
}
