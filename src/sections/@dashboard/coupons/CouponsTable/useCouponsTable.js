import { useContext } from 'react';

import { useQuery } from '@tanstack/react-query';

import { CouponsContext } from '@/contexts/CouponsContext';
import { getCuponsService } from '@/services/coupons';

export default function useCouponsTable() {
  const { table } = useContext(CouponsContext);

  const labels = [
    {
      id: 'code',
      label: 'Código',
    },
    {
      id: 'products',
      label: 'produtos',
    },
    {
      id: 'discount',
      label: 'Desconto',
    },
    {
      id: 'startTime',
      label: 'Início',
    },
    {
      id: 'endTime',
      label: 'Fim',
    },
    {
      id: 'usage_quantity',
      label: '# Usos',
    },
    {
      id: 'actions',
      label: '',
    },
  ];

  const { data, isLoading } = useQuery({
    queryKey: ['coupons', { search: table.search, page: table.page }],
    queryFn: () => getCuponsService({ page: table.page, search: table.search }),
  });

  return {
    labels,
    table,
    count: data?.count || 0,
    fetching: isLoading,
    rows: data?.results || [],
  };
}
