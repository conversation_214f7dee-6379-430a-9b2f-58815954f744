import PropTypes from 'prop-types';
// @mui
import { LoadingButton } from '@mui/lab';
import { Button, Dialog, Divider, InputAdornment, Paper, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { useContext } from 'react';
import { RHFTextField } from '../../../components/hook-form';
import FormProvider from '../../../components/hook-form/FormProvider';
import { RHFCheckboxTree } from '../../../components/hook-form/RHFCheckboxTree';
import Iconify from '../../../components/iconify';
import Scrollbar from '../../../components/scrollbar/Scrollbar';
import { CollaboratorsContext } from '../../../contexts/CollaboratorsContext';
import { FormScopes } from '../../../utils/form';

CollaboratorsModal.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

const crud = (value) => [
  {
    value: `${value}_create`,
    label: 'Criar',
    icon: 'eva:plus-outline',
  },
  {
    value: `${value}_view`,
    label: 'Ler',
    icon: 'eva:eye-outline',
  },
  {
    value: `${value}_edit`,
    label: 'Editar',
    icon: 'eva:edit-outline',
  },
  {
    value: `${value}_delete`,
    label: 'Deletar',
    icon: 'eva:trash-2-outline',
  },
];

const permissions = [
  {
    value: 'full',
    label: 'Acesso total',
    children: [
      {
        value: 'dashboard',
        label: 'Dashboard',
        children: crud('dashboard'),
      },
      {
        value: 'products',
        label: 'Produtos',
        children: crud('products'),
      },
      {
        value: 'members',
        label: 'Área de membros',
        children: crud('members'),
      },
      {
        value: 'affiliates',
        label: 'Meus Afiliados',
        children: crud('affiliates'),
      },
      {
        value: 'orders',
        label: 'Vendas',
        children: crud('orders'),
      },
      {
        value: 'financial',
        label: 'Financeiro',
        children: crud('financial'),
      },
      {
        value: 'reports',
        label: 'Relatórios',
        children: crud('reports'),
      },
      {
        value: 'collaborators',
        label: 'Colaboradores',
        children: crud('collaborators'),
      },
      {
        value: 'apps',
        label: 'Apps',
        children: crud('apps'),
      },
      {
        value: 'referrals',
        label: 'Programa de indicações',
        children: crud('referrals'),
      },
    ],
  },
];

export default function CollaboratorsModal({ open, onClose }) {
  const { form, scope, invite, inviting, update, updating } = useContext(CollaboratorsContext);
  const isEdition = scope === FormScopes.EDIT;
  const title = isEdition ? "Editar" : "Adicionar";

  const onSubmit = {
    [FormScopes.EDIT]: update,
    [FormScopes.CREATE]: invite,
  }[scope];

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <FormProvider methods={form} onSubmit={form.handleSubmit(onSubmit)}>
        <Stack p={2} spacing={2}>
          <Typography variant="h6">{title} Colaborador</Typography>
          <Divider sx={{ my: 2 }} />
          <Stack spacing={2}>
            <RHFTextField
              name="email"
              label="Email"
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:email-fill" />
                  </InputAdornment>
                ),
                disabled: isEdition,
              }}
              placeholder="Digite o email do colaborador"
            />
            <Stack
              component={Paper}
              spacing={2}
              variant="outlined"
              sx={{ width: 1, bgcolor: 'transparent', p: 2 }}
            >
              <Typography variant="caption" color="text.secondary">
                Permissões
              </Typography>
              <Scrollbar
                sx={{
                  height: 400,
                  px: 2,
                }}
              >
                <RHFCheckboxTree
                  options={permissions}
                  name="permissions"
                  defaultExpanded={['full']}
                />
              </Scrollbar>
            </Stack>
          </Stack>
          <Stack direction="row" justifyContent="flex-end" spacing={2}>
            <Button variant="outlined" onClick={onClose} color="inherit">
              Cancelar
            </Button>
            <LoadingButton variant="contained" type="submit" color="primary" loading={isEdition ? updating : inviting}>
              {isEdition ? "Salvar Alterações" : "Adicionar Colaborador"}
            </LoadingButton>
          </Stack>
        </Stack>
      </FormProvider>
    </Dialog>
  );
}
