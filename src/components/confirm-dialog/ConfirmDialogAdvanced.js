import PropTypes from 'prop-types';
// @mui
import { LoadingButton } from '@mui/lab';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
} from '@mui/material';
import { Box } from '@mui/system';
import { useState } from 'react';

// ----------------------------------------------------------------------

ConfirmDialogAdvanced.propTypes = {
  word: PropTypes.string,
  open: PropTypes.bool,
  title: PropTypes.node,
  action: PropTypes.node,
  content: PropTypes.node,
  onClose: PropTypes.func,
  onAction: PropTypes.func,
  loading: PropTypes.bool,
};

export default function ConfirmDialogAdvanced({
  word,
  title,
  content,
  action,
  open,
  onClose,
  onAction,
  loading,
  ...other
}) {
  const [value, setValue] = useState('');
  return (
    <Dialog fullWidth maxWidth="xs" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={{ pb: 2 }}>{title}</DialogTitle>

      <DialogContent>
        <Box sx={{ pb: 2 }}>{content}</Box>
        <Typography variant="body2" sx={{ pb: 1 }}>
          Para confirmar, digite <strong>{word}</strong> no campo abaixo:
        </Typography>
        <TextField
          autoFocus
          fullWidth
          value={value}
          onChange={(e) => setValue(e.target.value)}
          variant="outlined"
          placeholder={word}
          size="small"
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          Cancelar
        </Button>
        <LoadingButton
          loading={loading}
          variant="contained"
          color="error"
          onClick={onAction}
          disabled={value !== word}
        >
          {action}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
