import PropTypes from 'prop-types';
import { useState, useMemo, useContext } from 'react';
import { Typography } from '@mui/material';
import { useFormContext, useWatch } from 'react-hook-form';
import { RHFTextField } from '@/components/hook-form';
import { Divider } from '@/components/checkout';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { fCurrency } from '@/utils/formatNumber';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';
import OrderItemResume from './OderItemResume';

export function OrderSummary({ lastDesktopRows }) {
  const [coupon, setCoupon] = useState('');

  const { product, validateCoupon, couponData, setCouponData, selectedBumps, paymentMethod } =
    useContext(CheckoutBuilderContext);

  const form = useFormContext();

  const discount = useMemo(() => {
    const productPrice = +(product?.price || 0);

    const couponDiscount = ((couponData?.discount || 0) / 100) * productPrice;

    const bumpsDiscount = selectedBumps.reduce((acc, bump) => {
      const { price } = bump.offer;
      const bumpDiscount = ((couponData?.discount || 0) / 100) * price;
      return acc + bumpDiscount;
    }, 0);

    return couponDiscount + bumpsDiscount;
  }, [couponData, product, selectedBumps]);

  const finalPrice = useMemo(() => {
    const productPrice = +(product?.price || 0);

    const bumpsPrice = selectedBumps.reduce((acc, bump) => {
      const { price } = bump.offer;
      return acc + +price;
    }, 0);

    return productPrice + bumpsPrice;
  }, [product, selectedBumps]);

  const handleChange = (e) => {
    form.clearErrors('coupon');
    setCoupon(e.target.value);
  };

  const handleRemoveCoupon = () => {
    setCouponData(undefined);
    setCoupon('');
  };

  const handleApplyCoupon = async () => {
    try {
      await validateCoupon(coupon);
    } catch (error) {
      setCoupon('');
      form.setError('coupon', { message: 'Cupon inválido' });
    }
  };

  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const backgroundColor = useWatch({
    name: getAttributeName('form.background.color'),
  });

  const boxBackgroundColor = useWatch({
    name: getAttributeName('box.default.background.color'),
  });

  const textColorActive = useWatch({
    name: getAttributeName('text.color.active'),
  });

  const recurrencePeriod = useMemo(
    () => product?.offers?.find((offer) => offer.default)?.recurrence_period,
    [product]
  );

  return (
    <>
      <Typography
        variant="h6"
        color={primaryColor}
        className="font-bold text-lg"
        sx={{ marginBottom: '10px' }}
      >
        Resumo do pedido
      </Typography>

      <div
        className="p-6 rounded-t-lg  w-full  mx-auto relative"
        style={{ backgroundColor: boxBackgroundColor }}
      >
        {product?.showCouponField && (
          <>
            <div className="flex w-full flex-col items-start gap-3">
              <RHFTextField
                name="coupon"
                size="small"
                label=""
                placeholder="Código de desconto"
                InputProps={{
                  style: {
                    color: primaryColor,
                  },
                  startAdornment: (
                    <div className=" inset-y-0 pl-3 flex items-center pointer-events-none">
                      <img src="/assets/icons/checkout/coupon.svg" alt="Coupon Icon" />
                    </div>
                  ),
                  endAdornment: (
                    <>
                      {couponData ? (
                        <CouponButton
                          text="Remover Cupom"
                          onClick={handleRemoveCoupon}
                          className="text-red-500 hover:text-red-600"
                        />
                      ) : (
                        <CouponButton
                          text="Aplicar Cupom"
                          onClick={handleApplyCoupon}
                          className="text-green-500 hover:text-green-600"
                        />
                      )}
                    </>
                  ),
                }}
                value={coupon}
                onChange={handleChange}
              />
            </div>

            <Divider />
          </>
        )}

        <OrderItemResume
          image={product?.image}
          title={product?.name}
          offerPrice={+(product?.price || 0)}
          type={product?.type}
          recurrencePeriod={recurrencePeriod}
          lastDesktopRows={lastDesktopRows}
          paymentMethod={paymentMethod}
        />

        {selectedBumps.map((bump) => (
          <div key={bump.id} className="my-1">
            <OrderItemResume
              image={bump?.image}
              title={bump?.title}
              offerPrice={bump?.offer?.price}
              type={bump?.offer?.type}
              recurrencePeriod={bump?.offer?.recurrence_period}
              lastDesktopRows={lastDesktopRows}
              paymentMethod={paymentMethod}
            />
          </div>
        ))}

        {!!couponData && (
          <div className="flex justify-between">
            <Typography
              variant="body2"
              style={{ color: textColorActive }}
              className="text-[#38CA4F]"
            >
              Cupom aplicado ({couponData?.discount}%)
            </Typography>
            <Typography
              variant="body2"
              style={{ color: textColorActive }}
              className="text-[#38CA4F]"
            >
              -{fCurrency(discount)}
            </Typography>
          </div>
        )}

        <Divider />

        <div className="flex justify-between mt-4">
          <Typography color={primaryColor} className="text-white">
            Total
          </Typography>
          <Typography color={primaryColor} className="text-white">
            {fCurrency(finalPrice - discount)}
          </Typography>
        </div>

        <div className="absolute left-0 right-0 bottom-0 h-4 overflow-hidden">
          <div
            className="absolute left-0 right-0 h-8"
            style={{
              backgroundImage: `linear-gradient(135deg, transparent 50%, ${backgroundColor} 50%), linear-gradient(45deg, ${backgroundColor} 50%, transparent 50%)`,
              backgroundSize: '16px 16px',
              backgroundPosition: 'bottom',
              transform: 'translateY(4px)',
            }}
          />
        </div>
      </div>
    </>
  );
}

OrderSummary.propTypes = {
  lastDesktopRows: PropTypes.array,
};

function CouponButton({ text, onClick, className }) {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`absolute top-[0.5rem] right-2 font-semibold transition-colors text-[14px] ${className}`}
    >
      {text}
    </button>
  );
}

CouponButton.propTypes = {
  text: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  className: PropTypes.string,
};
