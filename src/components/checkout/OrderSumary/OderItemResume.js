import PropTypes from 'prop-types';
import { useMemo } from 'react';
import { Typography } from '@mui/material';
import { fCurrency } from '@/utils/formatNumber';
import { useWatch } from 'react-hook-form';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';

const OrderItemResume = ({
  image,
  offerPrice,
  title,
  type,
  recurrencePeriod,
  paymentMethod,
  lastDesktopRows,
}) => {
  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const device = useWatch({
    name: 'device',
  });

  const backgroundColor = useWatch({
    name: getAttributeName('box.selected.background.color'),
  });

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const getPrice = () => {
    if (type === 'subscription') {
      if (recurrencePeriod === 7) return `${fCurrency(Number(offerPrice))} / semana`;

      if (recurrencePeriod === 30) return `${fCurrency(Number(offerPrice))} / mês`;

      if (recurrencePeriod === 60) return `${fCurrency(Number(offerPrice))} / bimestre`;

      if (recurrencePeriod === 90) return `${fCurrency(Number(offerPrice))} / trimestre`;

      if (recurrencePeriod === 180) return `${fCurrency(Number(offerPrice))} / semestre`;

      if (recurrencePeriod === 365) return `${fCurrency(Number(offerPrice))}/ semestre`;

      return `${fCurrency(Number(offerPrice))} / mês`;
    }

    const price =
      paymentMethod === 'Cartão de Crédito'
        ? `1 x de ${fCurrency(offerPrice)}`
        : fCurrency(offerPrice);

    return price;
  };

  const fontSize = useMemo(() => {
    if (device === 'desktop') {
      if (['[4,8]', '[4,4,4]'].includes(JSON.stringify(lastDesktopRows))) {
        return 12;
      }

      return 20;
    }

    return 16;
  }, [device, lastDesktopRows]);

  return (
    <div
      style={{ backgroundColor }}
      className={`
          cursor-pointer
          ease-in-out
          duration-150
          rounded-md
          overflow-hidden
          shadow-md
          transition
          w-full
      `}
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          {image && (
            <img
              src={image}
              alt="Imagem do produto"
              className="h-24 w-24 object-cover rounded-sm"
            />
          )}
        </div>
        <div className="flex justify-between items-center p-3 w-full">
          <Typography
            fontSize={fontSize}
            fontWeight="bold"
            color={primaryColor}
            variant="caption"
            className="font-bold text-lg text-white"
          >
            {title?.trim() || 'Nome do Produto'}
          </Typography>
          <Typography
            fontSize={fontSize}
            fontWeight="bold"
            color={primaryColor}
            variant="caption"
            className="font-bold text-lg text-white"
          >
            {getPrice()}
          </Typography>
        </div>
      </div>
    </div>
  );
};

OrderItemResume.propTypes = {
  image: PropTypes.string.isRequired,
  offerPrice: PropTypes.number.isRequired,
  title: PropTypes.string.isRequired,
  type: PropTypes.string,
  recurrencePeriod: PropTypes.number,
  lastDesktopRows: PropTypes.array,
  paymentMethod: PropTypes.string,
};

export default OrderItemResume;
