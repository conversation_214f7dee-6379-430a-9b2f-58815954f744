import PropTypes from 'prop-types';
import { Typography, Link } from '@mui/material';
import { applyTransparency, hexToFilter } from '@/utils/colors';
import { fCurrency } from '@/utils/formatNumber';

export const ProductPurchase = ({
  product,
  backgroundColor,
  payButtonColor,
  payButtonTextColor,
  primaryColor,
  secondaryColor,
  activeColor,
}) => {
  const offer = product?.offers?.find((item) => item.default);

  const paymentMethods = product?.paymentMethods;

  const type = offer?.type;
  const recurrencePeriod = offer?.recurrencePeriod;

  const getPrice = () => {
    const maxInstallment = 1;

    const value = +(product?.price || 0);

    if (type === 'subscription') {
      if (recurrencePeriod === 7) return `${fCurrency(value)} / semana`;

      if (recurrencePeriod === 30) return `${fCurrency(value)} / mês`;

      if (recurrencePeriod === 60) return `${fCurrency(value)} / bimestre`;

      if (recurrencePeriod === 90) return `${fCurrency(value)} / trimestre`;

      if (recurrencePeriod === 180) return `${fCurrency(value)} / semestre`;

      if (recurrencePeriod === 365) return `${fCurrency(value)}/ semestre`;

      return `${fCurrency(value)} / mês`;
    }

    if (!paymentMethods?.includes('credit_card')) return '';

    return `${maxInstallment} X de ${fCurrency(value)}`;
  };

  const getRenovation = () => {
    if (type === 'subscription') {
      if (recurrencePeriod === 7) return `Renovação semanal`;

      if (recurrencePeriod === 30) return `Renovação mensal`;

      if (recurrencePeriod === 60) return `Renovação bimestral`;

      if (recurrencePeriod === 90) return `Renovação trimestral`;

      if (recurrencePeriod === 180) return `Renovação semestral`;

      if (recurrencePeriod === 365) return `Renovação anual`;

      return `Renovação mensal`;
    }

    return `Renovação atual`;
  };

  return (
    <div
      style={{
        backgroundColor,
      }}
      className="max-w-md mx-auto bg-gray-800 text-white rounded-md overflow-hidden shadow-md"
    >
      <div
        style={{
          backgroundColor: payButtonColor,
        }}
        className="bg-[#0F7864] p-4 text-center"
      >
        <Typography
          variant="h5"
          style={{
            color: payButtonTextColor,
          }}
          className="text-xl font-semibold"
        >
          Compra segura
        </Typography>
      </div>

      <div className="p-6 flex">
        {!!product?.image && (
          <img
            src={product?.image}
            alt="Imagem do produto"
            className="w-24 h-24 object-cover rounded-md mr-4"
          />
        )}
        <div>
          <Typography variant="h5" style={{ color: primaryColor }} className="text-xl font-bold">
            {product?.name}
          </Typography>
          <Typography
            variant="body2"
            style={{ color: applyTransparency(secondaryColor || '', 0.5) }}
            className="text-sm text-gray-400 mt-2"
          >
            Precisa de ajuda?
          </Typography>
          <Link
            variant="body2"
            href={`mailto:${product?.supportEmail}`}
            target="_blank"
            className="text-blue-400 underline text-sm cursor-pointer"
            color="#60A5FA"
            style={{
              textDecoration: 'underline',
            }}
            rel="noreferrer"
          >
            Veja o contato do vendedor
          </Link>
        </div>
      </div>

      <div className="border-t-2 border-dashed border-[#919EAB33]">
        <div className="p-6">
          <Typography
            variant="h5"
            style={{ color: primaryColor }}
            className="text-xl font-semibold"
          >
            Total
          </Typography>
          <Typography
            variant="h4"
            style={{ color: activeColor }}
            className="text-2xl font-bold text-green-400"
          >
            {getPrice()}
          </Typography>
          {offer?.type !== 'subscription' && (
            <Typography
              variant="subtitle1"
              style={{
                marginTop: 12,
                color: paymentMethods?.includes('credit_card')
                  ? applyTransparency(secondaryColor || '', 0.7)
                  : activeColor,
              }}
              className="text-gray-300 mt-2"
            >{`${paymentMethods?.includes('credit_card') ? 'ou' : ''} ${fCurrency(
              +(product?.price || 0)
            )} à vista`}</Typography>
          )}
          <Typography
            variant="subtitle2"
            style={{ marginTop: 6, color: applyTransparency(secondaryColor || '', 0.5) }}
            className="text-gray-500 text-sm mt-2"
          >
            {getRenovation()}
          </Typography>
        </div>
      </div>

      <div
        style={{ color: applyTransparency(secondaryColor || '', 0.5) }}
        className="border-t-2 border-dashed border-[#919EAB33] p-4 text-center text-xs text-gray-400"
      >
        <img
          src="/assets/cakto-logo-white.png"
          alt="cakto"
          className="mx-auto w-20 grayscale"
          style={{ filter: hexToFilter(primaryColor) }}
        />
        <Typography variant="caption">
          Cakto está processando este pagamento para o vendedor {product?.producerName}
        </Typography>
        <p>Este site é protegido pelo reCAPTCHA do Google</p>
        <Link
          variant="caption"
          style={{
            color: applyTransparency(secondaryColor || '', 0.5),
            textDecoration: 'underline',
          }}
          href="https://cakto.com.br/politica-de-privacidade"
          target="_blank"
          className="underline"
          rel="noreferrer"
        >
          Política de privacidade
        </Link>{' '}
        e{' '}
        <Link
          variant="caption"
          style={{
            color: applyTransparency(secondaryColor || '', 0.5),
            textDecoration: 'underline',
          }}
          href="https://cakto.com.br/termos-e-condicoes/"
          target="_blank"
          className="underline"
          rel="noreferrer"
        >
          Termos de serviço
        </Link>
        <Typography
          variant="caption"
          style={{ color: applyTransparency(secondaryColor || '', 0.4) }}
          className="mt-2 text-gray-500"
        >
          * Parcelamento com acréscimo
        </Typography>
        <Typography
          variant="caption"
          style={{ color: applyTransparency(secondaryColor || '', 0.4) }}
          className="text-gray-500"
        >
          Ao continuar, você concorda com os{' '}
          <Link
            variant="caption"
            style={{
              color: applyTransparency(secondaryColor || '', 0.4),
              textDecoration: 'underline',
            }}
            href="#"
            className="underline"
          >
            Termos de Compra
          </Link>
        </Typography>
      </div>
    </div>
  );
};

ProductPurchase.propTypes = {
  product: PropTypes.object,
  backgroundColor: PropTypes.string,
  payButtonColor: PropTypes.string,
  payButtonTextColor: PropTypes.string,
  primaryColor: PropTypes.string,
  secondaryColor: PropTypes.string,
  activeColor: PropTypes.string,
};
