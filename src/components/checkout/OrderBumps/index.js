import { useState, useContext } from 'react';
import { Input } from '@/components/checkout';
import { Typography } from '@mui/material';
import Iconify from '@/components/iconify';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import CircleCheckIcon from '@/assets/icons/CircleCheckIcon';
import { fCurrency } from '@/utils/formatNumber';
import { useWatch } from 'react-hook-form';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';

export function OrderBumps() {
  const { product, paymentMethod, setSelectedBumps, selectedBumps, couponData } =
    useContext(CheckoutBuilderContext);
  const [selectedBumpsIds, setSelectedBumpsIds] = useState([]);

  const handleCheck = (data) => {
    const { id } = data;
    if (selectedBumpsIds.includes(id)) {
      const values = selectedBumps.filter((checked) => checked.id !== id);
      const ids = selectedBumpsIds.filter((checked) => checked !== id);
      setSelectedBumps(values);
      setSelectedBumpsIds(ids);
    } else {
      const ids = [...selectedBumpsIds, id];
      const values = [...selectedBumps, data];
      setSelectedBumps(values);
      setSelectedBumpsIds(ids);
    }
  };

  const getFinalPrice = (type, productPrice, recurrencePeriod) => {
    const applyCouponValue = couponData?.applyOnBumps ? 1 : 0;

    const originalPrice = productPrice || 0;
    const discount = (applyCouponValue * (originalPrice * (couponData?.discount || 0))) / 100;

    const price = +(productPrice || 0) - discount;
    const minPrice = Math.max(0, price);

    if (type === 'subscription') {
      if (recurrencePeriod === 7) return `${fCurrency(Number(minPrice))} / semana`;

      if (recurrencePeriod === 30) return `${fCurrency(Number(minPrice))} / mês`;

      if (recurrencePeriod === 60) return `${fCurrency(Number(minPrice))} / bimestre`;

      if (recurrencePeriod === 90) return `${fCurrency(Number(minPrice))} / trimestre`;

      if (recurrencePeriod === 180) return `${fCurrency(Number(minPrice))} / semestre`;

      if (recurrencePeriod === 365) return `${fCurrency(Number(minPrice))}/ semestre`;

      return `${fCurrency(Number(minPrice))} / mês`;
    }

    if (paymentMethod === 'Cartão de Crédito') {
      return `1 x de ${fCurrency(Number(minPrice))}`;
    }

    return `${fCurrency(Number(minPrice))}`;
  };

  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const device = useWatch({
    name: 'device',
  });

  const iconColor = useWatch({
    name: getAttributeName('icon.color'),
  });

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const selectedBackgroundColor = useWatch({
    name: getAttributeName('box.selected.background.color'),
  });

  const unselectedBackgroundColor = useWatch({
    name: getAttributeName('box.unselected.background.color'),
  });

  const selectedHeaderBackgroundColor = useWatch({
    name: getAttributeName('box.selected.header.background.color'),
  });

  const unselectedHeaderBackgroundColor = useWatch({
    name: getAttributeName('box.unselected.header.background.color'),
  });

  const selectedHeaderTextColorPrimary = useWatch({
    name: getAttributeName('box.selected.header.text.color.primary'),
  });

  const unselectedHeaderTextColorPrimary = useWatch({
    name: getAttributeName('box.unselected.header.text.color.primary'),
  });

  const selectedTextColorPrimary = useWatch({
    name: getAttributeName('box.selected.text.color.primary'),
  });

  const unselectedTextColorPrimary = useWatch({
    name: getAttributeName('box.unselected.text.color.primary'),
  });

  const selectedTextColorSecondary = useWatch({
    name: getAttributeName('box.selected.text.color.secondary'),
  });

  const unselectedTextColorSecondary = useWatch({
    name: getAttributeName('box.unselected.text.color.secondary'),
  });

  const textColorActive = useWatch({
    name: getAttributeName('text.color.active'),
  });

  return (
    <section className="mt-3 w-full flex flex-col gap-2">
      {product?.bumps?.length && (
        <div className="flex items-center my-4 font-bold">
          <Iconify
            style={{ color: iconColor }}
            icon="heroicons:receipt-percent-16-solid"
            width={20}
            height={20}
            className="mr-2"
          />
          <Typography fontWeight="bold" fontSize={16} style={{ color: primaryColor }}>
            {product?.bumps?.length > 1 ? 'Ofertas limitadas' : 'Oferta limitada'}
          </Typography>
        </div>
      )}
      {product?.bumps?.map((bump) => (
        <div
          key={bump.id}
          role="button"
          tabIndex={0}
          style={{
            backgroundColor: selectedBumpsIds.includes(bump.id)
              ? selectedBackgroundColor
              : unselectedBackgroundColor,
            border: `2px ${selectedBumpsIds.includes(bump.id) ? 'solid' : 'dashed'} ${
              selectedBumpsIds.includes(bump.id)
                ? selectedHeaderBackgroundColor
                : unselectedHeaderBackgroundColor
            }`,
          }}
          className={`       
          cursor-pointer
          ease-in-out
          duration-150
          my-1
          overflow-hidden
          rounded-md
          shadow-md
          transition
          w-full
        `}
          onClick={() => handleCheck(bump)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleCheck(bump.id);
            }
          }}
        >
          <div
            style={{
              backgroundColor: selectedBumpsIds.includes(bump.id)
                ? selectedHeaderBackgroundColor
                : unselectedHeaderBackgroundColor,
            }}
            className={`
          
          ease-in-out
          flex
          justify-between
          py-2
          px-4
          transition
        `}
          >
            <div className="flex-grow">
              <Typography
                color={
                  selectedBumpsIds.includes(bump.id)
                    ? selectedHeaderTextColorPrimary
                    : unselectedHeaderTextColorPrimary
                }
                variant={device === 'desktop' ? 'h6' : 'caption'}
                fontSize={device === 'mobile' && 12}
                marginTop={device === 'mobile' && '3px'}
                fontWeight="bold"
                className="text-xl xs:text-lg text-white font-semibold line-clamp-3 overflow-hidden text-ellipsis"
              >
                {bump?.cta.trim()}
              </Typography>
            </div>
            <div className="flex items-center shrink-0 basis-auto">
              {selectedBumpsIds.includes(bump.id) && device === 'desktop' && (
                <Typography
                  sx={{ marginRight: 1 }}
                  color={
                    selectedBumpsIds.includes(bump.id)
                      ? selectedHeaderTextColorPrimary
                      : selectedHeaderTextColorPrimary
                  }
                  variant="body2"
                  className="text-white mr-1 xs:hidden"
                >
                  Selecionado
                </Typography>
              )}
              <CircleCheckIcon
                className={`h-4 w-4 ${
                  selectedBumpsIds.includes(bump.id) ? 'text-[#38C4AF]' : 'text-slate-500'
                }`}
              />
            </div>
          </div>

          <div className="flex p-4 xs:p-3">
            <div
              className={`flex  gap-2 items-center w-full  ${
                device === 'dektop' ? 'flex-col' : 'flex-row'
              }`}
            >
              <div className="flex gap-2 w-full">
                {bump?.image && bump?.showImage && (
                  <div className="w-[80px] h-[80px] xs:w-[80px] xs:h-[80px] flex-shrink-0">
                    <img
                      src={bump?.image}
                      alt="Imagem do produto"
                      className="w-full h-full rounded-md"
                    />
                  </div>
                )}

                <div className="flex flex-col justify-center w-full gap-1">
                  <div className="flex justify-between">
                    <div className="flex-1">
                      <Typography
                        color={
                          selectedBumpsIds.includes(bump.id)
                            ? selectedTextColorPrimary
                            : unselectedTextColorPrimary
                        }
                        variant="body1"
                        className="font-bold text-white text-md line-clamp-3 overflow-hidden text-ellipsis"
                        style={{
                          fontWeight: 'bold',
                        }}
                      >
                        {bump?.title.trim() || 'Nome do seu produto'}
                      </Typography>
                    </div>
                    <div className="min-w-[100px] flex justify-center items-center gap-2">
                      {bump?.referencePrice !== null && (
                        <>
                          <Typography
                            color={
                              selectedBumpsIds.includes(bump.id)
                                ? selectedTextColorSecondary
                                : unselectedTextColorSecondary
                            }
                            variant={device === 'desktop' ? 'body2' : 'caption'}
                            fontSize={device === 'desktop' ? 14 : 10}
                            className="text-gray-400 line-through mr-1 xs:text-xs"
                          >
                            {getFinalPrice(
                              bump?.offer?.type,
                              bump?.referencePrice,
                              bump?.offer?.recurrence_period
                            )}
                          </Typography>
                          <Typography
                            variant={device === 'desktop' ? 'body2' : 'caption'}
                            fontSize={device === 'desktop' ? 14 : 10}
                            color={selectedHeaderTextColorPrimary}
                            className="bg-[#0F7864] text-white text-xs font-bold px-2 py-1 rounded-full xs:px-1 xs:font-semibold"
                          >
                            {Math.round(
                              ((+(bump?.referencePrice || 0) - +(bump?.offer?.price || 0)) /
                                +(bump?.referencePrice || 0)) *
                                100
                            )}
                            % OFF
                          </Typography>
                        </>
                      )}
                    </div>
                  </div>
                  <div
                    className={`flex ${
                      device === 'desktop' ? 'justify-between' : 'flex-col'
                    } gap-2`}
                  >
                    <div className="flex-1">
                      <Typography
                        color={
                          selectedBumpsIds.includes(bump.id)
                            ? selectedTextColorSecondary
                            : unselectedTextColorSecondary
                        }
                        variant="body2"
                        className="text-[#919EAB]"
                      >
                        {bump?.description.trim()}
                      </Typography>
                    </div>
                    <div
                      className={`min-w-[100px] max-w-[150px] ${
                        device === 'desktop' && 'text-right'
                      }`}
                    >
                      <Typography
                        color={textColorActive}
                        variant="subtitle1"
                        className="font-bold text-md text-white"
                      >
                        {getFinalPrice(
                          bump?.offer?.type,
                          bump?.offer?.price,
                          bump?.offer?.recurrence_period
                        )}
                      </Typography>
                    </div>
                  </div>
                  <div>
                    {paymentMethod === 'Cartão de Crédito' && (
                      <Input
                        label={bump?.offer?.type === 'subscription' ? 'Assinatura' : 'Parcelas'}
                        value={getFinalPrice(
                          bump?.offer?.type,
                          bump?.offer?.price,
                          bump?.offer?.recurrence_period
                        )}
                        isSelect
                        backgroundColor={
                          selectedBumpsIds.includes(bump.id)
                            ? selectedBackgroundColor
                            : unselectedBackgroundColor
                        }
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            style={{
              width: '100%',
              height: 30,
              background: selectedBumpsIds.includes(bump.id)
                ? selectedHeaderBackgroundColor
                : unselectedHeaderBackgroundColor,
              display: 'flex',
              alignItems: 'center',
              padding: '0 10px',
              cursor: 'pointer',
            }}
          >
            <input
              type="checkbox"
              checked={selectedBumpsIds.includes(bump.id)}
              onChange={() => handleCheck(bump)}
              style={{
                marginRight: '8px',
                cursor: 'pointer',
                accentColor: selectedBumpsIds.includes(bump.id) ? '#38C4AF' : 'auto', // Aplica #38C4AF quando checado
              }}
            />
            <Typography
              style={{
                userSelect: 'none',
                fontSize: '14px',
                color: selectedBumpsIds.includes(bump.id)
                  ? selectedHeaderTextColorPrimary
                  : unselectedHeaderTextColorPrimary, // Branco se selecionado
                fontWeight: 'bold',
              }}
            >
              Adicionar Produto
            </Typography>
          </div>
        </div>
      ))}
    </section>
  );
}
