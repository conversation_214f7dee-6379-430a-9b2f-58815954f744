import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';
import { tv } from 'tailwind-variants';

const checkbox = tv({
  base: 'bg-white border-0 cursor-pointer rounded-md shadow-sm accent-indigo-600 outline-none transition ease-in-out duration-150',
  slots: {
    label: 'text-slate-500 flex-1 cursor-pointer',
  },
  variants: {
    color: {
      primary: 'focus:ring-blue-500 focus:border-blue-500',
      error: 'border-red-500 focus:ring-red-500 focus:border-red-500',
    },
    size: {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base w-4 h-4',
      lg: 'px-6 py-4 text-lg w-6 h-6',
    },
  },
  compoundSlots: [
    {
      slots: ['label'],
      size: 'sm',
      className: 'text-xs',
    },
    {
      slots: ['label'],
      size: 'md',
      className: 'text-sm',
    },
    {
      slots: ['label'],
      size: 'lg',
      className: 'text-md',
    },
  ],
  defaultVariants: {
    color: 'primary',
    size: 'md',
  },
});

export const Checkbox = ({ color = 'primary', name, size = 'md', label, className, ...props }) => {
  const { control } = useFormContext();
  // const { text } = useSettings();

  const { base: baseSlot, label: labelSlot } = checkbox({
    color,
    size,
  });

  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const secondaryColor = useWatch({
    name: getAttributeName('font.color.secondary'),
  });

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <div className={`flex flex-row w-full items-start gap-1 ${className}`}>
          <div className="flex flex-row gap-3 items-center justify-start">
            <input
              type="checkbox"
              className={baseSlot({ color: error ? 'error' : color, size })}
              id={name}
              {...field}
              {...props}
            />
            <Typography
              variant="subtitle2"
              className={labelSlot({ color: error ? 'error' : color, size })}
              htmlFor={name}
              style={{
                color: secondaryColor,
              }}
            >
              {label}
            </Typography>
          </div>
          {error && <span className="text-red-500 text-xs">{error.message}</span>}
        </div>
      )}
    />
  );
};

Checkbox.propTypes = {
  color: PropTypes.oneOf(['primary', 'error']),
  name: PropTypes.string.isRequired,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  label: PropTypes.string.isRequired,
  className: PropTypes.string,
};
