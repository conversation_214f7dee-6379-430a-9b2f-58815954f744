import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import Iconify from '@/components/iconify';
import { useWatch } from 'react-hook-form';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';

export const Input = ({ label, value, isCellphone, isSelect, backgroundColor }) => {
  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const secondaryColor = useWatch({
    name: getAttributeName('text.color.secondary'),
  });

  return (
    <div className="flex flex-col w-full items-start">
      <Typography color={secondaryColor} variant="caption" className="text-slate-500 text-xs">
        {label}
      </Typography>
      <div
        role="textbox"
        style={{ backgroundColor }}
        className=" 
            block
            border
            border-[#919EAB33]
            ease-in-out duration-150
            focus:ring-1
            outline-none
            rounded-md
            shadow-sm
            text-white
            transition
            w-full
            px-3 py-2 text-sm
            relative
        "
      >
        <div className="flex flex-row">
          {isCellphone && (
            <div className="flex items-center justify-center">
              <img
                src="/assets/brazil-flag.png"
                alt="Brazil Flag"
                style={{ width: '20px', height: '20px', marginRight: '4px' }}
              />
              <Iconify icon="bxs:down-arrow" width={6} height={6} sx={{ color: '#919EAB33' }} />
              <div className="h-full w-px ml-1 bg-[#919EAB33] mr-2" />
            </div>
          )}
          <Typography color={primaryColor} variant="body2">
            {value}
          </Typography>

          {isSelect && (
            <Iconify
              icon="ep:arrow-down-bold"
              width={12}
              height={12}
              sx={{ position: 'absolute', right: 5, top: 15 }}
            />
          )}
        </div>
      </div>
    </div>
  );
};
Input.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  isCellphone: PropTypes.bool,
  isSelect: PropTypes.bool,
  backgroundColor: PropTypes.string,
};

export default Input;
