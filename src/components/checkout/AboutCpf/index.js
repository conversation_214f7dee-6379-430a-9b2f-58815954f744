import PropTypes from 'prop-types';
import { useState } from 'react';
import { Dialog, Typography } from '@mui/material';
import { useWatch } from 'react-hook-form';
import { getOppositeColor, applyTransparency } from '@/utils/colors';
import useCheckoutBuilderFormHelper from '@/sections/@checkout-builder/hooks/useCheckoutBuilderFormHelper';

export function AboutCpf() {
  const [showModal, setShowModal] = useState(false);

  const onOpen = () => setShowModal(true);

  return (
    <>
      <button type="button" onClick={onOpen} className="flex items-center hover:underline">
        <Typography
          variant="caption"
          className="mr-2 -mt-1.5 -mb-3 text-xs leading-4 LinkButton text-[#36B37E]  hover:underline"
        >
          Porque pedimos esse dado?
        </Typography>
      </button>

      <ModalContent open={showModal} onClose={() => setShowModal(false)} />
    </>
  );
}

export function ModalContent({ open, onClose }) {
  const {
    settings: { getAttributeName },
  } = useCheckoutBuilderFormHelper();

  const primaryColor = useWatch({
    name: getAttributeName('text.color.primary'),
  });

  const secondaryColor = useWatch({
    name: getAttributeName('text.color.secondary'),
  });

  const backgroundColor = useWatch({
    name: getAttributeName('background.color'),
  });

  const formBackgroundColor = useWatch({
    name: getAttributeName('form.background.color'),
  });

  const payButtonBackgroundColor = useWatch({
    name: getAttributeName('payButton.color'),
  });

  const payButtonTextColor = useWatch({
    name: getAttributeName('payButton.text.color'),
  });

  return (
    <Dialog open={open} onClose={onClose}>
      <div
        style={{
          backgroundColor: applyTransparency(getOppositeColor(backgroundColor), 0.5),
        }}
        className="fixed inset-0 z-50 flex items-center justify-center  bg-opacity-50"
      >
        <div
          style={{ backgroundColor: formBackgroundColor }}
          className=" rounded-lg shadow-lg p-6 max-w-md mx-4"
        >
          <div className="flex flex-col">
            <h2 style={{ color: primaryColor }} className="text-lg font-semibold  mb-4">
              Sobre CPF/CNPJ
            </h2>
            <p style={{ color: secondaryColor }} className="text-sm  mb-6">
              Esse dado é necessário para garantir a segurança da sua compra e cadastro em nossa
              plataforma, essa informação também pode ser usada para emissão de Nota Fiscal.
            </p>
            <button
              onClick={onClose}
              type="button"
              style={{
                backgroundColor: payButtonBackgroundColor,
                color: payButtonTextColor,
              }}
              className="px-6 py-2 bg-[#0F7864] text-white rounded-md hover:bg-[#0b6856] hover:shadow-button-hover transition-colors w-full"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </Dialog>
  );
}

ModalContent.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
};
