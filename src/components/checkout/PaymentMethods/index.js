import PropTypes from 'prop-types';
import { useState, useContext, useEffect } from 'react';
import { useWatch } from 'react-hook-form';
import { Box, Typography } from '@mui/material';
import { tv } from 'tailwind-variants';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';

import { mapIcons } from './utils';
import { ApplePay, Boleto, CreditCard, GooglePay, PicPay, Pix, Nubank, ThreeDs } from './components';

export function PaymentMethods({
  iconColor,
  selectedTextColor,
  unselectedTextColor,
  selectedBackgroundColor,
  unselectedBackgroundColor,
  selectedIconColor,
  unselectectedIconColor,
  primaryColor,
  secondaryColor,
  isMobile,
  lastDesktopRows,
}) {
  const tab = tv({
    base: `
    bg-[${unselectedBackgroundColor}]
    border
    border-[#919EAB33]
    flex
    cursor-pointer
    font-medium
    gap-3
    h-[100px]
    hover:shadow-lg
    items-center
    justify-center
    outline-none
    p-[10px]
    rounded-md
    shadow-md
    shadow-sm
    flex-col
    text-center
    text-sm
    `,
    variants: {
      status: {
        active: `bg-[${selectedBackgroundColor}]`,
        inactive: 'text-gray-700',
      },
      width: {
        fullMobile: 'w-[90px]',
        minMobile: 'flex-1',
        minDesktop: 'flex-1',
        fullDesktop: 'w-[150px]',
      },
    },
  });

  const [activeTab, setActiveTab] = useState(null);
  const { product, setPaymentMethod } = useContext(CheckoutBuilderContext);

  const type = product?.offers?.find((offer) => offer.default)?.type;

  const allowedPaymentMethods =
    type === 'subscription' ? ['credit_card', 'boleto', 'pix', 'pix_auto'] : product?.paymentMethods;

  const paymentsOrder = product?.paymentsOrder?.filter((payment) =>
    allowedPaymentMethods.includes(payment)
  );

  // Garantir que pix_auto só apareça para produtos de assinatura
  const filteredPaymentMethods = product?.paymentMethods?.filter((payment) =>
    type === 'subscription' ? allowedPaymentMethods.includes(payment) : payment !== 'pix_auto'
  );

  const unorderedPaymentMethods = filteredPaymentMethods?.filter((payment) =>
    allowedPaymentMethods.includes(payment)
  );

  const paymentMethods = unorderedPaymentMethods?.sort(
    (a, b) => paymentsOrder.indexOf(a) - paymentsOrder.indexOf(b)
  );

  const nubankIndex = paymentMethods?.indexOf('openfinance_nubank');
  if (nubankIndex > -1) {
    const [nubankItem] = paymentMethods.splice(nubankIndex, 1);
    paymentMethods.unshift(nubankItem);
  }

  const mapLabels = {
    pix: 'PIX',
    pix_auto: 'PIX Automático',
    boleto: 'Boleto',
    credit_card: 'Cartão de Crédito',
    picpay: 'PicPay',
    applepay: 'ApplePay',
    googlepay: 'GooglePay',
    openfinance_nubank: 'Nubank',
    threeDs: '3DS',
  };

  const mapComponents = {
    pix: Pix,
    pix_auto: Pix,
    boleto: Boleto,
    credit_card: CreditCard,
    picpay: PicPay,
    applepay: ApplePay,
    googlepay: GooglePay,
    openfinance_nubank: Nubank,
    threeDs: ThreeDs,
  };

  const getIconColor = (name) => {
    if (activeTab === name) {
      return selectedIconColor;
    }

    if (name === 'openfinance_nubank') return '#ffffff';

    return unselectectedIconColor;
  };

  const getTextColor = (name) => {
    if (name === 'openfinance_nubank') return '#ffffff';

    if (activeTab === name) return selectedTextColor;

    return unselectedTextColor;
  };

  const getBackgroundColor = (name) => {
    if (name === 'openfinance_nubank') {
      if (activeTab === name) {
        return '#4C0677';
      }

      return '#820AD1';
    }

    if (activeTab === name) {
      return selectedBackgroundColor;
    }

    return unselectedBackgroundColor;
  };

  const tabs = paymentMethods?.map((paymentMethod) => ({
    name: paymentMethod,
    Icon(color) {
      return mapIcons(color)[paymentMethod];
    },
    label: mapLabels[paymentMethod],
    Component: mapComponents[paymentMethod],
  }));

  const getTabWidth = () => {
    if (isMobile) {
      if (tabs.length < 4) {
        return 'minMobile';
      }

      return 'fullMobile';
    }

    if (['[4,8]', '[4,4,4]'].includes(JSON.stringify(lastDesktopRows))) {
      if (tabs.length < 4) {
        return 'minMobile';
      }

      return 'fullMobile';
    }

    if (tabs.length < 6) {
      return 'minDesktop';
    }
    return 'fullDesktop';
  };

  const onChangeTab = (name) => {
    setActiveTab(name);
    setPaymentMethod(mapLabels[name]);
  };

  const device = useWatch({
    name: 'device',
  });

  useEffect(() => {
    if (product) {
      const initialIndex = paymentMethods?.findIndex((method) =>
        product?.defaultPaymentMethod
          ? method === product?.defaultPaymentMethod
          : method === 'credit_card'
      );
      setActiveTab(paymentMethods?.[initialIndex]);
      setPaymentMethod(mapLabels?.[paymentMethods[initialIndex]]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product]);

  return (
    <>
      <Box className="flex flex-row flex-wrap gap-2 mb-3 justify-center">
        {tabs?.map(({ name, Icon, label }, index) => (
          <Box
            onClick={() => onChangeTab(name)}
            key={index}
            className={tab({
              status: activeTab === name ? 'active' : 'inactive',
              width: getTabWidth(),
            })}
            sx={{
              backgroundColor: getBackgroundColor(name),
            }}
          >
            {Icon(getIconColor(name))}
            <Typography
              variant="body2"
              color={getTextColor(name)}
              className="font-semibold leading-tight"
            >
              {label}
            </Typography>
          </Box>
        ))}
      </Box>

      {tabs
        ?.find((item) => item.name === activeTab)
        ?.Component({ iconColor, primaryColor, secondaryColor, device })}
    </>
  );
}

PaymentMethods.propTypes = {
  selectedIconColor: PropTypes.string,
  unselectectedIconColor: PropTypes.string,
  selectedTextColor: PropTypes.string,
  unselectedTextColor: PropTypes.string,
  selectedBackgroundColor: PropTypes.string,
  unselectedBackgroundColor: PropTypes.string,
  iconColor: PropTypes.string,
  primaryColor: PropTypes.string,
  secondaryColor: PropTypes.string,
  isMobile: PropTypes.bool,
  lastDesktopRows: PropTypes.array,
};
