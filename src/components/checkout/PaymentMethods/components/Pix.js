import PropTypes from 'prop-types';
import React from 'react';
import { Typography } from '@mui/material';

const pixAdvantage = [
  { message: 'Liberação imediata' },
  {
    message: 'É simples, só usar o aplicativo de seu banco para pagar Pix',
  },
];

export function Pix({ primaryColor }) {
  return (
    <div className="rounded-md border border-[#0F7864] border-dashed p-3">
      <ul>
        {pixAdvantage.map(({ message }) => (
          <li key={message} className="flex items-center py-1 px-6 xs:px-1">
            <img
              src="/assets/icons/checkout/checkmark-big-circle.svg"
              alt="Checkmark Icon"
              style={{ height: '15px', width: '15px', marginRight: '10px' }}
            />
            <Typography style={{ color: primaryColor }} variant="body2">
              {message}
            </Typography>
          </li>
        ))}
      </ul>
    </div>
  );
}

Pix.propTypes = {
  primaryColor: PropTypes.string,
};
