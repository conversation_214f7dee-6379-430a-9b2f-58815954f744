import PropTypes from 'prop-types';
import { useContext } from 'react';
import { Typography } from '@mui/material';
import { Card, Checkbox, Input } from '@/components/checkout';
import { SecurityIcon } from '@/assets/icons';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { usePrice } from '../../hooks';

export function CreditCard({ iconColor, secondaryColor, device }) {
  const price = usePrice();
  const { product } = useContext(CheckoutBuilderContext);

  const offerType = product?.offers?.find((offer) => offer.default)?.type;

  return (
    <Card color="secondary" outline shadow={false} className="w-full">
      <section className="flex flex-col gap-3">
        <Input label="Número do cartão" value="5125 1456 5641 4010" />

        <div className="flex flex-wrap gap-3">
          <div
            className={`flex flex-1  gap-3 w-full ${
              device === 'desktop' ? 'flex-row' : 'flex-col'
            }`}
          >
            <Input label="Vencimento" value="10/25" />
            <Input label="CVV" value="123" />
            {device === 'mobile' && (
              <Input
                isSelect
                label={offerType === 'subscription' ? 'Assinatura' : 'Parcelas'}
                value={price}
              />
            )}
          </div>
          {device === 'desktop' && (
            <div className="flex w-full md:flex-1">
              <Input
                isSelect
                label={offerType === 'subscription' ? 'Assinatura' : 'Parcelas'}
                value={price}
              />
            </div>
          )}
        </div>
      </section>

      <Checkbox
        name="saveCard"
        label="Salvar dados para as próximas compras"
        className="my-5"
        size="md"
        checked={false}
      />

      <div className="mt-1 flex flex-row items-center gap-2">
        <SecurityIcon style={{ color: iconColor }} className="h-5 w-5 text-slate-500" />
        <Typography variant="subtitle2" color={secondaryColor} className="text-xs font-thin ">
          Os seus dados de pagamento são criptografados e processados de forma segura.
        </Typography>
      </div>
    </Card>
  );
}

CreditCard.propTypes = {
  iconColor: PropTypes.string,
  secondaryColor: PropTypes.string,
  device: PropTypes.string,
};
