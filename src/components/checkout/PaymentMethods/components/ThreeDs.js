import PropTypes from 'prop-types';
import React from 'react';
import { Typography } from '@mui/material';

const threeDsAdvantages = [
  { message: 'Segurança adicional com autenticação 3D Secure' },
  { message: 'Proteção contra fraudes em transações online' },
  { message: 'Aprovação mais rápida e confiável' },
];

export function ThreeDs({ primaryColor }) {
  return (
    <div className="rounded-md border border-[#0F7864] border-dashed p-3">
      <ul>
        {threeDsAdvantages.map(({ message }) => (
          <li key={message} className="flex items-center py-1 px-6 xs:px-1">
            <img
              src="/assets/icons/checkout/checkmark-big-circle.svg"
              alt="Checkmark Icon"
              style={{ height: '15px', width: '15px', marginRight: '10px' }}
            />
            <Typography style={{ color: primaryColor }} variant="body2">
              {message}
            </Typography>
          </li>
        ))}
      </ul>
    </div>
  );
}

ThreeDs.propTypes = {
  primaryColor: PropTypes.string,
};
