import { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { tv } from 'tailwind-variants';
import { Typography } from '@mui/material';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';

const button = tv({
  base: `
  aling-center
  block
  bg-[#0F7864]
  duration-300
  disabled:opacity-60
  ease-in-out
  font-bold
  hover:bg-[#0b6856]
  hover:shadow-button-hover
  rounded-md
  shadow-sm
  text-white
  xs:text-sm
  transition
  w-full
  `,
  variants: {
    buttonColor: {
      green: 'bg-green-500 hover:bg-green-600',
      indigo: 'bg-indigo-500 hover:bg-indigo-600',
    },
    size: {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-6 py-4 text-lg',
    },
  },
});

export const PayButton = ({
  size = 'md',
  payButtonColor,
  payButtonTextColor,
  className,
  backgroundColor,
  type = 'button',
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const { paymentMethod } = useContext(CheckoutBuilderContext);

  return (
    <button
      type="button"
      style={{
        backgroundColor: payButtonColor,
        color: payButtonTextColor,
        opacity: isHovered ? 0.8 : 1,
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`${button({ size })}`}
      {...props}
    >
      <Typography variant="subtitle1">{`Pagar com ${paymentMethod}`}</Typography>
    </button>
  );
};

PayButton.propTypes = {
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  payButtonColor: PropTypes.string,
  payButtonTextColor: PropTypes.string,
  className: PropTypes.string,
  backgroundColor: PropTypes.string,
  type: PropTypes.string,
};
