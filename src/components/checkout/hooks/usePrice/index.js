import { useContext } from 'react';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { fCurrency } from '@/utils/formatNumber';

export function usePrice() {
  const { product, couponData } = useContext(CheckoutBuilderContext);

  const offerType = product?.offers?.find((offer) => offer.default)?.type;
  const offerRecurrencePeriod = product?.offers?.find((offer) => offer.default)?.recurrence_period;

  const applyCouponValue = couponData ? 1 : 0;

  const originalPrice = product?.price || 0;
  const discount = (applyCouponValue * (originalPrice * (couponData?.discount || 0))) / 100;

  const price = +(product?.price || 0) - discount;
  const minPrice = Math.max(0, price);

  if (offerType === 'subscription') {
    if (offerRecurrencePeriod === 7) return `${fCurrency(Number(minPrice))} / semana`;

    if (offerRecurrencePeriod === 30) return `${fCurrency(Number(minPrice))} / mês`;

    if (offerRecurrencePeriod === 60) return `${fCurrency(Number(minPrice))} / bimestre`;

    if (offerRecurrencePeriod === 90) return `${fCurrency(Number(minPrice))} / trimestre`;

    if (offerRecurrencePeriod === 180) return `${fCurrency(Number(minPrice))} / semestre`;

    if (offerRecurrencePeriod === 365) return `${fCurrency(Number(minPrice))}/ semestre`;

    return `${fCurrency(Number(product?.price || 0))} / mês`;
  }

  if (!product?.paymentMethods?.includes('credit_card')) return '';

  return `1 X de ${fCurrency(minPrice)}`;
}
