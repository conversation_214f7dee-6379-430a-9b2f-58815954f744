.border-wave-effect {
  --size: 6px;
  --m: 0.5;
  --p: calc(var(--m) * var(--size));
  --R: calc(var(--size) * sqrt(var(--m) * var(--m) + 1));

  mask: radial-gradient(var(--R) at 50% calc(100% - (var(--size) + var(--p))), #000 99%, #0000 101%)
      calc(50% - 2 * var(--size)) 0 / calc(4 * var(--size)) 100%,
    radial-gradient(var(--R) at 50% calc(100% + var(--p)), #0000 99%, #000 101%) 50%
      calc(100% - var(--size)) / calc(4 * var(--size)) 100% repeat-x;
  height: calc(100% - 20px);
}
