import React from 'react';
import PropTypes from 'prop-types';
import { tv } from 'tailwind-variants';
import styles from './Card.module.css';

const card = tv({
  base: `
  block
  p-5
  xs:p-3
  rounded-md
  shadow-lg
  w-full
  `,
  variants: {
    color: {
      primary: 'bg-white',
      secondary: 'bg-indigo-50 bg-opacity-50',
    },
    outline: {
      true: 'border border-slate-300',
    },
    shadow: {
      false: 'shadow-none',
    },
  },
});

export const Card = ({
  backgroundColor = '#212B36',
  borderEffect = '',
  color,
  children,
  outline,
  shadow,
  ...props
}) => (
  <div
    className={`bg-[${backgroundColor}] ${card({ color, outline, shadow })} ${
      borderEffect && styles[borderEffect]
    }`}
    {...props}
  >
    {children}
  </div>
);

Card.propTypes = {
  backgroundColor: PropTypes.string,
  borderEffect: PropTypes.string,
  children: PropTypes.node.isRequired,
  color: PropTypes.oneOf(['primary', 'secondary']),
  outline: PropTypes.bool,
  shadow: PropTypes.bool,
};
