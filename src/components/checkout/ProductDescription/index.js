import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import React, { useContext } from 'react';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { applyTransparency } from '@/utils/colors';
import { fCurrency } from '@/utils/formatNumber';
import { usePrice } from '../hooks';

export const ProductDescription = ({ primaryColor, secondaryColor, activeColor }) => {
  const price = usePrice();

  const { product } = useContext(CheckoutBuilderContext);

  const offerType = product?.offers?.find((offer) => offer.default)?.type;

  const hasCreditCardPaymentMethod = !!product?.paymentMethods?.includes('credit_card');

  return (
    <div className="col-span-full mb-3 text-white rounded-md overflow-hidden">
      <div className="px-1 pt-2 pb-3 flex">
        {product?.image && (
          <img
            src={product?.image}
            alt="Imagem do produto"
            className="w-24 h-24 object-cover rounded-md mr-4"
          />
        )}
        <div>
          <Typography
            style={{ color: primaryColor }}
            variant="h5"
            className="font-bold text-lg text-white"
          >
            {product?.name}
          </Typography>
          <Typography
            style={{ color: activeColor }}
            variant="subtitle1"
            className="font-bold text-md text-white"
          >
            {price}
          </Typography>

          {offerType !== 'subscription' && (
            <Typography
              variant="subtitle2"
              style={{
                color: hasCreditCardPaymentMethod
                  ? applyTransparency(secondaryColor, 0.4)
                  : activeColor,
              }}
              className="text-gray-300"
            >{`${hasCreditCardPaymentMethod ? 'ou' : ''} ${fCurrency(
              +(product?.price || 0)
            )} à vista`}</Typography>
          )}
        </div>
      </div>
    </div>
  );
};

ProductDescription.propTypes = {
  primaryColor: PropTypes.string,
  secondaryColor: PropTypes.string,
  activeColor: PropTypes.string,
};
