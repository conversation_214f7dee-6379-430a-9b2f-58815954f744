import PropTypes from 'prop-types';

import '../../utils/highlight';

import 'react-quill/dist/quill.bubble.css';

import { styled } from '@mui/material/styles';
import ReactQuill from 'react-quill';

import { useState } from 'react';
import { v4 } from 'uuid';
import './bubble.css';

const Sizes = ReactQuill.Quill.import('attributors/style/size');

Sizes.whitelist = ['16px', '24px', '32px', '42px', '64px'];

ReactQuill.Quill.register(Sizes, true);

BubbleEditor.propTypes = {
  name: PropTypes.string,
};

const formats = ['bold', 'italic', 'underline', 'link', 'size', 'color', 'align'];

const QuillContainer = styled('div')({
  '.ql-formats': {
    position: 'relative',
    zIndex: '50 !important',
  },
  '.ql-picker': {
    zIndex: '50 !important',
  },
  '.ql-tooltip': {
    zIndex: '50 !important',
  },
});

export default function BubbleEditor({ ...other }) {
  const [uuid] = useState(v4());

  const modules = {
    toolbar: `.toolbar-${uuid}`,
    history: {
      delay: 500,
      maxStack: 100,
      userOnly: true,
    },
    syntax: true,
    clipboard: {
      matchVisual: false,
    },
  };

  return (
    <QuillContainer>
      <div className={`toolbar-${uuid}`}>
        <div className="ql-formats">
          <button type="button" className="ql-bold" />
          <button type="button" className="ql-italic" />
          <button type="button" className="ql-underline" />
          <button type="button" className="ql-link" />
        </div>
        <div className="ql-formats">
          <select className="ql-size">
            <option value="64px" />
            <option value="42px" />
            <option value="32px" />
            <option value="24px" />
            <option value="16px" />
          </select>
          <select className="ql-color" />
          <select className="ql-align" />
        </div>
      </div>
      <ReactQuill
        theme="bubble"
        modules={modules}
        formats={formats}
        placeholder="Write something awesome..."
        className="custom-quill-editor"
        {...other}
      />
    </QuillContainer>
  );
}
