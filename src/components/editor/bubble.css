.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: 'Heading 5';
  font-weight: bold;
  font-size: 1.2rem;
}

.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value='24px']::before {
  content: 'Heading 4';
  font-weight: bold;
  font-size: 1.4rem;
}

.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value='32px']::before {
  content: 'Heading 3';
  font-weight: bold;
  font-size: 1.6rem;
}

.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value='42px']::before {
  content: 'Heading 2';
  font-weight: bold;
  font-size: 1.8rem;
}

.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value='64px']::before {
  content: 'Heading 1';
  font-weight: bold;
  font-size: 2rem;
}

.ql-formats {
  position: relative;
  z-index: 50 !important;
}

.ql-picker {
  z-index: 50 !important;
}

.ql-tooltip {
  z-index: 50 !important;
}