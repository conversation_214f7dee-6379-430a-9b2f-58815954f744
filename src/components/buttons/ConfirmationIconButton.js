import { Button, IconButton, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useState } from 'react';
import Iconify from '../iconify/Iconify';
import MenuPopover from '../menu-popover/MenuPopover';

const ConfirmationIconButton = ({ onConfirm, children, ...props }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  return (
    <>
      <IconButton
        onClick={(event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        }}
        disabled={Boolean(anchorEl)}
        {...props}
      >
        {anchorEl ? (
          <Iconify icon="eva:alert-circle-fill" sx={{ color: 'error.main' }} />
        ) : (
          children
        )}
      </IconButton>
      <MenuPopover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
        }}
      >
        <Stack p={1}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Confirmar?
          </Typography>
          <Stack direction="row" gap={1} sx={{ mt: 2 }}>
            <Button
              size="small"
              color="error"
              variant="contained"
              onClick={(e) => {
                e.stopPropagation();
                onConfirm();
                setAnchorEl(null);
              }}
            >
              Sim
            </Button>
            <Button
              size="small"
              color="inherit"
              onClick={(e) => {
                e.stopPropagation();
                setAnchorEl(null);
              }}
            >
              Não
            </Button>
          </Stack>
        </Stack>
      </MenuPopover>
    </>
  );
};

ConfirmationIconButton.propTypes = {
  onConfirm: PropTypes.func.isRequired,
  children: PropTypes.node,
};

export default ConfirmationIconButton;
