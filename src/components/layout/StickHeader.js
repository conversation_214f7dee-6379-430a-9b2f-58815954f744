import { Stack, useTheme } from '@mui/system';
import PropTypes from 'prop-types';
import { HEADER } from '../../config-global';
import useOffSetTop from '../../hooks/useOffSetTop';
import { bgBlur } from '../../utils/cssStyles';
import { useSettingsContext } from '../settings';

const StickHeader = ({ children, ...props }) => {
  const { themeLayout } = useSettingsContext();

  const isNavHorizontal = themeLayout === 'horizontal';

  const isOffset = useOffSetTop(HEADER.H_DASHBOARD_DESKTOP) && !isNavHorizontal;

  const theme = useTheme();

  return (
    <Stack
      zIndex={1}
      width={1}
      sx={{
        position: isOffset ? 'sticky' : 'static',
        top: isOffset ? HEADER.H_DASHBOARD_DESKTOP_OFFSET : 'auto',
        left: 0,
        right: 0,
        width: 1,
        mt: 0.2,
        py: isOffset ? 2 : 0,
        ...bgBlur({
          color: theme.palette.background.default,
        }),
        transition: theme.transitions.create('all', {
          duration: theme.transitions.duration.longer,
        }),
        ...(props.sx || {}),
      }}
      {...props}
    >
      {children}
    </Stack>
  );
};

StickHeader.propTypes = {
  children: PropTypes.node.isRequired,
  sx: PropTypes.object,
};

export default StickHeader;
