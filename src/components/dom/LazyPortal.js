import PropTypes from 'prop-types';
import { createElement, Fragment, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

const LazyPortal = ({ id, children, ...props }) => {
  const [target, setTarget] = useState(null);

  useEffect(() => {
    setTarget(document.getElementById(id));
  }, [id]);

  return target ? createPortal(createElement(Fragment, props, children), target) : null;
};

LazyPortal.propTypes = {
  id: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

export default LazyPortal;
