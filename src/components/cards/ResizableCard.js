import { Box, alpha } from '@mui/system';
import PropTypes from 'prop-types';
import { Resizable } from 're-resizable';
import { useRef } from 'react';
import ContainerDimensions from 'react-container-dimensions';

const ResizableCard = ({ children, enabled, width, aspectRatio, onResize, ...props }) => {
  const ref = useRef();
  return (
    <ContainerDimensions>
      {({ width: containerWidth, height: containerHeight }) => (
        <Resizable
          defaultSize={{
            width: Math.min(width, containerWidth),
            height: Math.min(width, containerWidth) / (aspectRatio || 1),
          }}
          lockAspectRatio={aspectRatio}
          maxWidth="100%"
          minWidth={250}
          {...props}
        >
          <Box
            ref={ref}
            width={1}
            height={1}
            sx={{
              ...(enabled && {
                border: (theme) => `2px dashed ${theme.palette.grey[500]}`,
              }),
              position: 'relative',
            }}
          >
            {children}
            {enabled && (
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  pointerEvents: 'none',
                  width: '12px !important',
                  height: '12px !important',
                  backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.5),
                  border: (theme) => `1px solid ${theme.palette.primary.main}`,
                }}
              />
            )}
          </Box>
        </Resizable>
      )}
    </ContainerDimensions>
  );
};

ResizableCard.propTypes = {
  children: PropTypes.node.isRequired,
  enabled: PropTypes.bool,
  sx: PropTypes.object,
  width: PropTypes.number,
  onResize: PropTypes.func,
  aspectRatio: PropTypes.number,
};

export default ResizableCard;
