import { sendUserNPS, skipNPS } from '@/services/user';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentSatisfiedIcon from '@mui/icons-material/SentimentSatisfied';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentVerySatisfiedIcon from '@mui/icons-material/SentimentVerySatisfied';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useMutation } from '@tanstack/react-query';
import { isBefore, parseISO } from 'date-fns';
import { useEffect, useState } from 'react';

import { useAuthContext } from '@/auth/useAuthContext';
import CloseIcon from '@mui/icons-material/Close';

const Root = styled(Box)(({ theme }) => ({
  position: 'fixed',
  bottom: '20px',
  right: '20px',
  zIndex: 1000,
  backgroundColor: theme.palette.background.paper,
  padding: '20px',
  borderRadius: '10px',
  boxShadow: theme.shadows[4],
  textAlign: 'center',
  [theme.breakpoints.down('sm')]: {
    width: '90%',
    right: '5%',
    bottom: '300px',
    padding: '10px',
  },
}));

const Circle = styled(Box)(({ theme, color }) => ({
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '10px auto',
  cursor: 'pointer',
  backgroundColor: color,
  color: 'black',
  transition: 'background-color 0.2s',
  '&:hover': {
    backgroundColor: theme.palette.action.hoverOpacity,
  },
  [theme.breakpoints.down('sm')]: {
    width: '40px',
    height: '40px',
  },
}));

const SelectedCircle = styled(Circle)(({ theme }) => ({
  border: `4px solid ${
    theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.common.black
  }`,
}));

const getColor = (value) => {
  switch (value) {
    case 1:
      return '#f44336';
    case 2:
      return '#ff9800';
    case 3:
      return '#ffeb3b';
    case 4:
      return '#8bc34a';
    case 5:
      return '#4caf50';
    default:
      return '';
  }
};

export const NPS = () => {
  const [visible, setVisible] = useState(false);
  const [note, setNote] = useState(null);
  const [error, setError] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [justification, setJustification] = useState('');

  const { user, setUser } = useAuthContext();

  const { mutate, isLoading } = useMutation({
    mutationFn: sendUserNPS,
    onSuccess({ next_nps_survey_date }) {
      setIsSubmitted(true);
      setUser({ ...user, next_nps_survey_date });
    },
    onError() {
      setError('Ocorreu um erro ao tentar enviar avaliação');
    },
  });

  const { mutate: mutateSkip } = useMutation({
    mutationFn: skipNPS,
    onSuccess({ next_nps_survey_date }) {
      setUser({ ...user, next_nps_survey_date });
    },
  });

  const handleSelect = (value) => {
    setError('');
    setNote(value);
  };

  const handleSubmit = () => {
    if (!note) {
      setError('Selecione uma opção acima');
      return;
    }

    mutate({ note, justification });
  };

  const handleClose = () => {
    if (!isSubmitted) {
      mutateSkip();
    }
    setVisible(false);
  };

  useEffect(() => {
    if (!user?.next_nps_survey_date?.length) {
      setVisible(true);
      return;
    }

    const surveyDate = parseISO(user.next_nps_survey_date);

    const today = new Date();

    if (isBefore(today, surveyDate) === false) {
      setVisible(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!visible) return null;

  if (isSubmitted)
    return (
      <Root>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: 'absolute',
            top: '10px',
            right: '10px',
          }}
        >
          <CloseIcon />
        </IconButton>
        <Typography variant="h5" mt={4} mb={2}>
          Avaliação enviada com sucesso!
        </Typography>

        <Box display="flex" justifyContent="center" alignItems="center" pb={2}>
          <img src="/assets/accept-check.png" alt="NPS" width={60} />
        </Box>

        <Button variant="outlined" color="inherit" onClick={handleClose} fullWidth>
          Fechar
        </Button>
      </Root>
    );

  return (
    <Root>
      <IconButton
        aria-label="close"
        onClick={handleClose}
        disabled={isLoading}
        sx={{
          position: 'absolute',
          top: '10px',
          right: '10px',
        }}
      >
        <CloseIcon />
      </IconButton>
      <Typography variant="h5" mt={4}>
        Como você avalia nosso serviço?
      </Typography>
      <Grid container spacing={2} justifyContent="center">
        {[1, 2, 3, 4, 5].map((value) => (
          <Grid item key={value}>
            {note === value ? (
              <SelectedCircle color={getColor(value)} onClick={() => handleSelect(value)}>
                {value === 1 && <SentimentVeryDissatisfiedIcon fontSize="large" />}
                {value === 2 && <SentimentDissatisfiedIcon fontSize="large" />}
                {value === 3 && <SentimentNeutralIcon fontSize="large" />}
                {value === 4 && <SentimentSatisfiedIcon fontSize="large" />}
                {value === 5 && <SentimentVerySatisfiedIcon fontSize="large" />}
              </SelectedCircle>
            ) : (
              <Circle color={getColor(value)} onClick={() => handleSelect(value)}>
                {value === 1 && <SentimentVeryDissatisfiedIcon fontSize="large" />}
                {value === 2 && <SentimentDissatisfiedIcon fontSize="large" />}
                {value === 3 && <SentimentNeutralIcon fontSize="large" />}
                {value === 4 && <SentimentSatisfiedIcon fontSize="large" />}
                {value === 5 && <SentimentVerySatisfiedIcon fontSize="large" />}
              </Circle>
            )}
            <Typography>{value}</Typography>
          </Grid>
        ))}
      </Grid>
      <FormHelperText error>{error}</FormHelperText>
      <TextField
        label="Justificativa (opcional)"
        multiline
        rows={4}
        variant="outlined"
        fullWidth
        margin="normal"
        value={justification}
        onChange={(e) => setJustification(e.target.value)}
      />
      <LoadingButton
        loading={isLoading}
        variant="contained"
        color="primary"
        onClick={handleSubmit}
        fullWidth
      >
        Enviar
      </LoadingButton>
    </Root>
  );
};
