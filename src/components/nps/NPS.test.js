import { useAuthContext } from '@/auth/useAuthContext';
import { sendUserNPS, skipNPS } from '@/services/user';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { NPS } from './NPS';

// Mock the services and hooks
jest.mock('@/services/user', () => ({
  sendUserNPS: jest.fn(),
  skipNPS: jest.fn(),
}));

jest.mock('@/auth/useAuthContext', () => ({
  useAuthContext: jest.fn(),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('NPS Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock current date to 2025-05-12 19:40:00
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2025-05-12T19:40:00.000Z'));
    console.log('mocked date', new Date());
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Visibility based on NPS survey date', () => {
    it('should render when NPS date is in the past (2025-05-11)', () => {
      useAuthContext.mockReturnValue({
        user: {
          next_nps_survey_date: '2025-05-11T00:00:00.000Z',
        },
      });

      render(<NPS />, { wrapper: createWrapper() });
      expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();
    });

    it('should not render when NPS date is in the future (2025-05-13)', () => {
      useAuthContext.mockReturnValue({
        user: {
          next_nps_survey_date: '2025-05-13T00:00:00.000Z',
        },
      });

      render(<NPS />, { wrapper: createWrapper() });
      expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
    });

    it('should not render when NPS date is 1 second in the future (2025-05-12 19:40:01)', () => {
      useAuthContext.mockReturnValue({
        user: {
          next_nps_survey_date: '2025-05-12T19:40:01.000Z',
        },
      });

      render(<NPS />, { wrapper: createWrapper() });
      expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
    });

    it('should render when NPS date is exactly current time (2025-05-12 19:40:00)', () => {
      useAuthContext.mockReturnValue({
        user: {
          next_nps_survey_date: '2025-05-12T19:40:00.000Z',
        },
      });

      render(<NPS />, { wrapper: createWrapper() });
      expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();
    });

    it('should render when NPS date is 1 second in the past (2025-05-12 19:39:59)', () => {
      useAuthContext.mockReturnValue({
        user: {
          next_nps_survey_date: '2025-05-12T19:39:59.000Z',
        },
      });

      render(<NPS />, { wrapper: createWrapper() });
      expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();
    });
  });

  it('should not render when visible is false', () => {
    useAuthContext.mockReturnValue({
      user: {
        next_nps_survey_date: '2025-05-13T00:00:00.000Z',
      },
    });

    render(<NPS />, { wrapper: createWrapper() });
    expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
  });

  it('should render the NPS survey when visible is true', async () => {
    useAuthContext.mockReturnValue({
      user: {
        next_nps_survey_date: '2025-05-11T00:00:00.000Z',
      },
    });

    render(<NPS />, { wrapper: createWrapper() });

    expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();
    expect(screen.getByLabelText('Justificativa (opcional)')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /enviar/i })).toBeInTheDocument();
  });

  it('should show error when submitting without selecting a rating', () => {
    render(<NPS />, { wrapper: createWrapper() });

    const submitButton = screen.getByRole('button', { name: /enviar/i });
    fireEvent.click(submitButton);

    expect(screen.getByText('Selecione uma opção acima')).toBeInTheDocument();
  });

  it('should submit NPS rating successfully', async () => {
    sendUserNPS.mockResolvedValueOnce(new Promise((resolve) => resolve({})));

    render(<NPS />, { wrapper: createWrapper() });

    // Select rating by clicking the circle div
    const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
    fireEvent.click(ratingCircle);

    // Add justification
    const justificationInput = screen.getByRole('textbox', { name: /justificativa/i });
    fireEvent.change(justificationInput, { target: { value: 'Great service!' } });

    // Submit
    const submitButton = screen.getByRole('button', { name: /enviar/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(sendUserNPS).toHaveBeenCalledWith({
        note: 5,
        justification: 'Great service!',
      });
    });
  });

  it('should show success message after successful submission', async () => {
    sendUserNPS.mockResolvedValueOnce(new Promise((resolve) => resolve({})));

    render(<NPS />, { wrapper: createWrapper() });

    // Select rating by clicking the circle div
    const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
    fireEvent.click(ratingCircle);

    const submitButton = screen.getByRole('button', { name: /enviar/i });
    fireEvent.click(submitButton);

    expect(await screen.findByText('Avaliação enviada com sucesso!')).toBeInTheDocument();
  });

  it('should show error message when submission fails', async () => {
    sendUserNPS.mockRejectedValueOnce(new Error('Failed to submit'));

    render(<NPS />, { wrapper: createWrapper() });

    // Select rating by clicking the circle div
    const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
    fireEvent.click(ratingCircle);

    const submitButton = screen.getByRole('button', { name: /enviar/i });
    fireEvent.click(submitButton);

    expect(
      await screen.findByText('Ocorreu um erro ao tentar enviar avaliação')
    ).toBeInTheDocument();
  });

  it('should close the NPS survey when clicking close button', () => {
    render(<NPS />, { wrapper: createWrapper() });

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
  });

  describe('Skip functionality', () => {
    it('should call skipNPS when closing without submitting', async () => {
      // Mock successful response
      skipNPS.mockResolvedValueOnce({ next_nps_survey_date: '2025-06-12T00:00:00.000Z' });

      const setUser = jest.fn();
      useAuthContext.mockReturnValue({
        user: { next_nps_survey_date: '2025-05-11T00:00:00.000Z' },
        setUser,
      });

      render(<NPS />, { wrapper: createWrapper() });

      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(skipNPS).toHaveBeenCalled();
      });
    });

    it('should call skipNPS when closing after selecting rating but before submitting', async () => {
      // Mock successful response
      skipNPS.mockResolvedValueOnce({ next_nps_survey_date: '2025-06-12T00:00:00.000Z' });

      const setUser = jest.fn();
      useAuthContext.mockReturnValue({
        user: { next_nps_survey_date: '2025-05-11T00:00:00.000Z' },
        setUser,
      });

      render(<NPS />, { wrapper: createWrapper() });

      // Select a rating
      const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
      fireEvent.click(ratingCircle);

      // Close without submitting
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(skipNPS).toHaveBeenCalled();
      });
    });

    it('should not call skipNPS when closing after successful submission', async () => {
      sendUserNPS.mockResolvedValueOnce({ next_nps_survey_date: '2025-06-12T00:00:00.000Z' });

      const setUser = jest.fn();
      useAuthContext.mockReturnValue({
        user: { next_nps_survey_date: '2025-05-11T00:00:00.000Z' },
        setUser,
      });

      render(<NPS />, { wrapper: createWrapper() });

      // Select rating and submit
      const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
      fireEvent.click(ratingCircle);

      const submitButton = screen.getByRole('button', { name: /enviar/i });
      fireEvent.click(submitButton);

      // Wait for submission to complete and success message to appear
      await waitFor(() => {
        expect(screen.getByText('Avaliação enviada com sucesso!')).toBeInTheDocument();
      });

      // Ensure setUser has been called before proceeding
      await waitFor(() => {
        expect(setUser).toHaveBeenCalled();
      });

      // Reset skipNPS mock to ensure it's clean for this test
      skipNPS.mockClear();

      // Close after submission
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      // Just check immediately - no need for additional waiting
      expect(skipNPS).not.toHaveBeenCalled();
    });

    it('should disable close button while submitting', async () => {
      // Mock a delayed response to test loading state
      sendUserNPS.mockImplementationOnce(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () =>
                resolve({
                  next_nps_survey_date: '2025-06-12T00:00:00.000Z',
                }),
              100
            )
          )
      );

      render(<NPS />, { wrapper: createWrapper() });

      // Select rating
      const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
      fireEvent.click(ratingCircle);

      // Start submission
      const submitButton = screen.getByRole('button', { name: /enviar/i });
      fireEvent.click(submitButton);

      // Wait for loading state to be set
      await waitFor(() => {
        const closeButton = screen.getByRole('button', { name: /close/i });
        expect(closeButton).toBeDisabled();
      });

      // Wait for submission to complete
      await waitFor(() => {
        const closeButton = screen.getByRole('button', { name: /close/i });
        expect(closeButton).not.toBeDisabled();
      });
    });
  });

  describe('NPS after submission', () => {
    it('should not reappear after successful submission when next_nps_survey_date is updated', async () => {
      // Set up mock user and next_nps_survey_date
      const setUser = jest.fn();
      const user = { next_nps_survey_date: '2025-05-11T00:00:00.000Z' };
      useAuthContext.mockReturnValue({
        user,
        setUser,
      });

      // Mock successful response with future next_nps_survey_date
      const futureDate = '2025-06-12T00:00:00.000Z';
      sendUserNPS.mockResolvedValueOnce({ next_nps_survey_date: futureDate });

      const { rerender } = render(<NPS />, { wrapper: createWrapper() });

      // Verify NPS is initially displayed
      expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();

      // Select rating and submit
      const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
      fireEvent.click(ratingCircle);
      const submitButton = screen.getByRole('button', { name: /enviar/i });
      fireEvent.click(submitButton);

      // Wait for submission and verify success message
      await waitFor(() => {
        expect(screen.getByText('Avaliação enviada com sucesso!')).toBeInTheDocument();
      });

      // Verify setUser was called with updated next_nps_survey_date
      expect(setUser).toHaveBeenCalledWith({
        ...user,
        next_nps_survey_date: futureDate,
      });

      // Close success message
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      // Update auth context with new user data
      useAuthContext.mockReturnValue({
        user: { next_nps_survey_date: futureDate },
        setUser,
      });

      // Rerender component to simulate a new render after state updates
      rerender(<NPS />);

      // Verify NPS is not displayed
      expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
    });

    it('should not reappear after skipping when next_nps_survey_date is updated', async () => {
      // Set up mock user and next_nps_survey_date
      const setUser = jest.fn();
      const user = { next_nps_survey_date: '2025-05-11T00:00:00.000Z' };
      useAuthContext.mockReturnValue({
        user,
        setUser,
      });

      // Mock successful response with future next_nps_survey_date
      const futureDate = '2025-06-12T00:00:00.000Z';
      skipNPS.mockResolvedValueOnce({ next_nps_survey_date: futureDate });

      const { rerender } = render(<NPS />, { wrapper: createWrapper() });

      // Verify NPS is initially displayed
      expect(screen.getByText('Como você avalia nosso serviço?')).toBeInTheDocument();

      // Close without submitting
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      // Wait for skipNPS to complete
      await waitFor(() => {
        expect(skipNPS).toHaveBeenCalled();
      });

      // Verify setUser was called with updated date
      await waitFor(() => {
        expect(setUser).toHaveBeenCalledWith({
          ...user,
          next_nps_survey_date: futureDate,
        });
      });

      // Update auth context with new user data
      useAuthContext.mockReturnValue({
        user: { next_nps_survey_date: futureDate },
        setUser,
      });

      // Rerender component to simulate a new render after state updates
      rerender(<NPS />);

      // Verify NPS is not displayed
      expect(screen.queryByText('Como você avalia nosso serviço?')).not.toBeInTheDocument();
    });

    it('should properly update user context with next_nps_survey_date after successful submission', async () => {
      // Setup mock
      const setUser = jest.fn();
      const initialUser = { id: 1, next_nps_survey_date: '2025-05-11T00:00:00.000Z' };
      useAuthContext.mockReturnValue({
        user: initialUser,
        setUser,
      });

      // Mock response with future date
      const futureDate = '2025-06-12T00:00:00.000Z';
      sendUserNPS.mockResolvedValueOnce({ next_nps_survey_date: futureDate });

      render(<NPS />, { wrapper: createWrapper() });

      // Select rating and submit
      const ratingCircle = screen.getByTestId('SentimentVerySatisfiedIcon').closest('div');
      fireEvent.click(ratingCircle);
      const submitButton = screen.getByRole('button', { name: /enviar/i });
      fireEvent.click(submitButton);

      // Verify setUser was called with all previous user data plus updated next_nps_survey_date
      await waitFor(() => {
        expect(setUser).toHaveBeenCalledWith({
          ...initialUser,
          next_nps_survey_date: futureDate,
        });
      });
    });
  });
});
