import PropTypes from 'prop-types';
import { useEffect } from 'react';
// form
import { Controller, useFormContext } from 'react-hook-form';
// @mui
import { InputAdornment, TextField } from '@mui/material';
import { CurrencyInput } from 'react-currency-mask';

RHFPercentageTextField.propTypes = {
  name: PropTypes.string,
  helperText: PropTypes.node,
  disabled: PropTypes.bool,
};

export default function RHFPercentageTextField({ name, helperText, disabled = false, ...other }) {
  const { control, setValue } = useFormContext();

  useEffect(() => {
    if(other?.defaultValue){
      setValue(name, other?.defaultValue)
    }
  }, [setValue, name, other?.defaultValue])

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <CurrencyInput
          {...other}
          hideSymbol
          value={field.value || '0.00'}
          onChangeValue={(_, value) => {
            field.onChange(value || '0.00');
          }}
          InputElement={
            <TextField
              error={!!error}
              helperText={error ? error?.message : helperText}
              InputProps={{
                endAdornment: <InputAdornment position="end">%</InputAdornment>,
              }}
              disabled={disabled}
            />
          }
        />
      )}
    />
  );
}
