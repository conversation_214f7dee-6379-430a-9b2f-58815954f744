import { ToggleButtonGroup } from '@mui/material';
import PropTypes from 'prop-types';
import { Controller, useFormContext } from 'react-hook-form';

RHFToggleButtonGroup.propTypes = {
  name: PropTypes.string,
  helperText: PropTypes.node,
  children: PropTypes.node,
};

export default function RHFToggleButtonGroup({ name, helperText, children, ...other }) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <ToggleButtonGroup
          {...field}
          fullWidth
          onChange={(event, value) => {
            field.onChange(value || field.value);
          }}
          {...other}
        >
          {children}
        </ToggleButtonGroup>
      )}
    />
  );
}
