import {
  Box,
  Card,
  FormHelperText,
  ListItem,
  ListItemText,
  Paper,
  Popover,
  Typography,
} from '@mui/material';
import { rgbToHex, useTheme } from '@mui/system';
import { Chrome } from '@uiw/react-color';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

RHFColorPicker.propTypes = {
  name: PropTypes.string,
  label: PropTypes.string,
  helperText: PropTypes.node,
  onChange: PropTypes.func,
  defaultValue: PropTypes.string,
};

export default function RHFColorPicker({ name, label, helperText, ...props }) {
  const { control } = useFormContext();
  const [anchor, setAnchor] = useState(null);
  const theme = useTheme();
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={props.defaultValue || '#000000'}
      render={({ field, fieldState: { error } }) => (
        <>
          <Paper sx={{ width: 1 }} {...props}>
            <ListItem sx={{ px: 2, pr: 1.5, py: 0.8 }}>
              <ListItemText
                primary={
                  <ListItemText
                    primary={
                      <Typography variant="caption" color="text.secondary">
                        {label}
                      </Typography>
                    }
                  />
                }
              />
              <Box
                onClick={(event) => {
                  setAnchor(event.currentTarget);
                }}
                width={25}
                height={25}
                borderRadius={1}
                boxShadow={18}
                sx={{
                  background: field.value,
                  cursor: 'pointer',
                }}
              />
            </ListItem>
          </Paper>
          {(!!error || helperText) && (
            <FormHelperText error={!!error} sx={{ mt: 0 }}>
              {error ? error?.message : helperText}
            </FormHelperText>
          )}
          <Popover
            open={!!anchor}
            anchorEl={anchor}
            onClose={(e) => {
              setAnchor(null);
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            onKeyUp={(event) => {
              if (event.key === 'Enter') {
                setAnchor(null);
              }
            }}
          >
            <Card>
              <Chrome
                style={{
                  background: theme.palette.background.paper,
                  color: theme.palette.text.primary,
                }}
                color={field.value}
                onChange={({ rgba: { r, g, b, a } }) => {
                  if (props?.onChange) {
                    props.onChange(rgbToHex(`rgba(${r}, ${g}, ${b}, ${a})`));
                  }
                  field.onChange(rgbToHex(`rgba(${r}, ${g}, ${b}, ${a})`));
                }}
              />
            </Card>
          </Popover>
        </>
      )}
    />
  );
}
