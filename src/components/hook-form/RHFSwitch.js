import PropTypes from 'prop-types';
// form
import { Controller, useFormContext } from 'react-hook-form';
// @mui
import {
  Box,
  FormControlLabel,
  FormHelperText,
  IconButton,
  Switch,
  Tooltip,
} from '@mui/material';
import Iconify from '../iconify';

// ----------------------------------------------------------------------

RHFSwitch.propTypes = {
  name: PropTypes.string.isRequired,
  helperText: PropTypes.node,
  onChange: PropTypes.func,
  tooltip: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  disabled: PropTypes.bool,
};

export default function RHFSwitch({
  name,
  helperText,
  onChange,
  tooltip,
  disabled = false,
  ...other
}) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={other.defaultValue}
      render={({ field, fieldState: { error } }) => {
        const switchComponent = (
          <Switch
            {...field}
            checked={field.value}
            onChange={(e) => {
              field.onChange(e);
              if (onChange) {
                onChange(e);
              }
            }}
          />
        );

        return (
          <div>
            <Box display="flex" alignItems="center" gap={0.5}>
              <FormControlLabel
                control={switchComponent}
                {...other}
              />

              {tooltip && (
                <Tooltip
                  title={tooltip}
                  arrow
                  enterDelay={200}
                  leaveDelay={200}
                  disableInteractive={false}
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 4],
                        },
                      },
                    ],
                  }}
                >
                  <IconButton size="small" sx={{ ml: -2 }}>
                    <Iconify
                      icon="eva:question-mark-circle-outline"
                      width={18}
                      height={18}
                    />
                  </IconButton>
                </Tooltip>
              )}
            </Box>

            {(!!error || helperText) && (
              <FormHelperText error={!!error}>
                {error ? error?.message : helperText}
              </FormHelperText>
            )}
          </div>
        );
      }}
    />
  );
}