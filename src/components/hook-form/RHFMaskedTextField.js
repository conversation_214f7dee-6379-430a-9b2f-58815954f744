import { useEffect, useRef } from 'react';
import { TextField } from '@mui/material';
import PropTypes from 'prop-types';
import { Controller, useFormContext } from 'react-hook-form';
import InputMask from 'react-input-mask';

RHFMaskedTextField.propTypes = {
  name: PropTypes.string,
  helperText: PropTypes.node,
  mask: PropTypes.any,
};

export default function RHFMaskedTextField({ name, helperText, mask, ...other }) {
  const { control } = useFormContext();

  const inputRef = useRef();

  useEffect(() => {
    if (other?.focus) {
      inputRef.current.focus();
    }
  }, [other?.focus]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <InputMask mask={mask} value={field.value} onChange={field.onChange} {...other}>
          {(inputProps) => (
            <TextField
              {...other}
              {...inputProps}
              inputRef={inputRef}
              fullWidth
              error={!!error}
              helperText={error ? error?.message : helperText}
            />
          )}
        </InputMask>
      )}
    />
  );
}
