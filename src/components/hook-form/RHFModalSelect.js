import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, FormHelperText, Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useFormContext } from 'react-hook-form';
import ConfirmDialog from '../confirm-dialog/ConfirmDialog';

ModalSelect.propTypes = {
  name: PropTypes.string,
  placeholder: PropTypes.string,
  label: PropTypes.string,
  modalTitle: PropTypes.string,
  options: PropTypes.array,
  selectedOptionsOrder: PropTypes.array,
  defaultValues: PropTypes.array,
  onChange: PropTypes.func,
};

export default function ModalSelect({
  name,
  placeholder,
  label,
  modalTitle,
  options,
  selectedOptionsOrder = [],
  defaultValues,
  onChange,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [isModalError, setIsModalError] = useState(false);

  const theme = useTheme();

  const form = useFormContext();

  const error = form.formState.errors[name];

  const isLight = theme.palette.mode === 'light';

  const onOpenModal = () => setIsModalOpen(true);

  const onCloseModal = () => {
    setIsModalOpen(false);
    const values = form.getValues(name);

    setSelectedOptions(() =>
      values.map((value) => ({
        value: options.find((option) => option.value === value)?.value,
        label: options.find((option) => option.value === value)?.label,
      }))
    );
  };

  const onChangePayment = (option) => {
    if (onChange) {
      onChange(option);
    }

    setIsModalError(false);

    setSelectedOptions((state) => {
      const hasOption = state.find(({ value }) => value === option.value);

      if (hasOption) {
        const result = state.filter(({ value }) => value !== option.value);
        return result;
      }

      const result = [...state, option];

      return result;
    });
  };

  const onSave = () => {
    if (!selectedOptions.length) {
      setIsModalError(true);

      return;
    }

    form.setValue(
      name,
      selectedOptions.map(({ value }) => value)
    );

    setIsModalOpen(false);
  };

  const getBorder = () => {
    if (error?.message) return 'border-red-500';

    if (isLight) return 'border-gray-300 hover:border-[#637381]';

    return 'border-[#637381] hover:border-gray-300';
  };

  useEffect(() => {
    if (defaultValues) {
      form.setValue(name, defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValues]);

  const selected = form.getValues(name);

  useEffect(() => {
    const defaultSelectedOptins = options.filter(({ value }) => selected.includes(value));
    setSelectedOptions(defaultSelectedOptins);
  }, [selected, options]);

  return (
    <>
      <button type="button" className="relative text-start w-full" onClick={onOpenModal}>
        {label && (
          <div
            style={{
              fontFamily: 'Public Sans, sans-serif',
              fontSize: '12px',
              fontWeight: 400,
            }}
            className={`absolute -top-2.5 left-2 ${
              isLight ? 'bg-[#ffffff]' : 'bg-[#212B36]'
            } px-1  ${error?.message ? 'text-red-500' : 'text-[#637381]'}`}
          >
            {label}
          </div>
        )}
        <div
          className={`
            flex items-center justify-between p-2 border ${getBorder()}  rounded-lg cursor-pointer focus-within:ring-2 focus-within:ring-blue-500
        `}
        >
          {selectedOptions?.length ? (
            <div className="flex flex-row">
              {selectedOptions
                .sort(
                  (a, b) =>
                    selectedOptionsOrder.indexOf(a.value) - selectedOptionsOrder.indexOf(b.value)
                )
                .map((option, index) => (
                  <>
                    <div
                      className="rounded-md outline-none  hover:shadow-lg font-medium text-sm shadow-md px-2"
                      key={option.value}
                    >
                      {option.label}
                    </div>

                    {index < selectedOptions.length - 1 && (
                      <span className="mt-1 mr-2 ml-1">,</span>
                    )}
                  </>
                ))}
            </div>
          ) : (
            <div className={`flex-1  ${isLight ? 'text-black' : 'text-white'}`}>
              {placeholder || 'Selecione uma opção'}
            </div>
          )}
          <svg
            className="w-5 h-5 text-gray-500 transform"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.707a1 1 0 011.414 0L10 11.586l3.293-3.879a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        {error && <FormHelperText error>{error.message}</FormHelperText>}
      </button>

      <ConfirmDialog
        title={modalTitle}
        open={isModalOpen}
        onClose={onCloseModal}
        maxWidth="md"
        content={
          <div className="flex flex-col gap-4">
            <div className="flex sm:flex-row flex-col gap-4">
              {options?.map((option) => (
                <button
                  className={`p-5 rounded-md outline-none flex-1 hover:shadow-lg font-medium text-sm shadow-md ${
                    isLight
                      ? `${
                          selectedOptions.find(({ value }) => value === option.value)
                            ? 'border-gray-600'
                            : 'border-gray-400'
                        } hover:border-gray-600`
                      : ` ${
                          selectedOptions.find(({ value }) => value === option.value)
                            ? 'border-gray-100'
                            : 'border-gray-600'
                        }
                         hover:border-gray-100
                    `
                  } border-2 text-gray-500`}
                  key={option.value}
                  type="button"
                  onClick={() => onChangePayment(option)}
                >
                  {option.label}
                </button>
              ))}
            </div>

            {isModalError && <Alert severity="error">Selecione ao menos uma opção</Alert>}
          </div>
        }
        action={
          <Button onClick={onSave} variant="contained">
            Salvar
          </Button>
        }
      />
    </>
  );
}
