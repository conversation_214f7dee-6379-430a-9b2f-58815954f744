import { useEffect, useRef } from 'react';

import PropTypes from 'prop-types';

// form
import { Controller, useFormContext } from 'react-hook-form';

// @mui
import { TextField } from '@mui/material';

// ----------------------------------------------------------------------

RHFTextField.propTypes = {
  name: PropTypes.string,
  reset: PropTypes.bool,
  helperText: PropTypes.node,
};

export default function RHFTextField({ name, helperText, reset, ...other }) {
  const { control, setValue } = useFormContext();

  const inputRef = useRef();

  useEffect(() => {
    if (other?.defaultValue) {
      setValue(name, other?.defaultValue);
    }
  }, [setValue, name, other?.defaultValue]);

  useEffect(() => {
    if (other?.focus) {
      inputRef.current.focus();
    }
  }, [other?.focus]);

  useEffect(() => {
    if (reset) {
      setValue(name, '');
    }
  }, [name, reset, setValue]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          name={field.name}
          inputRef={inputRef}
          fullWidth
          value={typeof field.value === 'number' && field.value === 0 ? '' : field.value || ''}
          error={!!error}
          helperText={error ? error?.message : helperText}
          {...other}
          onChange={(e) => {
            if (other?.onChange) {
              other.onChange(e);
            }

            field.onChange(e);
          }}
        />
      )}
    />
  );
}
