import { TextField } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import PropTypes from 'prop-types';
import { Controller, useFormContext } from 'react-hook-form';

const RHFDateTimePicker = ({ name, helperText, ...other }) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={other?.defaultValue}
      render={({ field, fieldState: { error } }) => (
        <DateTimePicker
          {...field}
          inputFormat="dd/MM/yyyy HH:mm"
          renderInput={(params) => (
            <TextField
              fullWidth
              {...params}
              error={!!error}
              helperText={error?.message || helperText}
              {...other}
            />
          )}
        />
      )}
    />
  );
};

RHFDateTimePicker.propTypes = {
  name: PropTypes.string.isRequired,
  helperText: PropTypes.node,
};

export default RHFDateTimePicker;
