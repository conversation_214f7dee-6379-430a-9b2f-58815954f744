import { Icon<PERSON>utton, MenuItem, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import * as datefns from 'date-fns';
import { ptBR } from 'date-fns/locale';
import PropTypes from 'prop-types';
import { useMemo, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { useDateRangePicker } from '../date-range-picker';
import DateRangePicker from '../date-range-picker/DateRangePicker';
import Iconify from '../iconify';
import { RHFSelect } from './RHFSelect';

datefns.setDefaultOptions({
  locale: ptBR,
});

export const PERIODS = {
  TODAY: 'today',
  YESTERDAY: 'yesterday',
  LAST_WEEK: 'last_week',
  LAST_MONTH: 'last_month',
  CUSTOM: 'custom',
  ALL_TIME: 'all_time',
};

const today = new Date();

const yesterday = datefns.subDays(today, 1);

const lastWeek = datefns.subDays(today, 7);

const lastMonth = datefns.subMonths(today, 1);

const RHFDateRangePicker = ({ label, initialPeriod, ...rest }) => {
  const form = useFormContext();

  const {
    startDate: pickerStartDate,
    endDate: pickerEndDate,
    onChangeStartDate: onChangePickerStartDate,
    onChangeEndDate: onChangePickerEndDate,
    open: openPicker,
    onOpen: onOpenPicker,
    onClose: onClosePicker,
    isSelected: isSelectedValuePicker,
    isError,
  } = useDateRangePicker(new Date(), new Date());

  const handlePeriodChange = (newPeriod) => {
    switch (newPeriod) {
      case PERIODS.TODAY:
        form.setValue('startDate', today);
        form.setValue('endDate', today);
        break;
      case PERIODS.YESTERDAY:
        form.setValue('startDate', yesterday);
        form.setValue('endDate', yesterday);
        break;
      case PERIODS.LAST_WEEK:
        form.setValue('startDate', lastWeek);
        form.setValue('endDate', today);
        break;
      case PERIODS.LAST_MONTH:
        form.setValue('startDate', lastMonth);
        form.setValue('endDate', today);
        break;
      case PERIODS.ALL_TIME:
        form.setValue('startDate', null);
        form.setValue('endDate', null);
        break;
      case PERIODS.CUSTOM:
        form.setValue('startDate', pickerStartDate);
        form.setValue('endDate', pickerEndDate);
        break;
      default:
        break;
    }
    form.setValue('period', newPeriod);
  };

  const startDate = form?.getValues('startDate');

  const endDate = form?.getValues('endDate');

  const shortLabelDate = useMemo(() => {
    if (!startDate || !endDate) {
      return '-';
    }
    const isCurrentYear =
      datefns.isSameYear(startDate, endDate) &&
      datefns.getYear(startDate) === datefns.getYear(new Date());
    if (!isCurrentYear) {
      return `${datefns.format(endDate, 'dd MMM yy')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    if (!datefns.isSameMonth(startDate, endDate)) {
      return `${datefns.format(startDate, 'dd MMM')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    if (!datefns.isSameDay(startDate, endDate)) {
      return `${datefns.format(startDate, 'dd')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    return datefns.format(endDate, 'dd MMM yy');
  }, [startDate, endDate]);

  const customRef = useRef();

  return (
    <>
      <RHFSelect
        name="period"
        value={form.watch('period') || initialPeriod || PERIODS.LAST_WEEK}
        label={label}
        size="small"
        onChange={(event) => handlePeriodChange(event.target.value)}
        maxHeight={250}
        {...rest}
      >
        <MenuItem value={PERIODS.TODAY}>Hoje</MenuItem>
        <MenuItem value={PERIODS.YESTERDAY}>Ontem</MenuItem>
        <MenuItem value={PERIODS.LAST_WEEK}>Últimos 7 dias</MenuItem>
        <MenuItem value={PERIODS.LAST_MONTH}>Últimos 30 dias</MenuItem>
        <MenuItem value={PERIODS.ALL_TIME}>Sempre</MenuItem>
        <MenuItem
          ref={customRef}
          value={PERIODS.CUSTOM}
          sx={{
            maxHeight: 34,
          }}
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            width={1}
            gap={1}
          >
            <Typography variant="body2">{shortLabelDate}</Typography>
            <IconButton
              onClick={(event) => {
                event.stopPropagation();
                onOpenPicker();
              }}
              size="small"
            >
              <Iconify icon="eva:calendar-fill" />
            </IconButton>
          </Stack>
        </MenuItem>
      </RHFSelect>
      <DateRangePicker
        title="Selecione o período"
        variant="calendar"
        startDate={pickerStartDate}
        endDate={pickerEndDate}
        onConfirm={() => {
          form.setValue('startDate', pickerStartDate);
          form.setValue('endDate', pickerEndDate);
          customRef.current.focus();
          customRef.current.click();
        }}
        onChangeStartDate={onChangePickerStartDate}
        onChangeEndDate={onChangePickerEndDate}
        open={openPicker}
        onClose={() => {
          onClosePicker();
        }}
        isSelected={isSelectedValuePicker}
        isError={isError}
      />
    </>
  );
};

RHFDateRangePicker.propTypes = {
  name: PropTypes.string,
  label: PropTypes.string,
  initialPeriod: PropTypes.string,
};

export default RHFDateRangePicker;
