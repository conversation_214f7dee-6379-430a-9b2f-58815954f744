import { Badge, Button, InputAdornment, Stack, TextField } from '@mui/material';
import PropTypes from 'prop-types';
import Iconify from '../iconify/Iconify';

const TableActionsRenderer = ({ Actions, searchable, filterable, table, onSearch }) => (
  <Stack
    p={2}
    mb={1}
    direction="row"
    flexWrap="wrap"
    alignItems="center"
    justifyContent="space-between"
    gap={1}
  >
    {searchable && (
      <TextField
        sx={{
          flex: {
            xs: 1,
            sm: 'unset',
          },
        }}
        size="small"
        placeholder="Pesquisar"
        onChange={(event) => {
          table.onChangePage(1);
          table.page = 1;
          onSearch(event.target.value);
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="ion:search-outline" />
            </InputAdornment>
          ),
        }}
      />
    )}
    <Stack
      width={{
        xs: 1,
        sm: 'unset',
      }}
      direction={{
        xs: 'column',
        sm: 'row',
      }}
      alignItems={{
        xs: 'stretch',
        sm: 'center',
      }}
      gap={1}
      justifyContent={{
        xs: 'flex-start',
        sm: 'flex-end',
      }}
      sx={{
        '& > *': {
          flex: {
            xs: 1,
            sm: 'unset',
          },
        },
      }}
    >
      {Actions}
      {filterable && (
        <Badge badgeContent={table.dirtyFilters} color="primary">
          <Button
            fullWidth
            variant="outlined"
            onClick={() => table.setShowFilter(true)}
            startIcon={<Iconify icon="eva:options-2-outline" />}
            color="inherit"
          >
            Filtros
          </Button>
        </Badge>
      )}
    </Stack>
  </Stack>
);

TableActionsRenderer.propTypes = {
  Actions: PropTypes.oneOfType([PropTypes.element, PropTypes.arrayOf(PropTypes.element)]),
  searchable: PropTypes.bool,
  filterable: PropTypes.bool,
  table: PropTypes.object,
  onSearch: PropTypes.func,
};

export default TableActionsRenderer;
