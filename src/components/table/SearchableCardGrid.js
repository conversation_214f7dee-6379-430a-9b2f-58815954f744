import { Card, Checkbox, Grid, LinearProgress, Pagination, Stack, Typography } from '@mui/material';
import { Box } from '@mui/system';
import PropTypes from 'prop-types';
import { useMemo } from 'react';
import TableActionsRenderer from './TableActionsRenderer';
import TableSelectedAction from './TableSelectedAction';

const SearchableCardGrid = ({
  table,
  count,
  rows: rawRows,
  labels,
  fetching,
  infiniteLoading = false,
  selectable = false,
  searchable = true,
  filterable = false,
  pageable = true,
  scrollable = true,
  inMemoryFilter = null,
  renderCard,
  onCardClick,
  Actions,
  gridConfig = { base: 12 },
  ...props
}) => {
  const rows = useMemo(() => rawRows || [], [rawRows]);

  const filtered = useMemo(() => {
    if (inMemoryFilter) {
      return rows.filter(inMemoryFilter);
    }
    if (fetching || !rows) {
      return [];
    }
    return rows;
  }, [rows, inMemoryFilter, fetching]);

  return (
    <Card {...props}>
      {(searchable || filterable || Actions) && (
        <TableActionsRenderer
          searchable={searchable}
          filterable={filterable}
          table={table}
          onSearch={(value) => table.setSearch(value)}
          Actions={Actions}
        />
      )}
      <div style={{ position: 'relative' }}>
        {fetching && <LinearProgress />}
        {selectable && (
          <Box px={2} pb={2} borderRadius={1}>
            <TableSelectedAction
              dense={table.dense}
              numSelected={table.selected.length}
              rowCount={rows.length}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  rows.map((row) => row.id)
                )
              }
              sx={{
                position: 'relative',
                borderRadius: 2,
              }}
            />
          </Box>
        )}

        <Grid container spacing={2} px={2} pb={2}>
          {filtered.map((row, index) => (
            <Grid item key={row.id} {...gridConfig}>
              <Card
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  bgcolor: 'background.paper',
                  ...(onCardClick && {
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }),
                }}
                variant="outlined"
                onClick={() => onCardClick && onCardClick(row)}
              >
                {selectable && (
                  <Box sx={{ mr: -2, px: 1 }}>
                    <Checkbox
                      checked={table.selected.includes(row.id)}
                      onClick={(event) => {
                        event.stopPropagation();
                        table.onSelectRow(row.id);
                      }}
                    />
                  </Box>
                )}
                <Box sx={{ flex: 1 }}>{renderCard(row, index)}</Box>
              </Card>
            </Grid>
          ))}
        </Grid>
        {rows.length === 0 && (
          <Stack width={1} alignItems="center" justifyContent="center" p={2}>
            <Typography variant="caption" color="text.secondary">
              {fetching ? 'Carregando...' : 'Nenhum registro encontrado'}
            </Typography>
          </Stack>
        )}
        {infiniteLoading && !fetching && (
          <Stack width={1} alignItems="center" justifyContent="center" p={2}>
            <Typography variant="caption" color="text.secondary">
              Carregando...
            </Typography>
          </Stack>
        )}
      </div>
      {count > 0 && pageable && (
        <Grid
          paddingX={2}
          paddingY={2}
          alignItems="center"
          justifyContent="center"
          container
          marginTop={1}
        >
          <Pagination
            count={Math.ceil(count / table.rowsPerPage)}
            page={+table.page || 1}
            onChange={table.onChangePage}
          />
        </Grid>
      )}
    </Card>
  );
};

SearchableCardGrid.propTypes = {
  fetching: PropTypes.bool,
  labels: PropTypes.arrayOf(PropTypes.shape({ id: PropTypes.string, label: PropTypes.string })),
  Actions: PropTypes.oneOfType([PropTypes.element, PropTypes.arrayOf(PropTypes.element)]),
  count: PropTypes.number,
  rows: PropTypes.array,
  infiniteLoading: PropTypes.bool,
  selectable: PropTypes.bool,
  searchable: PropTypes.bool,
  filterable: PropTypes.bool,
  pageable: PropTypes.bool,
  scrollable: PropTypes.bool,
  inMemoryFilter: PropTypes.func,
  renderCard: PropTypes.func.isRequired,
  onCardClick: PropTypes.func,
  gridConfig: PropTypes.object,
  table: PropTypes.object.isRequired,
};

export default SearchableCardGrid;
