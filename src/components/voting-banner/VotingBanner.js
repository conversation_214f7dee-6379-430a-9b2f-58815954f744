import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import { Box, Button, IconButton, Stack, Typography, useTheme } from '@mui/material';

import Iconify from '../iconify';
import Image from '../image';

const STORAGE_KEY = 'voting-banner-closed';

VotingBanner.propTypes = {
  sx: PropTypes.object,
};

export default function VotingBanner({ sx, ...other }) {
  const theme = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const isClosed = sessionStorage.getItem(STORAGE_KEY) === 'true';
    if (!isClosed) {
      setIsVisible(true);
    }
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    sessionStorage.setItem(STORAGE_KEY, 'true');
  };

  const handleVoteClick = () => {
    window.open('https://www.reclameaqui.com.br/premio/votacao/empresa/cakto-pay/', '_blank');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        mb: 2,
        ...sx,
      }}
      {...other}
    >
      <Box
        sx={{
          backgroundColor: theme.palette.background.paper,
          borderRadius: 2,
          boxShadow: theme.shadows[1],
          border: `1px solid ${theme.palette.divider}`,
          overflow: 'hidden',
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            px: { xs: 2, sm: 3 },
            py: { xs: 1.5, sm: 2 },
            minHeight: { xs: 'auto', sm: 80 },
            display: { xs: 'none', sm: 'flex' },
          }}
        >
          <Stack direction="row" alignItems="center" spacing={3} sx={{ flex: 1, minWidth: 0 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: 1.5,
                overflow: 'hidden',
                flexShrink: 0,
                backgroundColor: "#a9dd6a",

              }}
            >
              <Image
                src="https://www.reclameaqui.com.br/remote/premio-timeline/prd/images/ervilho.svg"
                alt="Prêmio Reclame Aqui 2025"
                disabledEffect
                sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
              />
            </Box>

            <Stack spacing={1} sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant="h6"
                sx={{
                  color: 'text.primary',
                  fontWeight: 600,
                  fontSize: '1.125rem',
                  lineHeight: 1.2,
                }}
              >
                Já deixou seu voto? Cakto no Prêmio Reclame Aqui 2025 🏆
              </Typography>

              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  fontSize: '0.875rem',
                  lineHeight: 1.4,
                }}
              >
                Fomos indicados como um dos melhores meios de pagamento eletrônico do Brasil. Agora,
                precisamos do seu apoio para conquistar esse troféu.
              </Typography>
            </Stack>
          </Stack>

          <Button
            variant="contained"
            onClick={handleVoteClick}
            startIcon={<Iconify icon="eva:external-link-fill" />}
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              fontWeight: 600,
              px: 3,
              py: 1.5,
              borderRadius: 1.5,
              textTransform: 'none',
              fontSize: '0.875rem',
              minWidth: 160,
              '&:hover': {
                backgroundColor: theme.palette.primary.dark,
              },
              flexShrink: 0,
            }}
          >
            Votar na Cakto
          </Button>

          <IconButton
            onClick={handleClose}
            aria-label="Fechar banner"
            sx={{
              color: 'text.secondary',
              ml: 1,
              flexShrink: 0,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>

        <Stack
          sx={{
            px: 2,
            py: 2,
            display: { xs: 'block', sm: 'none' },
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  overflow: 'hidden',
                  flexShrink: 0,
                  backgroundColor: "#a9dd6a",
                  padding: 0.5,
                }}
              >
                <Image
                  src="https://www.reclameaqui.com.br/remote/premio-timeline/prd/images/ervilho.svg"
                  alt="Prêmio Reclame Aqui 2025"
                  disabledEffect
                  sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
                />
              </Box>

              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.primary',
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  lineHeight: 1.2,
                  flex: 1,
                }}
              >
                Já deixou seu voto? Cakto no Prêmio Reclame Aqui 2025 🏆
              </Typography>
            </Stack>

            <IconButton
              onClick={handleClose}
              size="small"
              aria-label="Fechar banner"
              sx={{
                color: 'text.secondary',
                flexShrink: 0,
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <Iconify icon="eva:close-fill" />
            </IconButton>
          </Stack>

          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '0.75rem',
              lineHeight: 1.4,
              mb: 2,
            }}
          >
            Fomos indicados como um dos melhores meios de pagamento eletrônico do Brasil. Agora,
            precisamos do seu apoio para conquistar esse troféu.
          </Typography>

          <Button
            variant="contained"
            onClick={handleVoteClick}
            startIcon={<Iconify icon="eva:external-link-fill" />}
            fullWidth
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              fontWeight: 600,
              py: 1.5,
              borderRadius: 1.5,
              textTransform: 'none',
              fontSize: '0.875rem',
              '&:hover': {
                backgroundColor: theme.palette.primary.dark,
              },
            }}
          >
            Votar na Cakto
          </Button>
        </Stack>
      </Box>
    </Box>
  );
}
