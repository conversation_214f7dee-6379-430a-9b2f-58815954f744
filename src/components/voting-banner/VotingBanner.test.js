import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import VotingBanner from './VotingBanner';

// Mock do sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

const theme = createTheme();

const renderWithTheme = (component) => {
  return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
};

describe('VotingBanner', () => {
  beforeEach(() => {
    sessionStorageMock.getItem.mockClear();
    sessionStorageMock.setItem.mockClear();
  });

  it('should render banner when not closed', () => {
    sessionStorageMock.getItem.mockReturnValue('false');
    renderWithTheme(<VotingBanner />);

    expect(screen.getAllByText('Já deixou seu voto? Cakto no Prêmio Reclame Aqui 2025 🏆')).toHaveLength(2);
    expect(screen.getAllByText('Votar na Cakto')).toHaveLength(2);
  });

  it('should not render banner when closed', () => {
    sessionStorageMock.getItem.mockReturnValue('true');
    renderWithTheme(<VotingBanner />);

    expect(screen.queryByText('Já deixou seu voto? Cakto no Prêmio Reclame Aqui 2025 🏆')).not.toBeInTheDocument();
  });

  it('should close banner when close button is clicked', () => {
    sessionStorageMock.getItem.mockReturnValue('false');
    renderWithTheme(<VotingBanner />);

    const closeButtons = screen.getAllByRole('button', { name: /fechar/i });
    fireEvent.click(closeButtons[0]); // Click the first close button (desktop version)

    expect(sessionStorageMock.setItem).toHaveBeenCalledWith('voting-banner-closed', 'true');
  });

  it('should open voting link in new tab', () => {
    sessionStorageMock.getItem.mockReturnValue('false');
    const mockOpen = jest.fn();
    Object.defineProperty(window, 'open', {
      value: mockOpen,
    });

    renderWithTheme(<VotingBanner />);

    const voteButtons = screen.getAllByText('Votar na Cakto');
    fireEvent.click(voteButtons[0]); // Click the first button (desktop version)

    expect(mockOpen).toHaveBeenCalledWith(
      'https://www.reclameaqui.com.br/premio/votacao/empresa/cakto-pay/',
      '_blank'
    );
  });
});
