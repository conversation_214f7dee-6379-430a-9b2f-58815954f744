import { fetchAppEventHistory, resendEventHistory } from '@/services/apps';
import {
  Box,
  Button,
  Card,
  Grid,
  Modal,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { useState } from 'react';

export const WebhookTableModal = ({ open, onClose, id }) => {
  const { enqueueSnackbar } = useSnackbar();
  const queryClient = useQueryClient();
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const handleChangePage = (_, newPage) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  const { data: eventHistory = [], isFetching: isFetchingHistory } = useQuery({
    queryKey: ['app', 'eventHistory', id, pagination.page],
    queryFn: () => fetchAppEventHistory({ appId: id, page: pagination.page }),
    initialData: [],
  });

  const { mutate: resendEvent, isLoading: isResending } = useMutation({
    mutationFn: (eventHistoryId) => resendEventHistory({ eventHistoryId }),
    onSuccess: () => {
      enqueueSnackbar('Evento reenviado com sucesso!', { variant: 'success' });

      queryClient.invalidateQueries(['app', 'eventHistory', id]);
    },
    onError: (error) => {
      enqueueSnackbar(`Erro ao reenviar evento: ${error.message}`, { variant: 'error' });
    },
  });

  const handleRowClick = (event) => {
    setSelectedEvent(event);
  };

  const handleResend = () => {
    if (selectedEvent) {
      resendEvent(selectedEvent.id);
    }
  };

  const getStatusColor = (status) => {
    if (status >= 200 && status <= 299) {
      return '#197864';
    }
    if (!status || (status >= 400 && status <= 599)) {
      return '#EE324B';
    }
    return '#FFAB00';
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      BackdropProps={{
        'data-testid': 'modal-backdrop',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '90%',
          height: '80%',
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        <Box sx={{ flex: 1, marginRight: 2, overflowY: 'auto' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Logs
          </Typography>
          {isFetchingHistory ? (
            <Typography>Carregando...</Typography>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Descrição</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {eventHistory.results?.map((row) => (
                    <TableRow
                      key={row.id}
                      sx={{
                        cursor: 'pointer',
                        backgroundColor:
                          selectedEvent?.id === row.id ? 'background.neutral' : 'inherit', // Fundo cinza escuro para a linha selecionada
                        boxShadow:
                          selectedEvent?.id === row.id ? '0px 4px 10px rgba(0, 0, 0, 0.2)' : 'none', // Leve elevação
                        '&:hover': {
                          backgroundColor: 'background.neutral',
                        },
                        transition: 'all 0.3s ease',
                      }}
                      onClick={() => handleRowClick(row)}
                    >
                      <TableCell>
                        <Typography variant="subtitle2">{row?.event_name}</Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          {new Date(row.sentAt).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Card
                          sx={{
                            display: 'inline-block',
                            backgroundColor: getStatusColor(row.event_status),
                            color: 'white',
                            px: 2,
                            py: 1,
                          }}
                        >
                          <Typography variant="body2">{row.event_status || '-'}</Typography>
                        </Card>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {eventHistory.count > 0 && (
            <Grid
              paddingX={2}
              paddingY={2}
              alignItems="center"
              justifyContent="center"
              container
              marginTop={1}
            >
              <Pagination
                count={Math.ceil(eventHistory.count / pagination.rowsPerPage)}
                page={pagination.page}
                onChange={handleChangePage}
              />
            </Grid>
          )}
        </Box>
        <Box
          sx={{
            flex: 2,
            padding: 3,
            borderLeft: '1px solid',
            borderColor: 'divider',
            color: 'text.primary',
            overflowY: 'auto',
            overflowX: 'hidden',
            borderRadius: '8px',
            maxWidth: '100%',
            wordBreak: 'break-word',
          }}
        >
          {selectedEvent ? (
            <>
              <Typography variant="h6" sx={{ mb: 2, color: 'text.primary' }}>
                Detalhes
              </Typography>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                <strong>URL de destino:</strong> {selectedEvent?.url}
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Data de envio:</strong> {new Date(selectedEvent.sentAt).toLocaleString()}
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>App:</strong> {selectedEvent.app.name}
              </Typography>
              <Box
                sx={{
                  backgroundColor: 'background.neutral',
                  color: 'text.primary',
                  padding: 2,
                  borderRadius: 2,
                  marginBottom: 2,
                  boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.2)',
                }}
              >
                <Typography variant="body2" sx={{ fontWeight: 'bold', marginBottom: 1 }}>
                  Conteúdo Enviado:
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    color: (theme) => (theme.palette.mode === 'dark' ? 'grey.400' : 'grey.700'),
                  }}
                >
                  {JSON.stringify(selectedEvent.payload, null, 2)}
                </Typography>
              </Box>
              <Box
                sx={{
                  backgroundColor: 'background.neutral',
                  color: 'text.primary',
                  padding: 2,
                  borderRadius: 2,
                  marginBottom: 2,
                  boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.2)',
                }}
              >
                <Typography variant="body2" sx={{ fontWeight: 'bold', marginBottom: 1 }}>
                  Resposta do servidor de destino:
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    color: (theme) => (theme.palette.mode === 'dark' ? 'grey.400' : 'grey.700'),
                    wordBreak: 'break-word',
                  }}
                >
                  {selectedEvent.response?.error
                    ? selectedEvent.response.error.message
                    : JSON.stringify(selectedEvent.response, null, 2)}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  sx={{ boxShadow: '0 8px 16px 0 rgba(15, 120, 101, 0.24)' }}
                  variant="contained"
                  color="primary"
                  onClick={handleResend}
                  disabled={isResending}
                >
                  {isResending ? 'Reenviando...' : 'Reenviar'}
                </Button>
              </Box>
            </>
          ) : (
            <Typography variant="body2">Selecione um evento para ver os detalhes.</Typography>
          )}
        </Box>
      </Box>
    </Modal>
  );
};

WebhookTableModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  id: PropTypes.string,
};
