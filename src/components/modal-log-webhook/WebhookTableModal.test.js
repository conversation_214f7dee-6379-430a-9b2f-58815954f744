import { WebhookTableModal } from '@/components/modal-log-webhook/WebhookTableModal';
import { fetchAppEventHistory, resendEventHistory } from '@/services/apps';
import { createTheme } from '@mui/material/styles';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';

// Mock the services
jest.mock('@/services/apps', () => ({
  fetchAppEventHistory: jest.fn(),
  resendEventHistory: jest.fn(),
}));

// Mock data
const mockEventHistory = {
  results: [
    {
      id: '1',
      event_name: 'Success Event 200',
      sentAt: '2024-03-20T10:00:00Z',
      event_status: 200,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { status: 'success' },
    },
    {
      id: '2',
      event_name: 'Success Event 299',
      sentAt: '2024-03-20T10:01:00Z',
      event_status: 299,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { status: 'success' },
    },
    {
      id: '3',
      event_name: 'Error Event 400',
      sentAt: '2024-03-20T10:02:00Z',
      event_status: 400,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { error: { message: 'Bad Request' } },
    },
    {
      id: '4',
      event_name: 'Error Event 500',
      sentAt: '2024-03-20T10:03:00Z',
      event_status: 500,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { error: { message: 'Server Error' } },
    },
    {
      id: '5',
      event_name: 'Other Status 100',
      sentAt: '2024-03-20T10:04:00Z',
      event_status: 100,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { status: 'info' },
    },
    {
      id: '6',
      event_name: 'Other Status 300',
      sentAt: '2024-03-20T10:05:00Z',
      event_status: 300,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { status: 'redirect' },
    },
    // Add more items to enable pagination
    ...Array.from({ length: 5 }, (_, i) => ({
      id: `${i + 7}`,
      event_name: `Additional Event ${i + 1}`,
      sentAt: '2024-03-20T12:00:00Z',
      event_status: 200,
      url: 'https://test.com/webhook',
      app: { name: 'Test App' },
      payload: { test: 'data' },
      response: { status: 'success' },
    })),
  ],
  count: 11, // Total count of items
};

// Create a theme instance
const theme = createTheme();

// Setup function to render component with providers
const renderWithProviders = (component) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>);
};

describe('WebhookTableModal', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    fetchAppEventHistory.mockResolvedValue(mockEventHistory);
    resendEventHistory.mockResolvedValue({ success: true });
  });

  it('renders modal when open is true', () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    expect(screen.getByText('Logs')).toBeInTheDocument();
  });

  it('does not render modal when open is false', () => {
    renderWithProviders(<WebhookTableModal open={false} onClose={() => {}} id="test-id" />);

    expect(screen.queryByText('Logs')).not.toBeInTheDocument();
  });

  it('fetches and displays event history', async () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    // Initially shows loading state
    expect(screen.getByText('Carregando...')).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Success Event 200')).toBeInTheDocument();
      expect(screen.getByText('Success Event 299')).toBeInTheDocument();
      expect(screen.getByText('Error Event 400')).toBeInTheDocument();
      expect(screen.getByText('Error Event 500')).toBeInTheDocument();
      expect(screen.getByText('Other Status 100')).toBeInTheDocument();
      expect(screen.getByText('Other Status 300')).toBeInTheDocument();
    });

    // Verify fetch was called with correct parameters
    expect(fetchAppEventHistory).toHaveBeenCalledWith({
      appId: 'test-id',
      page: 1,
    });
  });

  it('displays event details when an event is selected', async () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Success Event 200')).toBeInTheDocument();
    });

    // Click on an event
    fireEvent.click(screen.getByText('Success Event 200'));

    // Check if details are displayed
    expect(screen.getByText('Detalhes')).toBeInTheDocument();
    expect(screen.getByText(/URL de destino:/)).toBeInTheDocument();
    expect(screen.getByText(/Data de envio:/)).toBeInTheDocument();
    expect(screen.getByText(/App:/)).toBeInTheDocument();
    expect(screen.getByText(/Conteúdo Enviado:/)).toBeInTheDocument();
  });

  it('handles event resend', async () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    // Wait for data to load and select an event
    await waitFor(() => {
      expect(screen.getByText('Success Event 200')).toBeInTheDocument();
    });
    fireEvent.click(screen.getByText('Success Event 200'));

    // Click resend button
    const resendButton = screen.getByText('Reenviar');
    fireEvent.click(resendButton);

    // Verify resend was called
    await waitFor(() => {
      expect(resendEventHistory).toHaveBeenCalledWith({ eventHistoryId: '1' });
    });
  });

  it('handles pagination', async () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    // Wait for initial data load
    await waitFor(() => {
      expect(fetchAppEventHistory).toHaveBeenCalledWith({
        appId: 'test-id',
        page: 1,
      });
    });

    // Click next page using the next icon button
    const nextPageButton = screen.getByTestId('NavigateNextIcon').closest('button');
    fireEvent.click(nextPageButton);

    // Verify fetch was called with new page number
    await waitFor(() => {
      expect(fetchAppEventHistory).toHaveBeenCalledWith({
        appId: 'test-id',
        page: 2,
      });
    });
  });

  it('displays correct status colors', async () => {
    renderWithProviders(<WebhookTableModal open={true} onClose={() => {}} id="test-id" />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Success Event 200')).toBeInTheDocument();
    });

    // Test success range (200-299)
    const success200Row = screen.getByText('Success Event 200').closest('tr');
    const success299Row = screen.getByText('Success Event 299').closest('tr');
    const success200Status = within(success200Row).getByText('200');
    const success299Status = within(success299Row).getByText('299');
    expect(success200Status.closest('div')).toHaveStyle({ backgroundColor: '#197864' });
    expect(success299Status.closest('div')).toHaveStyle({ backgroundColor: '#197864' });

    // Test error range (400-599)
    const error400Row = screen.getByText('Error Event 400').closest('tr');
    const error500Row = screen.getByText('Error Event 500').closest('tr');
    const error400Status = within(error400Row).getByText('400');
    const error500Status = within(error500Row).getByText('500');
    expect(error400Status.closest('div')).toHaveStyle({ backgroundColor: '#EE324B' });
    expect(error500Status.closest('div')).toHaveStyle({ backgroundColor: '#EE324B' });

    // Test other status codes
    const other100Row = screen.getByText('Other Status 100').closest('tr');
    const other300Row = screen.getByText('Other Status 300').closest('tr');
    const other100Status = within(other100Row).getByText('100');
    const other300Status = within(other300Row).getByText('300');
    expect(other100Status.closest('div')).toHaveStyle({ backgroundColor: '#FFAB00' });
    expect(other300Status.closest('div')).toHaveStyle({ backgroundColor: '#FFAB00' });
  });

  it('calls onClose when modal is closed', () => {
    const onClose = jest.fn();
    renderWithProviders(<WebhookTableModal open={true} onClose={onClose} id="test-id" />);

    // Find and click the backdrop
    const backdrop = screen.getByTestId('modal-backdrop');
    fireEvent.click(backdrop);

    expect(onClose).toHaveBeenCalled();
  });
});
