import PropTypes from 'prop-types';
import { useMemo } from 'react';

export function Pagination({ page, count, limit, handleChangePage }) {
  const lastPage = useMemo(() => Math.ceil(count / limit), [count, limit]);

  const getPaginationItems = useMemo(() => {
    const paginationItems = [];
    const maxVisiblePages = 5;
    const halfVisiblePages = Math.floor(maxVisiblePages / 2);

    let startPage = Math.max(page - halfVisiblePages, 1);
    let endPage = Math.min(page + halfVisiblePages, lastPage);

    if (endPage - startPage < maxVisiblePages - 1) {
      if (page <= halfVisiblePages) {
        endPage = Math.min(startPage + maxVisiblePages - 1, lastPage);
      } else if (page + halfVisiblePages >= lastPage) {
        startPage = Math.max(endPage - maxVisiblePages + 1, 1);
      }
    }

    if (startPage > 1) {
      paginationItems.push(
        <button
          type="button"
          key={1}
          onClick={() => handleChangePage(1)}
          className={`px-4 py-2 mx-1 rounded ${
            page === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
        >
          1
        </button>
      );
      if (startPage > 2) {
        paginationItems.push(
          <span key="start-ellipsis" className="px-4 py-2 mx-1">
            ...
          </span>
        );
      }
    }

    const pageNumbers = Array.from(
      { length: endPage - startPage + 1 },
      (_, index) => startPage + index
    );
    pageNumbers.map((item) => {
      paginationItems.push(
        <button
          type="button"
          key={item}
          onClick={() => handleChangePage(item)}
          className={`px-4 py-2 mx-1 rounded ${
            page === item ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
        >
          {item}
        </button>
      );
      return null;
    });

    if (endPage < lastPage) {
      if (endPage < lastPage - 1) {
        paginationItems.push(
          <span key="end-ellipsis" className="px-4 py-2 mx-1">
            ...
          </span>
        );
      }
      paginationItems.push(
        <button
          type="button"
          key={lastPage}
          onClick={() => handleChangePage(lastPage)}
          className={`px-4 py-2 mx-1 rounded ${
            page === lastPage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
        >
          {lastPage}
        </button>
      );
    }

    return paginationItems;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lastPage, page]);

  return (
    <div className="flex justify-center items-center mt-4">
      <button
        type="button"
        onClick={() => handleChangePage(page - 1)}
        disabled={page === 1}
        className="px-4 py-2 mx-1 bg-gray-200 text-gray-700 rounded disabled:opacity-50"
      >
        {'< '} Anterior
      </button>
      {getPaginationItems}
      <button
        type="button"
        onClick={() => handleChangePage(page + 1)}
        disabled={lastPage === page || lastPage === 0}
        className="px-4 py-2 mx-1 bg-gray-200 text-gray-700 rounded disabled:opacity-50"
      >
        Próxima {'> '}
      </button>
    </div>
  );
}

Pagination.propTypes = {
  page: PropTypes.number,
  count: PropTypes.number,
  limit: PropTypes.number,
  handleChangePage: PropTypes.func,
};
