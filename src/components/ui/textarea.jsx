/* eslint-disable react/prop-types */
import * as React from 'react';

import { cn } from '../../utils/lib/utils';

const Textarea = React.forwardRef(({ className, ...props }, ref) => {
  const error = props.errors?.[props.name];
  console.log(error);
  return (
    <>
      {props.label && (
        <span
          className={`${error && 'text-red-500'} block text-sm font-medium mb-1`}
          htmlFor={props.name}
        >
          {props.label}
        </span>
      )}
      <textarea
        className={cn(
          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          className,
          error && 'border-red-500'
        )}
        ref={ref}
        {...props}
      />
      <p className="text-sm text-red-500 mt-1">{error?.message}</p>
    </>
  );
});
Textarea.displayName = 'Textarea';

export { Textarea };
