import PropTypes from 'prop-types';
import { Box, Stack } from '@mui/material';

ProgressSteps.propTypes = {
  steps: PropTypes.number,
  activeStep: PropTypes.number,
};

export function ProgressSteps({ steps, activeStep }) {
  return (
    <Stack direction="row" spacing={1} alignItems="center">
      {Array.from({ length: steps }).map((_, index) => (
        <Box
          key={index}
          sx={{
            width: index + 1 === activeStep ? 14 : 8,
            height: 8,
            borderRadius: index + 1 === activeStep ? '30%' : '50%',
            backgroundColor: index + 1 === activeStep ? '#4caf50' : '#4caf50a0',
            transition: 'background-color 0.3s ease',
          }}
        />
      ))}
    </Stack>
  );
}
