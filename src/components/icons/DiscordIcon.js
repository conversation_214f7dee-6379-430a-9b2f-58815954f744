export default function DiscordIcon(props) {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="40" height="40" rx="20" fill="#5865F2" />
      <g clipPath="url(#clip0_4534_9217)">
        <path
          d="M28.5 4H11.5C7.35786 4 4 7.35786 4 11.5V28.5C4 32.6421 7.35786 36 11.5 36H28.5C32.6421 36 36 32.6421 36 28.5V11.5C36 7.35786 32.6421 4 28.5 4Z"
          fill="#5865F2"
        />
        <mask id="mask0_4534_9217" maskUnits="userSpaceOnUse" x="7" y="10" width="26" height="20">
          <path d="M7.5 10.375H32.5V29.7412H7.5V10.375Z" fill="white" />
        </mask>
        <g mask="url(#mask0_4534_9217)">
          <path
            d="M28.6634 12.0997C27.0426 11.3557 25.3321 10.8252 23.5748 10.5213C23.5588 10.5184 23.5424 10.5205 23.5277 10.5275C23.513 10.5344 23.5009 10.5458 23.493 10.56C23.2733 10.9508 23.0298 11.4608 22.8594 11.8615C20.9378 11.5738 19.0262 11.5738 17.144 11.8615C16.9735 11.4518 16.7213 10.9508 16.5005 10.56C16.4924 10.5461 16.4802 10.5349 16.4656 10.528C16.451 10.521 16.4346 10.5187 16.4187 10.5213C14.6612 10.8245 12.9506 11.3551 11.33 12.0997C11.3163 12.1057 11.3046 12.1157 11.2965 12.1284C8.05555 16.9704 7.1678 21.6934 7.6033 26.3579C7.60595 26.3809 7.61764 26.402 7.6358 26.4164C9.77405 27.9868 11.8454 28.9402 13.8783 29.572C13.8941 29.5767 13.911 29.5765 13.9267 29.5713C13.9423 29.5662 13.9561 29.5564 13.966 29.5433C14.4469 28.8865 14.8755 28.1942 15.2432 27.466C15.2482 27.4561 15.2511 27.4451 15.2517 27.434C15.2522 27.4228 15.2503 27.4116 15.2462 27.4012C15.2421 27.3908 15.2359 27.3814 15.2279 27.3736C15.2199 27.3658 15.2103 27.3597 15.1998 27.3559C14.5198 27.0979 13.8724 26.7835 13.2497 26.4264C13.2383 26.4197 13.2288 26.4104 13.2219 26.3991C13.2151 26.3879 13.2111 26.3751 13.2103 26.362C13.2095 26.3489 13.212 26.3357 13.2175 26.3238C13.223 26.3118 13.2313 26.3014 13.2418 26.2934C13.3732 26.1952 13.5023 26.094 13.629 25.9899C13.6402 25.9808 13.6536 25.9749 13.6679 25.9729C13.6821 25.971 13.6966 25.9731 13.7098 25.9789C17.8013 27.8469 22.2307 27.8469 26.2738 25.9789C26.287 25.9727 26.3017 25.9703 26.3162 25.9721C26.3307 25.9739 26.3444 25.9797 26.3557 25.9889C26.4826 26.0936 26.612 26.1951 26.7439 26.2934C26.7544 26.3013 26.7629 26.3117 26.7684 26.3236C26.774 26.3355 26.7766 26.3486 26.7759 26.3617C26.7752 26.3749 26.7713 26.3876 26.7645 26.3989C26.7578 26.4102 26.7483 26.4196 26.737 26.4264C26.114 26.7902 25.4613 27.1008 24.7859 27.3549C24.7755 27.3589 24.766 27.3651 24.7581 27.3731C24.7501 27.381 24.744 27.3906 24.74 27.401C24.736 27.4115 24.7342 27.4227 24.7349 27.4339C24.7355 27.4451 24.7384 27.4561 24.7435 27.466C25.1175 28.1884 25.544 28.8823 26.0195 29.5423C26.0293 29.5557 26.043 29.5658 26.0587 29.5712C26.0744 29.5765 26.0914 29.5768 26.1073 29.572C28.15 28.9402 30.2214 27.9868 32.3598 26.4164C32.3689 26.4096 32.3766 26.401 32.3822 26.3911C32.3878 26.3812 32.3912 26.3702 32.3923 26.3589C32.9135 20.9663 31.5192 16.2819 28.6959 12.1294C28.6891 12.1159 28.6775 12.1053 28.6634 12.0997ZM15.8542 23.5178C14.6223 23.5178 13.6073 22.3869 13.6073 20.998C13.6073 19.6092 14.6027 18.4783 15.8542 18.4783C17.1154 18.4783 18.1205 19.6192 18.1008 20.998C18.1008 22.3869 17.1055 23.5178 15.8542 23.5178ZM24.1612 23.5178C22.9293 23.5178 21.9144 22.3869 21.9144 20.998C21.9144 19.6092 22.9097 18.4783 24.1612 18.4783C25.4224 18.4783 26.4275 19.6192 26.4079 20.998C26.4079 22.3869 25.4224 23.5178 24.1612 23.5178Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_4534_9217">
          <rect width="32" height="32" fill="white" transform="translate(4 4)" />
        </clipPath>
      </defs>
    </svg>
  );
}
