import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { AppErrorRendererCard } from './AppErrorRenderer';

export const useErrorSnackbar = () => {
  const { enqueueSnackbar } = useSnackbar();
  const defaultFieldNameMap = {
    code: 'Código',
  };
  const appErrorRenderer = ({ error, fieldNameMap = defaultFieldNameMap, dataTestId = 'api-error-renderer' }) => {
    if (!error) {
      return null;
    }

    const hasFieldErrors = error.fieldErrors?.length > 0;

    if (!hasFieldErrors && error.message) {
      return enqueueSnackbar(
        <AppErrorRendererCard dataTestId={dataTestId} message={error.message} />,
        {
          variant: 'error',
        }
      );
    }

    if (hasFieldErrors) {
      return (
        <>
          {error.fieldErrors.map(({ field, errors }) =>
            errors.map((errorMsg, index) =>
              enqueueSnackbar(
                <AppErrorR<PERSON>er<PERSON>ard
                  key={`${field}-${index}`}
                  field={field}
                  message={error.message}
                  errorMsg={errorMsg}
                  fieldNameMap={fieldNameMap}
                />,
                {
                  variant: 'error',
                }
              )
            )
          )}
        </>
      );
    }

    return null;
  };

  appErrorRenderer.propTypes = {
    error: PropTypes.shape({
      message: PropTypes.string,
      fieldErrors: PropTypes.arrayOf(
        PropTypes.shape({
          field: PropTypes.string,
          errors: PropTypes.arrayOf(PropTypes.string),
        })
      ),
    }),
    fieldNameMap: PropTypes.objectOf(PropTypes.string),
    dataTestId: PropTypes.string,
  };

  return { appErrorRenderer };
};
