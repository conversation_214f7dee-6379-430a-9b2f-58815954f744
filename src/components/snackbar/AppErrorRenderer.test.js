import { render, screen } from '@testing-library/react';
import { AppErrorRendererCard } from './AppErrorRenderer';

describe('AppErrorRendererCard', () => {
  it('should render only message when no field is provided', () => {
    render(<AppErrorRendererCard message="General error message" />);
    expect(screen.getByText('General error message')).toBeInTheDocument();
    expect(screen.queryByTestId('error-card')).not.toBeInTheDocument();
  });

  it('should render field name and message when field is provided', () => {
    render(
      <AppErrorRendererCard 
        field="email" 
        message="Invalid email format" 
        hasFieldErrors 
      />
    );
    
    expect(screen.getByText('email')).toBeInTheDocument();
    expect(screen.getByText('Invalid email format')).toBeInTheDocument();
  });

  it('should render mapped field name when fieldNameMap is provided', () => {
    const fieldNameMap = { email: 'Email Address' };
    
    render(
      <AppErrorRendererCard 
        field="email" 
        message="Invalid email format" 
        fieldNameMap={fieldNameMap}
        hasFieldErrors
      />
    );
    
    expect(screen.getByText('Email Address')).toBeInTheDocument();
    expect(screen.queryByText('email')).not.toBeInTheDocument();
  });

  it('should render error message when errorMsg is provided', () => {
    render(
      <AppErrorRendererCard 
        field="email" 
        message="Validation failed" 
        errorMsg="Invalid email format"
        hasFieldErrors
      />
    );
    
    expect(screen.getByText('Validation failed')).toBeInTheDocument();
    expect(screen.getByText('Invalid email format')).toBeInTheDocument();
  });

  it('should apply different styling when hasFieldErrors is false', () => {
    const { container } = render(
      <AppErrorRendererCard 
        message="General error message" 
        hasFieldErrors={false}
      />
    );
    
    const box = container.firstChild;
    expect(box).toHaveStyle('align-items: center');
    expect(box).toHaveStyle('min-height: 70px');
    expect(box).toHaveStyle('padding-top: 0');
    expect(box).toHaveStyle('padding-bottom: 0');
  });

  it('should apply correct data-testid when provided', () => {
    render(
      <AppErrorRendererCard 
        message="Test message" 
        dataTestId="test-error-card"
      />
    );
    
    expect(screen.getByTestId('test-error-card')).toBeInTheDocument();
  });
});