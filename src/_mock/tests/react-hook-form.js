// Mock do useForm
const useForm = () => ({
  register: jest.fn(),
  handleSubmit: jest.fn((fn) => fn),
  unregister: jest.fn(),
  watch: jest.fn(),
  reset: jest.fn(),
  setError: jest.fn(),
  clearErrors: jest.fn(),
  setValue: jest.fn(),
  getValues: jest.fn(),
  trigger: jest.fn(),
  formState: {
    isDirty: false,
    isSubmitting: false,
    isValid: true,
    touched: {},
    isSubmitSuccessful: false,
    submitCount: 0,
    dirtyFields: {},
    errors: {},
    isFieldValidating: jest.fn(),
    isValidating: false,
    isValidatingAll: jest.fn(),
    isValidatingField: jest.fn(),
  },
  errors: {},
});

// eslint-disable-next-line react/prop-types
const FormProvider = ({ children }) => <div id="test-form">{children}</div>;

const Controller = ({ name, defaultValue, render }) => {
  const mockRegister = jest.fn();
  const mockState = {
    dirty: false,
    isDirty: false,
    isTouched: false,
    isValid: true,
    isValidating: false,
    errors: {},
    value: defaultValue,
  };

  mockRegister(name, { ...mockState });

  return render({
    field: {
      name,
      onChange: jest.fn(),
      onBlur: jest.fn(),
      ref: null,
    },
    fieldState: mockState,
    formState: { isSubmitting: false },
  });
};

const useFormContext = () => ({
  register: jest.fn(),
  unregister: jest.fn(),
  handleSubmit: jest.fn(),
  control: {
    register: jest.fn(),
    unregister: jest.fn(),
    setValue: jest.fn(),
    defaultValue: '',
    errors: {},
  },
});

export { useForm, FormProvider, Controller, useFormContext };
