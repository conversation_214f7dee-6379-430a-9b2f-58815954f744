/* eslint-disable class-methods-use-this */
// uppy-core-mock.js

class UppyCore {
  // eslint-disable-next-line no-useless-constructor
  constructor(options) {
    // Você pode configurar qualquer comportamento necessário no construtor
  }

  // Exemplo de métodos que você pode querer mockar
  use(plugin) {
    // Implementação simulada do método use
  }

  on(event, callback) {
    // Implementação simulada do método on
  }

  off(event, callback) {
    // Implementação simulada do método off
  }

  addFile(file) {
    // Implementação simulada do método addFile
  }

  upload() {
    // Implementação simulada do método upload
  }

  getState() {
    // Implementação simulada do método getState
  }

  setState(newState) {
    // Implementação simulada do método setState
  }

  close() {
    // Implementação simulada do método close
  }

  reset() {
    // Implementação simulada do método reset
  }

  // Adicione mais métodos conforme necessário para seus testes
}

module.exports = UppyCore;
