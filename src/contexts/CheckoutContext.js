import { useErrorSnackbar } from '@/components/snackbar/useErrorSnackbar';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as yup from 'yup';
import useTableFilter from '../hooks/useTableFilter';
import {
  createCheckoutService,
  deleteCheckoutService,
  duplicateCheckoutService,
  getCheckouts,
  updateCheckoutService,
} from '../services/product-checkout';
import { FormScopes } from '../utils/form';

export const ProductCheckoutFormDefaultValues = {
  name: '',
  default: false,
  offers: [],
};

const CheckoutContext = createContext({
  form: null,
  table: null,
  scope: FormScopes.INDEX,
  setScope: () => {},
  creating: false,
  createCheckout: () => {},
  deleting: false,
  deleteCheckout: () => {},
  updateCheckout: () => {},
  duplicating: false,
  duplicateCheckout: () => {},
  updating: false,
  checkout: null,
  setCheckout: () => {},
  checkouts: [],
  loading: false,
});

const CheckoutProvider = ({ children }) => {
  const { enqueueSnackbar } = useSnackbar();
  const { appErrorRenderer } = useErrorSnackbar();

  const table = useTableFilter();

  const schema = yup.object().shape({
    name: yup.string().required('Nome é obrigatório'),
    default: yup.boolean(),
    links: yup.array(),
  });

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: ProductCheckoutFormDefaultValues,
  });

  const [checkout, setCheckout] = useState(null);

  const [scope, setScope] = useState(FormScopes.INDEX);

  const queryClient = useQueryClient();

  const { id } = useParams();

  const {
    data: { checkouts = [], count },
    isLoading: fetching,
  } = useQuery({
    queryKey: [`products/${id}/checkouts`, { page: table.page, search: table.search }],
    queryFn: () =>
      getCheckouts({
        id,
        page: table.page,
        search: table.search,
        limit: table.rowsPerPage,
      }),
    enabled: !!id,
    initialData: {
      results: [],
      count: 0,
    },
  });

  const { mutateAsync: updateCheckout, isLoading: updating } = useMutation({
    mutationFn: updateCheckoutService,
    onSuccess: () => {
      queryClient.invalidateQueries([`products/${id}/checkouts`]);

      enqueueSnackbar('Checkout salvo com sucesso!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: createCheckout, isLoading: creating } = useMutation({
    mutationFn: (data) => createCheckoutService({ id, ...data }),
    onSuccess: () => {
      queryClient.invalidateQueries([`products/${id}/checkouts`]);

      enqueueSnackbar('Checkout criado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      if (error?.response?.status === 404) {
        enqueueSnackbar('Nenhum usuário encontrado com este email', { variant: 'error' });
        return;
      }

      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: deleteCheckout, isLoading: deleting } = useMutation({
    mutationFn: deleteCheckoutService,
    onSuccess: () => {
      queryClient.invalidateQueries([`products/${id}/checkouts`]);

      enqueueSnackbar('Checkout excluído com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      if (error?.response?.status === 404) {
        enqueueSnackbar('Nenhum usuário encontrado com este email', { variant: 'error' });
        return;
      }

      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: duplicateCheckout, isLoading: duplicating } = useMutation({
    mutationFn: duplicateCheckoutService,
    onSuccess: () => {
      queryClient.invalidateQueries([`products/${id}/checkouts`]);

      enqueueSnackbar('Checkout duplicado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error, dataTestId: 'duplicate-checkout-error' });
    },
  });

  const value = useMemo(
    () => ({
      scope,
      setScope,
      updateCheckout,
      creating,
      createCheckout,
      deleting,
      deleteCheckout,
      updating,
      duplicateCheckout,
      duplicating,
      checkout,
      setCheckout,
      checkouts,
      count,
      fetching,
      table,
      form,
    }),
    [
      scope,
      updateCheckout,
      creating,
      createCheckout,
      deleting,
      deleteCheckout,
      updating,
      duplicateCheckout,
      duplicating,
      checkout,
      checkouts,
      count,
      fetching,
      table,
      form,
    ]
  );

  return <CheckoutContext.Provider value={value}>{children}</CheckoutContext.Provider>;
};

CheckoutProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export { CheckoutContext, CheckoutProvider };
