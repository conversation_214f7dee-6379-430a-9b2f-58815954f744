import { yupResolver } from '@hookform/resolvers/yup';
import PropTypes from 'prop-types';
import { createContext, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import FormProvider from '../components/hook-form/FormProvider';
import Iconify from '../components/iconify';
import Image from '../components/image';
import { FormScopes } from '../utils/form';

export const ProductPixelsContext = createContext({
  form: {},
  scope: {},
  setScope: () => {},
  schema: {},
  pixelError: '',
  setPixelError: () => {},
});

const TriggerSchema = yup.object().shape({
  provider: yup.string().required('Esse campo é obrigatório'),
  type: yup.string().required('Esse campo é obrigatório'),
  enabled: yup.boolean().required('Esse campo é obrigatório'),
  value: yup
    .number()
    .typeError('Digite um número válido')
    .when('enabled', {
      is: true,
      then: yup.number().typeError('Digite um número válido').required('Esse campo é obrigatório'),
    }),
});

export const FacebookPixelSchema = yup.object().shape({
  pixelId: yup.string().required('Esse campo é obrigatório'),
  domain: yup.string(),
  apiToken: yup.string().optional(),
});

export const FacebookSchema = yup.object().shape({
  pixels: yup.array().of(FacebookPixelSchema),
  triggers: yup.array().of(TriggerSchema),
});

export const GoogleAdsPixelSchema = yup.object().shape({
  name: yup.string().required('Esse campo é obrigatório'),
  pixelId: yup.string().required('Esse campo é obrigatório'),
  conversionLabel: yup.string().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.string().required('Esse campo é obrigatório'),
  }),
  checkoutVisitTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  cardPixApprovalTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  boletoTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
});

export const GoogleAdsSchema = yup.object().shape({
  pixels: yup.array().of(GoogleAdsPixelSchema),
});

export const GoogleAnalyticsPixelSchema = yup.object().shape({
  trackingId: yup.string(),
});

export const GoogleAnalyticsSchema = yup.object().shape({
  pixels: yup.array().of(GoogleAnalyticsPixelSchema),
});

export const TaboolaPixelSchema = yup.object().shape({
  eventName: yup.string().required('Esse campo é obrigatório'),
  accountId: yup.string().required('Esse campo é obrigatório'),
  checkoutVisitTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  cardPixApprovalTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  boletoTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
});

export const TaboolaSchema = yup.object().shape({
  pixels: yup.array().of(TaboolaPixelSchema),
  triggers: yup.array().of(TriggerSchema),
});

export const OutbrainPixelSchema = yup.object().shape({
  eventName: yup.string().required('Esse campo é obrigatório'),
  pixelId: yup.string().required('Esse campo é obrigatório'),
  checkoutVisitTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  cardPixApprovalTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
  boletoTrigger: yup.boolean().when('$scope', {
    is: FormScopes.EDIT,
    then: yup.boolean().required('Esse campo é obrigatório'),
  }),
});

export const OutbrainSchema = yup.object().shape({
  pixels: yup.array().of(OutbrainPixelSchema),
  triggers: yup.array().of(TriggerSchema),
});

export const TikTokPixelSchema = yup.object().shape({
  pixelId: yup.string().required('Esse campo é obrigatório'),
  apiToken: yup.string().required('Esse campo é obrigatório'),
});

export const TikTokSchema = yup.object().shape({
  pixels: yup.array().of(TikTokPixelSchema),
  triggers: yup.array().of(TriggerSchema),
});

export const KwaiPixelSchema = yup.object().shape({
  pixelId: yup.string().required('Esse campo é obrigatório'),
});

export const KwaiSchema = yup.object().shape({
  pixels: yup.array().of(KwaiPixelSchema),
  triggers: yup.array().of(TriggerSchema),
});

export const FacebookPixelDefaultValue = {
  pixelId: '',
  domain: '',
  apiToken: '',
};

export const GoogleAdsPixelDefaultValue = {
  name: '',
  pixelId: '',
  conversionLabel: '',
  checkoutVisitTrigger: false,
  cardPixApprovalTrigger: false,
  boletoTrigger: false,
};

export const GoogleAnalyticsPixelDefaultValue = {
  trackingId: '',
};

export const TaboolaPixelDefaultValue = {
  eventName: '',
  accountId: '',
  checkoutVisitTrigger: false,
  cardPixApprovalTrigger: false,
  boletoTrigger: false,
};

export const OutbrainPixelDefaultValue = {
  eventName: '',
  pixelId: '',
  checkoutVisitTrigger: false,
  cardPixApprovalTrigger: false,
  boletoTrigger: false,
};

export const TikTokPixelDefaultValue = {
  pixelId: '',
};

export const KwaiPixelDefaultValue = {
  pixelId: '',
};

const ProductPixelsProvider = ({ children }) => {
  const [provider, setProvider] = useState('facebook');
  const [scope, setScope] = useState(FormScopes.INDEX);
  const [pixelError, setPixelError] = useState();

  const ProductPixelsDefaultValue = {
    facebook: {
      pixels: [],
      triggers: [
        {
          provider: 'facebook',
          type: 'pix',
          enabled: false,
          value: 100,
        },
        {
          provider: 'facebook',
          type: 'ticket',
          enabled: false,
          value: 100,
        },
      ],
    },
    googleAds: {
      pixels: [],
    },
    googleAnalytics: {
      pixels: [GoogleAnalyticsPixelDefaultValue],
    },
    taboola: {
      pixels: [],
    },
    outbrain: {
      pixels: [],
    },
    tiktok: {
      pixels: [],
      triggers: [
        {
          provider: 'tiktok',
          type: 'pix',
          enabled: false,
          value: 100,
        },
        {
          provider: 'tiktok',
          type: 'ticket',
          enabled: false,
          value: 100,
        },
      ],
    },
    kwai: {
      pixels: [],
      triggers: [
        {
          provider: 'kwai',
          type: 'pix',
          enabled: false,
          value: 100,
        },
        {
          provider: 'kwai',
          type: 'ticket',
          enabled: false,
          value: 100,
        },
      ],
    },
  };

  const PixelProviderConfig = {
    facebook: {
      schema: FacebookSchema,
      pixelSchema: FacebookPixelSchema,
      defaultPixelValues: FacebookPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.facebook,
      icon: <Iconify icon="logos:facebook" width={24} height={24} />,
      label: 'Facebook',
    },
    googleAds: {
      schema: GoogleAdsSchema,
      pixelSchema: GoogleAdsPixelSchema,
      defaultPixelValues: GoogleAdsPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.googleAds,
      icon: <Iconify icon="logos:google-ads" width={24} height={24} />,
      label: 'Google Ads',
    },
    googleAnalytics: {
      schema: GoogleAnalyticsSchema,
      pixelSchema: GoogleAnalyticsPixelSchema,
      defaultPixelValues: GoogleAnalyticsPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.googleAnalytics,
      icon: <Iconify icon="logos:google-analytics" width={24} height={24} />,
      label: 'Google Analytics',
    },
    taboola: {
      schema: TaboolaSchema,
      pixelSchema: TaboolaPixelSchema,
      defaultPixelValues: TaboolaPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.taboola,
      icon: (
        <Image
          src="/assets/icons/pixels/taboola.png"
          sx={{
            width: 24,
            height: 24,
            mx: 1,
          }}
        />
      ),
      label: 'Taboola',
    },
    outbrain: {
      schema: OutbrainSchema,
      pixelSchema: OutbrainPixelSchema,
      defaultPixelValues: OutbrainPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.outbrain,
      icon: (
        <Image
          src="/assets/icons/pixels/outbrain.png"
          sx={{
            width: 24,
            height: 24,
            mx: 1,
          }}
        />
      ),
      label: 'Outbrain',
    },
    tiktok: {
      schema: TikTokSchema,
      pixelSchema: TikTokPixelSchema,
      defaultPixelValues: TikTokPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.tiktok,
      icon: <Iconify icon="logos:tiktok-icon" width={24} height={24} />,
      label: 'TiKTok',
    },
    kwai: {
      schema: KwaiSchema,
      pixelSchema: KwaiPixelSchema,
      defaultPixelValues: KwaiPixelDefaultValue,
      defaultValues: ProductPixelsDefaultValue.kwai,
      icon: (
        <Image
          src="/assets/icons/pixels/kwai.png"
          sx={{
            width: 24,
            height: 24,
            mx: 1,
          }}
        />
      ),
      label: 'Kwai',
    },
  };

  const schema = yup.object().shape({
    facebook: FacebookSchema,
    googleAds: GoogleAdsSchema,
    googleAnalytics: GoogleAnalyticsSchema,
    taboola: TaboolaSchema,
    outbrain: OutbrainSchema,
    tiktok: TikTokSchema,
    kwai: KwaiSchema,
  });

  const form = useForm({
    context: { scope },
    resolver: yupResolver(schema),
    defaultValues: ProductPixelsDefaultValue,
  });

  // eslint-disable-next-line
  const value = {
    form,
    scope,
    setScope,
    provider,
    setProvider,
    PixelProviderConfig,
    pixelError,
    setPixelError,
  };

  return (
    <ProductPixelsContext.Provider value={value}>
      <FormProvider methods={form}>{children}</FormProvider>
    </ProductPixelsContext.Provider>
  );
};

ProductPixelsProvider.propTypes = {
  children: PropTypes.node.isRequired,
  config: PropTypes.shape({
    schema: PropTypes.object.isRequired,
    defaultValues: PropTypes.object.isRequired,
    pixelSchema: PropTypes.object.isRequired,
    defaultPixelValues: PropTypes.object.isRequired,
    icon: PropTypes.node.isRequired,
    label: PropTypes.string.isRequired,
  }).isRequired,
};

export default ProductPixelsProvider;
