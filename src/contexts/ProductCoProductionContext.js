import { useErrorSnackbar } from '@/components/snackbar/useErrorSnackbar';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as yup from 'yup';
import { useAuthContext } from '../auth/useAuthContext';
import useTableFilter from '../hooks/useTableFilter';
import {
  acceptProductCoProductionService,
  cancelDeleteProductCoProductionService,
  createProductCoProductionService,
  deleteProductCoProductionService,
  getProductCoProductionsByEmailService,
  getProductCoProductionsService,
  rejectProductCoProductionService,
  updateProductCoProductionService,
} from '../services/product-coproductions';
import { FormScopes } from '../utils/form';

export const ProductCoProductionsContext = createContext({
  table: {},
  form: {},
  coproductions: [],
  userEmail: '',
  setUserEmail: () => {},
  fetching: false,
  count: 0,
  create: () => {},
  creating: false,
  accept: () => {},
  accepting: false,
  deleteContract: () => {},
  deleting: false,
  rejectContract: () => {},
  rejecting: false,
  update: () => {},
  updating: false,
  cancelDelete: () => {},
  canceling: false,
  scope: FormScopes.INDEX,
  setScope: () => {},
  coproduction: {},
  setCoproduction: () => {},
  getProductionsByUserEmail: () => {},
});

export const ProductCoProductionStatus = {
  pending: {
    label: 'Pendente',
    color: 'warning',
    value: 'pending',
  },
  canceled: {
    label: 'Cancelado',
    color: 'default',
    value: 'canceled',
  },
  active: {
    label: 'Ativo',
    color: 'success',
    value: 'active',
  },
  expired: {
    label: 'Expirado',
    color: 'default',
    value: 'expired',
  },
  rejected: {
    label: 'Recusado',
    color: 'error',
    value: 'rejected',
  },
};

export const ProductCoProductionDuration = {
  forever: {
    label: 'Eterno',
    value: 'forever',
  },
  thirty: {
    label: '30 dias',
    value: '30',
  },
  sixty: {
    label: '60 dias',
    value: '60',
  },
  ninety: {
    label: '90 dias',
    value: '90',
  },
  oneEighty: {
    label: '180 dias',
    value: '180',
  },
};

export const ProductCoProductionFormDefaultValues = {
  id: '',
  email: '',
  duration: ProductCoProductionDuration.thirty.value,
  receiveSalesFromAffiliate: false,
  receiveSalesFromProducer: true,
  commission: '0.00',
  enableInvoice: true,
  validityDate: '',
  status: ProductCoProductionStatus.pending.value,
};

const fieldNameMap = {
  id: 'ID',
  product_pk: 'ID do produto',
  email: 'Email do co-produtor',
  duration: 'Duração',
  receiveSalesFromAffiliate: 'Receber vendas do afiliado',
  receiveSalesFromProducer: 'Receber vendas do produtor',
  commission: 'Comissão',
  enableInvoice: 'Gerar fatura',
  validityDate: 'Data de validade',
  status: 'Status',
};

const ProductCoProductionsProvider = ({ type, children }) => {
  const {
    user: { email },
  } = useAuthContext();

  const { id: productPk } = useParams();
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const [scope, setScope] = useState(FormScopes.INDEX);
  const [userEmail, setUserEmail] = useState(email);
  const [status, setStatus] = useState('all');

  const queryClient = useQueryClient();

  const { enqueueSnackbar } = useSnackbar();
  const { appErrorRenderer } = useErrorSnackbar();

  const [coproduction, setCoproduction] = useState({});

  const schema = yup.object().shape({
    id: yup.string(),
    product_pk: yup.string(),
    email: yup.string().email().required(),
    duration: yup.string().required(),
    receiveSalesFromAffiliate: yup.boolean(),
    receiveSalesFromProducer: yup.boolean(),
    commission: yup.number().min(0.01).max(100).required(),
    enableInvoice: yup.boolean().required(),
    validityDate: yup.string(),
    status: yup.string(),
  });

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      id: coproduction.id || ProductCoProductionFormDefaultValues.id,
      product_pk: coproduction?.product?.id || ProductCoProductionFormDefaultValues.product_pk,
      email: coproduction?.coProducer || ProductCoProductionFormDefaultValues.email,
      duration:
        coproduction.duration ||
        ProductCoProductionFormDefaultValues.duration ||
        ProductCoProductionDuration.thirty.value,
      receiveSalesFromAffiliate:
        coproduction.receiveSalesFromAffiliate ||
        ProductCoProductionFormDefaultValues.receiveSalesFromAffiliate,
      receiveSalesFromProducer:
        coproduction.receiveSalesFromProducer ||
        ProductCoProductionFormDefaultValues.receiveSalesFromProducer,
      commission: coproduction.commission || ProductCoProductionFormDefaultValues.commission,
      enableInvoice:
        coproduction.enableInvoice || ProductCoProductionFormDefaultValues.enableInvoice,
      validityDate: coproduction.validityDate || ProductCoProductionFormDefaultValues.validityDate,
      status:
        coproduction.status ||
        ProductCoProductionFormDefaultValues.status ||
        ProductCoProductionStatus.pending.value,
    },
  });

  const { data: { results: coproductions = [], count = 0 } = {}, isFetching: fetching } = useQuery({
    queryKey: ['coproductions', { page: table.page, search: table.search, status, type }],
    queryFn: () =>
      getProductCoProductionsService({
        productPk: productPk || undefined,
        page: table.page,
        email: userEmail,
        search: table.search,
        limit: table.rowsPerPage,
        status,
        type,
      }),
    initialData: {
      results: [],
      count: 0,
    },
  });

  const { mutateAsync: getProductionsByUserEmail } = useMutation({
    mutationFn: getProductCoProductionsByEmailService,
    onError: (error) => {
      appErrorRenderer({ error, fieldNameMap });
    },
  });

  const { mutateAsync: create, isLoading: creating } = useMutation({
    mutationFn: createProductCoProductionService,
    onSuccess: () => {
      queryClient.invalidateQueries('coproductions');

      enqueueSnackbar('Contrato criado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error, fieldNameMap });
    },
  });

  const { mutateAsync: update, isLoading: updating } = useMutation({
    mutationFn: updateProductCoProductionService,
    onSuccess: () => {
      queryClient.invalidateQueries('coproductions');

      enqueueSnackbar('Contrato atualizado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error, fieldNameMap });
    },
  });

  const cooperationsQueryKey = [
    'coproductions',
    { page: table.page, search: table.search, status, type },
  ];

  const getDataToUpdateStatus = async (data, newStatus) => {
    await queryClient.cancelQueries({ queryKey: cooperationsQueryKey });
    const previousData = queryClient.getQueryData(cooperationsQueryKey);

    const newData = {
      ...previousData,
      results: previousData?.results
        .map((previous) => {
          if (data.id.toString() === previous.id.toString()) {
            return {
              ...data,
              status: newStatus,
            };
          }

          return previous;
        })
        .filter((state) => state.status !== 'remove'),
    };

    return {
      previousData,
      newData,
    };
  };

  const { mutateAsync: accept, isLoading: accepting } = useMutation({
    mutationFn: acceptProductCoProductionService,
    onSuccess: () => {
      setUserEmail('');

      enqueueSnackbar('Contrato aceito com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      queryClient.invalidateQueries({ queryKey: cooperationsQueryKey });
      appErrorRenderer({ error, fieldNameMap });
    },
    async onMutate(data) {
      const { previousData, newData } = await getDataToUpdateStatus(data, 'active');

      queryClient.setQueryData(cooperationsQueryKey, newData);

      return { previousData };
    },
  });

  const { mutateAsync: rejectContract, isLoading: rejecting } = useMutation({
    mutationFn: rejectProductCoProductionService,
    onSuccess: () => {
      setUserEmail('');

      enqueueSnackbar('Contrato rejeitado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      queryClient.invalidateQueries({ queryKey: cooperationsQueryKey });
      appErrorRenderer({ error, fieldNameMap });
    },
    async onMutate(data) {
      const { previousData, newData } = await getDataToUpdateStatus(data, 'rejected');
      queryClient.setQueryData(cooperationsQueryKey, newData);
      return { previousData };
    },
  });

  const { mutateAsync: deleteContract, isLoading: deleting } = useMutation({
    mutationFn: deleteProductCoProductionService,
    onSuccess: () => {
      const message =
        coproduction.status === 'pending'
          ? 'Pedido de co produção removido com sucesso'
          : 'Pedido de cancelamento de contrato enviado com sucesso!';

      enqueueSnackbar(message, { variant: 'success' });
      setUserEmail('');
      setScope(FormScopes.INDEX);
    },
    async onMutate(data) {
      const { previousData, newData } = await getDataToUpdateStatus(
        data,
        data.isToRemove ? 'remove' : 'active'
      );

      queryClient.setQueryData(cooperationsQueryKey, newData);

      return { previousData };
    },
    onError: (error) => {
      queryClient.invalidateQueries({ queryKey: cooperationsQueryKey });
      appErrorRenderer({ error, fieldNameMap });
    },
  });

  const { mutateAsync: cancelDelete, isLoading: canceling } = useMutation({
    mutationFn: cancelDeleteProductCoProductionService,
    onSuccess: () => {
      enqueueSnackbar('Pedido para cancelamento de coprodução removido com sucesso!', {
        variant: 'success',
      });
      setUserEmail('');
      setScope(FormScopes.INDEX);
    },
    async onMutate(data) {
      const { previousData, newData } = await getDataToUpdateStatus(data, 'active');

      queryClient.setQueryData(cooperationsQueryKey, newData);

      return { previousData };
    },
    onError: (error) => {
      queryClient.invalidateQueries({ queryKey: cooperationsQueryKey });
      appErrorRenderer({ error, fieldNameMap });
    },
  });

  const value = useMemo(
    () => ({
      table,
      form,
      coproductions,
      fetching,
      count,
      update,
      updating,
      accept,
      accepting,
      rejectContract,
      rejecting,
      userEmail,
      create,
      creating,
      deleteContract,
      deleting,
      cancelDelete,
      canceling,
      scope,
      setScope,
      coproduction,
      setCoproduction,
      setUserEmail,
      getProductionsByUserEmail,
      status,
      setStatus,
    }),
    [
      table,
      form,
      setUserEmail,
      coproductions,
      fetching,
      count,
      update,
      updating,
      accept,
      accepting,
      rejectContract,
      rejecting,
      create,
      userEmail,
      creating,
      deleteContract,
      deleting,
      cancelDelete,
      canceling,
      scope,
      coproduction,
      getProductionsByUserEmail,
      status,
      setStatus,
    ]
  );

  return (
    <ProductCoProductionsContext.Provider value={value}>
      {children}
    </ProductCoProductionsContext.Provider>
  );
};

ProductCoProductionsProvider.propTypes = {
  type: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

export default ProductCoProductionsProvider;
