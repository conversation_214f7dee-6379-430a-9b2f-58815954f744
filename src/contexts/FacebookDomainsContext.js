/* eslint-disable import/no-cycle */
import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { createContext, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as yup from 'yup';
import useTableFilter from '../hooks/useTableFilter';
import { AuthenticatedHttp } from '../http/axios';
import { FormScopes } from '../utils/form';

export const FacebookDomainsContext = createContext({
  table: {},
  form: {},
  domains: [],
  domain: {},
  domainId: '',
  setDomainId: () => {},
  setDomain: () => {},
  scope: {},
  fetching: false,
  create: () => {},
  creating: false,
  remove: () => {},
  removing: false,
  verify: () => {},
  verifying: false,
});

export const FacebookDomainsDefaultValues = {
  domain: '',
  isVerified: false,
};

// eslint-disable-next-line react/prop-types
const FacebookDomainsProvider = ({ children }) => {
  const queryClient = useQueryClient();

  const { enqueueSnackbar } = useSnackbar();

  const [domain, setDomain] = useState({});
  const [scope, setScope] = useState({});
  const [domainId, setDomainId] = useState({});

  const schema = yup.object().shape({
    domain: yup.string(),
    isVerified: yup.boolean(),
  });

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      domain: FacebookDomainsDefaultValues.domain || '',
      isVerified: FacebookDomainsDefaultValues.isVerified || false,
    },
  });
  const { id, short_id } = useParams();

  const table = useTableFilter();

  const product = queryClient.getQueryData(['product', { id, short_id }]);

  const domains = product?.tracking_pixels?.pixel_domains;

  const verifyPixelDomain = async (pixelDomainId) => {
    try {
      await AuthenticatedHttp.post(`/pixels/pixel_domain/${pixelDomainId}/verify/`);
    } catch (error) {
      console.error('Erro na chamada:', error);
      throw error; // Lança novamente o erro para que quem chama a função possa lidar com ele
    }
  };

  const { mutateAsync: verify, isLoading: verifying } = useMutation({
    mutationFn: (pixelDomainId) => verifyPixelDomain(pixelDomainId),
    onSuccess: () => {
      enqueueSnackbar('Domínio verificado com sucesso', { variant: 'success' });

      queryClient.invalidateQueries(['product', { id, short_id }]);
      form.setValue('isVerified', true);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: 'error' });
    },
  });

  const createPixelDomain = async (pixelDomain) => {
    try {
      const response = await AuthenticatedHttp.post(`/pixels/pixel_domain/`, {
        domain: pixelDomain,
      });
      return response.data;
    } catch (error) {
      console.error('Erro na chamada GET:', error);
      throw error; // Lança novamente o erro para que quem chama a função possa lidar com ele
    }
  };

  const { mutateAsync: create, isLoading: creating } = useMutation({
    mutationFn: (pixelDomain) => createPixelDomain(pixelDomain),
    onSuccess: () => {
      enqueueSnackbar('Domínio criado com sucesso', { variant: 'success' });

      queryClient.invalidateQueries(['product', { id, short_id }]);

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: 'error' });
    },
  });

  const removePixelDomain = async (pixelDomainId) => {
    try {
      await AuthenticatedHttp.delete(`/pixels/pixel_domain/${pixelDomainId}`);
    } catch (error) {
      console.error('Erro na chamada GET:', error);
      throw error; // Lança novamente o erro para que quem chama a função possa lidar com ele
    }
  };

  const { mutateAsync: remove, isLoading: removing } = useMutation({
    mutationFn: async (pixelDomainId) => removePixelDomain(pixelDomainId),
    onSuccess: () => {
      enqueueSnackbar('Domínio removido com sucesso', { variant: 'success' });

      queryClient.invalidateQueries(['product', { id, short_id }]);

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: 'error' });
    },
  });

  const value = useMemo(
    () => ({
      domain,
      setDomain,
      form,
      verify,
      verifying,
      create,
      creating,
      remove,
      removing,
      table,
      scope,
      setScope,
      domainId,
      setDomainId,
      domains,
    }),
    [
      create,
      creating,
      domain,
      form,
      remove,
      removing,
      scope,
      table,
      verify,
      verifying,
      domainId,
      setDomainId,
      domains,
    ]
  );

  return (
    <FacebookDomainsContext.Provider value={value}>{children}</FacebookDomainsContext.Provider>
  );
};

export default FacebookDomainsProvider;
