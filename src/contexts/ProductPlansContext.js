import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { _prices } from '../_mock/arrays/_prices';
import useTableFilter from '../hooks/useTableFilter';
import { FormScopes } from '../utils/form';

export const ProductPricesType = {
  plan: {
    label: 'Plano',
    plural: 'Planos de Assinatura',
    value: 'plan',
  },
  offer: {
    label: 'Oferta',
    plural: 'Ofertas',
    value: 'offer',
  },
};

export const ProductPlanFrequency = {
  1: { label: 'Diário', value: 1 },
  7: { label: 'Semanal', value: 7 },
  30: { label: 'Mensal', value: 30 },
  60: { label: 'Bimestral', value: 60 },
  90: { label: 'Trimestral', value: 90 },
  180: { label: 'Semestral', value: 180 },
  365: { label: 'Anual', value: 365 },
};

export const ProductPlanRenewal = {
  until_cancel: {
    label: 'Até o cliente cancelar',
    value: 'until_cancel',
  },
  fixed_charges: {
    label: 'Número fixo de cobranças',
    value: 'fixed_charges',
  },
};

export const ProductOfferDefaultValues = {
  name: '',
  price: '0.00',
};

export const ProductPlanDefaultValues = {
  name: '',
  price: '0.00',
  recurrence_period: ProductPlanFrequency[30]?.value,
  renewal: ProductPlanRenewal.until_cancel?.value,
  quantity_recurrences: '0',
  firstPriceEnabled: false,
  firstPrice: '0.00',
};

export const PlanSchema = yup.object().shape({
  name: yup.string().required('Campo obrigatório'),
  price: yup.number().required('Campo obrigatório'),
  recurrence_period: yup.number().required('Campo obrigatório'),
  trial_days: yup
    .number()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .nullable()
    .min(1, 'A quantidade de dias de teste deve ser maior que zero')
    .notRequired(),
  max_retries: yup
    .number()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .nullable()
    .min(1, 'A quantidade de tentativas de cobrança deve ser maior que zero')
    .notRequired(),
  retry_interval: yup
    .number()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .nullable()
    .min(1, 'O intervalo ser maior que zero')
    .notRequired(),
});

export const ProductPlansContext = createContext({
  table: {},
  form: {},
  prices: [],
  fetching: false,
  create: () => {},
  creating: false,
  update: () => {},
  updating: false,
  remove: () => {},
  removing: false,
  count: 0,
  scope: FormScopes.INDEX,
  setScope: () => {},
});

const ProductPricesProvider = ({ children }) => {
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const [scope, setScope] = useState(FormScopes.INDEX);

  const { enqueueSnackbar } = useSnackbar();

  const form = useForm({
    resolver: yupResolver(PlanSchema),
    defaultValues: ProductPlanDefaultValues,
  });

  const {
    data: { results: prices = [], count = 0 },
    isFetching: fetching,
  } = useQuery({
    queryFn: () => ({
      results: _prices,
      count: 2,
    }),
    queryKey: ['prices', table.page, table.search, table.filter],
    initialData: {
      results: [],
      count: 0,
    },
  });

  const { mutateAsync: create, isLoading: creating } = useMutation({
    mutationFn: () => setTimeout(() => {}, 1000),
    onSuccess: () => {
      enqueueSnackbar('Preço adicionado com sucesso!', {
        variant: 'success',
      });
    },
    onError: () => {
      enqueueSnackbar('Não foi possível adicionar o preço!', {
        variant: 'error',
      });
    },
  });

  const { mutateAsync: update, isLoading: updating } = useMutation({
    mutationFn: () => setTimeout(() => {}, 1000),
    onSuccess: () => {
      enqueueSnackbar('Preço atualizado com sucesso!', {
        variant: 'success',
      });
    },
    onError: () => {
      enqueueSnackbar('Não foi possível atualizar o preço!', {
        variant: 'error',
      });
    },
  });

  const { mutateAsync: remove, isLoading: removing } = useMutation({
    mutationFn: () => setTimeout(() => {}, 1000),
    onSuccess: () => {
      enqueueSnackbar('Preço removido com sucesso!', {
        variant: 'success',
      });
    },
    onError: () => {
      enqueueSnackbar('Não foi possível remover o preço!', {
        variant: 'error',
      });
    },
  });

  const value = useMemo(
    () => ({
      table,
      prices,
      count,
      fetching,
      create,
      creating,
      update,
      updating,
      remove,
      removing,
      scope,
      setScope,
      form,
    }),
    [
      table,
      prices,
      count,
      fetching,
      create,
      creating,
      update,
      updating,
      remove,
      removing,
      scope,
      form,
    ]
  );

  return <ProductPlansContext.Provider value={value}>{children}</ProductPlansContext.Provider>;
};

ProductPricesProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ProductPricesProvider;
