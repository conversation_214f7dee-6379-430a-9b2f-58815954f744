import { createContext, useMemo, useState } from 'react';

import PropTypes from 'prop-types';

import useTableFilter from '@/hooks/useTableFilter';

export const InvoiceContext = createContext({
  table: {},
  scope: '',
  setScope: () => { },
});

const InvoiceProvider = ({ children }) => {
  const [scope, setScope] = useState('');
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const values = useMemo(
    () => ({
      table,
      scope,
      setScope,
    }),
    [table, scope, setScope]
  );

  return <InvoiceContext.Provider value={values}>{children}</InvoiceContext.Provider>;
};

InvoiceProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default InvoiceProvider;
