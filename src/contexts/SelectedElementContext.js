import PropTypes from 'prop-types';
import { createContext, useMemo, useState } from 'react';
import { useSelectedElementIndex } from '../sections/@checkout-builder/hooks/useSelectedElementIndex';

const SelectedElementContext = createContext({
  selected: {
    id: null,
    type: null,
    index: {
      row: null,
      column: null,
      component: null,
    },
  },
  selectedID: null,
  setSelectedID: () => {},
});

const SelectedElementProvider = ({ children }) => {
  const [selectedID, setSelectedID] = useState(null);

  const { selected } = useSelectedElementIndex({
    selectedID,
  });

  const value = useMemo(
    () => ({
      selected: selected ?? {
        id: null,
        type: null,
        index: {
          row: null,
          column: null,
          component: null,
        },
      },
      selectedID,
      setSelectedID,
    }),
    [selected, selectedID]
  );

  return (
    <SelectedElementContext.Provider value={value}>{children}</SelectedElementContext.Provider>
  );
};

SelectedElementProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export { SelectedElementContext, SelectedElementProvider };
