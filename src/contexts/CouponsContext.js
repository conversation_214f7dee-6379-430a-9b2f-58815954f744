import PropTypes from 'prop-types';
import { createContext, useMemo, useState } from 'react';
import useTableFilter from '../hooks/useTableFilter';

export const CouponsContext = createContext({
  table: {},
  scope: '',
  setScope: () => {},
});

const CouponsProvider = ({ children }) => {
  const [scope, setScope] = useState('');
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const values = useMemo(
    () => ({
      table,
      scope,
      setScope,
    }),
    [table, scope, setScope]
  );

  return <CouponsContext.Provider value={values}>{children}</CouponsContext.Provider>;
};

CouponsProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default CouponsProvider;
