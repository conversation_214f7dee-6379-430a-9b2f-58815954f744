import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

export const FacebookPixelAdvancedContext = createContext({
  form: {},
  save: () => {},
  saving: false,
});

export const FacebookPixelAdvancedDefaultValues = {
  facebookPixelHash: '',
  facebookPixelId: '',
  facebookPixelDomain: '',
  facebookPixelToken: '',
};

const FacebookPixelAdvancedProvider = ({ children }) => {
  const { enqueueSnackbar } = useSnackbar();

  const schema = yup.object().shape({
    facebookPixelHash: yup.string(),
    facebookPixelId: yup.string().required('Obrigatório'),
    facebookPixelDomain: yup.string().required('Obrigatório'),
    facebookPixelToken: yup.string().required('Obrigatório'),
  });

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      facebookPixelHash: FacebookPixelAdvancedDefaultValues.facebookPixelHash || '',
      facebookPixelId: FacebookPixelAdvancedDefaultValues.facebookPixelId || '',
      facebookPixelDomain: FacebookPixelAdvancedDefaultValues.facebookPixelDomain || '',
      facebookPixelToken: FacebookPixelAdvancedDefaultValues.facebookPixelToken || '',
    },
  });

  const { mutateAsync: save, isLoading: saving } = useMutation({
    mutationFn: () => new Promise((resolve) => setTimeout(resolve, 1000)),
    onSuccess: () => {
      enqueueSnackbar('Configurações salvas com sucesso', { variant: 'success' });
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: 'error' });
    },
  });

  const value = useMemo(
    () => ({
      form,
      save,
      saving,
    }),
    [form, save, saving]
  );

  return (
    <FacebookPixelAdvancedContext.Provider value={value}>
      {children}
    </FacebookPixelAdvancedContext.Provider>
  );
};

FacebookPixelAdvancedProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default FacebookPixelAdvancedProvider;
