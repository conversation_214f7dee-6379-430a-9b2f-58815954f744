import PropTypes from 'prop-types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { createContext, useMemo } from 'react';

import { useAuthContext } from '../auth/useAuthContext';
import useTableFilter from '../hooks/useTableFilter';
import { getAffiliates, deleteAffiliateService } from '../services/affiliates';

export const MyAffiliationsContext = createContext({
  table: {},
  affiliations: [],
  fetching: false,
  count: 0,
  search: '',
  setSearch: () => {},
  filter: {},
  setFilter: () => {},
  dirtyFilters: 0,
  setDirtyFilters: () => {},
  cancelAffiliation: () => {},
  isLoadingCancelAffiliation: false,
});

export const MyAffiliationsDefaultFilter = {
  status: [],
};

const MyAffiliationsProvider = ({ children }) => {
  const { enqueueSnackbar } = useSnackbar();
  const table = useTableFilter({
    defaultFilter: MyAffiliationsDefaultFilter,
  });

  const { user } = useAuthContext();

  const queryClient = useQueryClient();

  const {
    data: { results: affiliations = [], count },
    isLoading: fetching,
  } = useQuery({
    queryKey: ['affiliations', table.page, table.search, table.filter, user.email],
    queryFn: () =>
      getAffiliates({
        page: table.page,
        search: table.search,
        limit: table.rowsPerPage,
        status: table.filter.status,
        filter: {
          userEmail: user.email,
        },
        url: 'products/',
      }),
    initialData: {
      results: [],
      count: 0,
    },
    refetchOnMount: true,
  });

  const { mutateAsync: cancelAffiliation, isLoading: isLoadingCancelAffiliation } = useMutation({
    mutationFn: (id) => deleteAffiliateService({ id }),
    async onMutate(id) {
      const queryKey = ['affiliations', table.page, table.search, table.filter, user.email];
      await queryClient.cancelQueries({ queryKey });
      const previousData = queryClient.getQueryData(queryKey);

      const newData = {
        ...previousData,
        count: previousData.count - 1,
        results: previousData.results.map((item) => {
          if (item.short_id === id) {
            return {
              ...item,
              status: 'canceled',
            };
          }

          return item;
        }),
      };

      queryClient.setQueryData(queryKey, newData);

      return { previousData };
    },
    onSuccess() {
      enqueueSnackbar('Afiliação cancelada com sucesso!', {
        variant: 'success',
      });
    },
    onError() {
      queryClient.invalidateQueries([
        'affiliations',
        table.page,
        table.search,
        table.filter,
        user.email,
      ]);
      enqueueSnackbar('Ocorreu um erro ao tentar cancelar a afiliação!', {
        variant: 'error',
      });
    },
  });

  const value = useMemo(
    () => ({
      affiliations,
      fetching,
      count,
      table,
      cancelAffiliation,
      isLoadingCancelAffiliation,
    }),
    [affiliations, count, fetching, table, cancelAffiliation, isLoadingCancelAffiliation]
  );

  return <MyAffiliationsContext.Provider value={value}>{children}</MyAffiliationsContext.Provider>;
};

MyAffiliationsProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default MyAffiliationsProvider;
