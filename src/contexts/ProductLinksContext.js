import { useErrorSnackbar } from '@/components/snackbar/useErrorSnackbar';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useCallback, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as yup from 'yup';
import useTableFilter from '../hooks/useTableFilter';
import {
  createProductLinkService,
  deleteProductLinkService,
  disableProductLinksService,
  enableProductLinksService,
  getProductLinksService,
  hideProductLinkToAffiliatesService,
  showProductLinkToAffiliatesService,
  updateProductLinkService,
} from '../services/product-links';
import { FormScopes } from '../utils/form';

export const ProductLinksContext = createContext({
  table: {},
  form: {},
  links: [],
  fetching: false,
  count: 0,
  copy: () => {},
  create: () => {},
  creating: false,
  update: () => {},
  updating: false,
  disable: () => {},
  disabling: false,
  remove: () => {},
  removing: false,
  showToAffiliates: () => {},
  showingToAffiliates: false,
  hideToAffiliates: () => {},
  hidingToAffiliates: false,
  enable: () => {},
  enabling: false,
  scope: FormScopes.INDEX,
  setScope: () => {},
  link: {},
  setLink: () => {},
  customUTMs: () => {},
  prevUTMLinks: [],
  setPrevUTMLinks: () => {},
});

export const ProductLinkStatus = {
  pending: {
    label: 'Pendente',
    color: 'warning',
    value: 'pending',
  },
  active: {
    label: 'Ativo',
    color: 'success',
    value: 'active',
  },
  disabled: {
    label: 'Desativado',
    color: 'default',
    value: 'disabled',
  },
};

export const ProductLinkTypes = {
  checkout: {
    label: 'Checkout',
    value: 'checkout',
    color: 'info',
  },
  sales: {
    label: 'Página de vendas',
    value: 'sales',
    color: 'success',
  },
};

const ProductLinksProvider = ({ children }) => {
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const { id: product, short_user_id, short_id } = useParams();

  const [scope, setScope] = useState(FormScopes.INDEX);

  const queryClient = useQueryClient();

  const { enqueueSnackbar } = useSnackbar();
  const { appErrorRenderer } = useErrorSnackbar();

  const [link, setLink] = useState({});

  const [prevUTMLinks, setPrevUTMLinks] = useState([]);

  const schema = yup.object().shape({
    id: yup.string(),
    name: yup.string().required('O nome é obrigatório'),
    url: yup.string().url('A URL é inválida').required('A URL é obrigatória'),
  });

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      id: link.id || '',
      name: link.name || '',
      url: link.url || '',
    },
  });

  const customUTMs = useFieldArray({
    control: form.control,
    name: 'customUTMs',
  });

  function getUTMQueryParams(url) {
    const queryParams = {};
    const urlParams = new URLSearchParams(url);

    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of urlParams.entries()) {
      if (key !== url) {
        queryParams[key] = value;
      }
    }

    return queryParams;
  }

  const { data: { results: links = [], count = 0 } = {}, isFetching: fetching } = useQuery({
    queryKey: [
      'links',
      { page: table.page, search: table.search, limit: table.rowsPerPage, product },
    ],
    queryFn: () =>
      getProductLinksService({
        product,
        page: table.page,
        search: table.search,
        limit: table.rowsPerPage,
        short_user_id: short_user_id || short_id,
      }),
    select(data) {
      const baseShortUrl = process.env.REACT_APP_SHORT_URL.replace(/\/+$/, '');
      return {
        ...data,
        results: data.results.map((result) => {
          const utmParams = getUTMQueryParams(result.url.replace(/^.*\?/, ''));
          return {
            ...result,
            shortUrl: short_id
              ? `${baseShortUrl}/${result.shortId}/?affiliate=${short_id}`
              : `${baseShortUrl}/${result.shortId}/`,
            utmParams,
          };
        }),
      };
    },
    initialData: {
      results: [],
      count: 0,
    },
  });

  const { mutateAsync: create, isLoading: creating } = useMutation({
    mutationFn: (info) => createProductLinkService({ product, ...info }),
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('Link criado com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
    onSettled() {
      setLink({});
    },
  });

  const { mutateAsync: update, isLoading: updating } = useMutation({
    mutationFn: (info) => updateProductLinkService({ product, ...info }),
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('Dados atualizados com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
    onSettled() {
      setLink({});
    },
  });

  const { mutateAsync: showToAffiliates, isLoading: showingToAffiliates } = useMutation({
    mutationFn: showProductLinkToAffiliatesService,
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('O link foi exibido para os afiliados!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: hideToAffiliates, isLoading: hidingToAffiliates } = useMutation({
    mutationFn: hideProductLinkToAffiliatesService,
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('O link foi ocultado para os afiliados!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: enable, isLoading: enabling } = useMutation({
    mutationFn: enableProductLinksService,
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('O(s) link(s) foi(ram) habilitado(s) com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      console.log('error: ', error);
      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: disable, isLoading: disabling } = useMutation({
    mutationFn: disableProductLinksService,
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('O(s) link(s) foi(ram) desabilitado(s) com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      console.log('error: ', error);
      appErrorRenderer({ error });
    },
  });

  const { mutateAsync: remove, isLoading: removing } = useMutation({
    mutationFn: () => deleteProductLinkService({ id: link.id }),
    onSuccess: () => {
      queryClient.invalidateQueries(['links']);

      enqueueSnackbar('Link removido com sucesso!', { variant: 'success' });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      appErrorRenderer({ error });
    },
  });

  const copy = useCallback(
    ({ url }) => {
      navigator.clipboard.writeText(url);
      enqueueSnackbar('Link copiado para a área de transferência!', { variant: 'success' });
    },
    [enqueueSnackbar]
  );

  const value = useMemo(
    () => ({
      table,
      form,
      links,
      fetching,
      count,
      copy,
      update,
      updating,
      create,
      creating,
      remove,
      removing,
      showToAffiliates,
      showingToAffiliates,
      hideToAffiliates,
      hidingToAffiliates,
      enable,
      enabling,
      disable,
      disabling,
      scope,
      setScope,
      link,
      setLink,
      customUTMs,
      prevUTMLinks,
      setPrevUTMLinks,
    }),
    [
      table,
      form,
      links,
      fetching,
      count,
      copy,
      update,
      updating,
      create,
      creating,
      remove,
      removing,
      showToAffiliates,
      showingToAffiliates,
      hideToAffiliates,
      hidingToAffiliates,
      enable,
      enabling,
      disable,
      disabling,
      scope,
      link,
      customUTMs,
      prevUTMLinks,
      setPrevUTMLinks,
    ]
  );

  return <ProductLinksContext.Provider value={value}>{children}</ProductLinksContext.Provider>;
};

ProductLinksProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ProductLinksProvider;
