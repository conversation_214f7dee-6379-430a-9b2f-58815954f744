import { useParams } from 'react-router';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import { createContext, useContext, useMemo, useState } from 'react';
import useTableFilter from '../hooks/useTableFilter';
import {
  createOrderBump,
  deleteOrderBump,
  updateBump,
  orderBumpsPosition,
} from '../services/order-bumps';
import { getProductService } from '../services/products';
import { FormScopes } from '../utils/form';
import { EditProductContext } from './EditProductContext';

export const ProductBumpsContext = createContext({
  table: {},
  bump: {},
  setBump: () => {},
  create: () => {},
  creating: false,
  update: () => {},
  updating: false,
  remove: () => {},
  removing: false,
  scope: FormScopes.INDEX,
  setScope: () => {},
  mutateLoadProduct: () => {},
  bumps: [],
  order: () => {},
});

const ProductBumpsProvider = ({ children }) => {
  const { product } = useContext(EditProductContext);
  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const queryClient = useQueryClient();

  const [scope, setScope] = useState(FormScopes.INDEX);

  const [bump, setBump] = useState({});

  const { enqueueSnackbar } = useSnackbar();

  const { id: productId } = useParams();

  const productQueryKey = ['product', { id: productId }];

  const { bumps } = queryClient.getQueryData(productQueryKey);

  const { mutateAsync: create, isLoading: creating } = useMutation({
    mutationFn: (data) => createOrderBump({ data, productId: product?.id }),
    onSuccess: async () => {
      queryClient.invalidateQueries(productQueryKey);
      enqueueSnackbar('Bump adicionado com sucesso!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: (error) => {
      const errMessage = error.response.data.product[0];
      queryClient.invalidateQueries(productQueryKey);
      enqueueSnackbar(errMessage || 'Não foi possível adicionar o Bump!', {
        variant: 'error',
      });
    },
  });

  const { mutateAsync: update, isLoading: updating } = useMutation({
    mutationFn: (data) => updateBump({ ...data, id: bump.externalId }),
    onSuccess: () => {
      queryClient.invalidateQueries(productQueryKey);
      enqueueSnackbar('Bump atualizado com sucesso!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: () => {
      queryClient.invalidateQueries(productQueryKey);
      enqueueSnackbar('Não foi possível atualizar o Bump!', {
        variant: 'error',
      });
    },
  });

  const { mutateAsync: remove, isLoading: removing } = useMutation({
    mutationFn: () => deleteOrderBump({ bumpId: bump.externalId }),
    async onMutate() {
      await queryClient.cancelQueries({ queryKey: productQueryKey });

      const previousData = queryClient.getQueryData(productQueryKey);

      const newData = {
        ...previousData,
        bumps: previousData.bumps.filter(({ id }) => id !== bump.externalId),
      };

      queryClient.setQueryData(productQueryKey, newData);

      return { previousData };
    },
    onSuccess: () => {
      enqueueSnackbar('Bump removido com sucesso!', {
        variant: 'success',
      });

      setScope(FormScopes.INDEX);
    },
    onError: () => {
      queryClient.invalidateQueries(productQueryKey);
      enqueueSnackbar('Não foi possível remover o Bump!', {
        variant: 'error',
      });
    },
  });

  const { mutateAsync: mutateLoadProduct } = useMutation({
    mutationFn: async (id) => {
      const cachedProduct = queryClient.getQueryData(['product', id]);

      if (cachedProduct) return cachedProduct;

      const result = await getProductService({ id });

      queryClient.setQueryData(['product', id], result);

      return result;
    },
  });

  const { mutate: order } = useMutation({
    mutationFn: (data) => orderBumpsPosition({ productId, bumps: data }),
    async onMutate(data) {
      await queryClient.cancelQueries({ queryKey: productQueryKey });
      const previousData = queryClient.getQueryData(productQueryKey);
      const newData = {
        ...previousData,
        bumps: previousData.bumps.map((item) => ({
          ...item,
          position: data.find((itm) => itm.id === item.id)?.position,
        })),
      };
      queryClient.setQueryData(productQueryKey, newData);
      return { previousData };
    },
    onSuccess(data) {
      enqueueSnackbar(data.detail, {
        variant: 'success',
      });
    },
    onError() {
      enqueueSnackbar('Não foi possível alterar a ordem dos Bumps!', {
        variant: 'error',
      });
      queryClient.invalidateQueries(productQueryKey);
    },
  });

  const value = useMemo(
    () => ({
      table,
      create,
      creating,
      update,
      updating,
      remove,
      removing,
      scope,
      setScope,
      bump,
      setBump,
      mutateLoadProduct,
      bumps,
      order,
    }),
    [
      table,
      create,
      creating,
      update,
      updating,
      remove,
      removing,
      scope,
      bump,
      mutateLoadProduct,
      bumps,
      order,
    ]
  );

  return <ProductBumpsContext.Provider value={value}>{children}</ProductBumpsContext.Provider>;
};

ProductBumpsProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ProductBumpsProvider;
