import { Helmet } from 'react-helmet-async';
import { Link as RouterLink, useLocation, useParams } from 'react-router-dom';
// @mui
import { Link, Typography } from '@mui/material';
// routes
import { PATH_AUTH } from '../../routes/paths';
// components
import Iconify from '../../components/iconify';
// sections
import AuthNewPasswordForm from '../../sections/auth/AuthNewPasswordForm';
// assets
import { SentIcon } from '../../assets/icons';

// ----------------------------------------------------------------------

export default function NewPasswordPage() {
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const code = queryParams.get('code') ?? 0;
  console.log(code);

  return (
    <>
      <Helmet>
        <title> Nova Senha </title>
      </Helmet>

      <SentIcon sx={{ mb: 5, height: 96 }} />

      <Typography variant="h3" paragraph>
        Pedido enviado com sucesso!
      </Typography>

      <Typography sx={{ color: 'text.secondary', mb: 5 }}>
        Enviamos um e-mail de confirmação de 6 dígitos para o seu e-mail. Digite o código na caixa
        abaixo para verificar seu e-mail.
      </Typography>

      <AuthNewPasswordForm defaultValue={code} disbledCode={code !== 0} />

      <Typography variant="body2" sx={{ my: 3 }}>
        Não tem um código? &nbsp;
        <Link variant="subtitle2">Reenviar código</Link>
      </Typography>

      <Link
        component={RouterLink}
        to={PATH_AUTH.login}
        color="inherit"
        variant="subtitle2"
        sx={{
          mx: 'auto',
          alignItems: 'center',
          display: 'inline-flex',
        }}
      >
        <Iconify icon="eva:chevron-left-fill" width={16} />
        Voltar ao login
      </Link>
    </>
  );
}
