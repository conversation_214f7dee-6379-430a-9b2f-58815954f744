import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link as RouterLink, useSearchParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
// @mui
import { Link, Typography } from '@mui/material';
// routes
import { PATH_AUTH } from '../../routes/paths';
// components
import Iconify from '../../components/iconify';
import Loading from '../../components/loading-screen';
// sections
import AuthCreatePassword from '../../sections/auth/AuthCreatePasswordForm/AuthCreatePasswordForm';
// services
import { getOtpToken } from '../../services/auth';
// utils
import { isValidToken } from '../../auth/utils';
// auth
import { useAuthContext } from '../../auth/useAuthContext';

export default function CreatePasswordPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthContext();

  const { isLoading } = useQuery({
    queryFn: () => getOtpToken(searchParams.get('token')),
    queryKey: ['otpAccessToken'],
    async select(data) {
      if (data?.redirect) {
        if (isAuthenticated) {
          navigate('/');

          return data;
        }

        navigate('/auth/login');

        return data;
      }

      const { access } = data;

      if (!access) {
        navigate('/auth/login');

        return data;
      }

      const isValid = await isValidToken(access);

      if (!isValid) {
        navigate('/auth/login');

        return data;
      }

      return data;
    },
    onError() {
      navigate('/auth/login');
    },
  });

  useEffect(() => {
    navigate('/auth/create-password/');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Helmet>
        <title> Criar Senha </title>
      </Helmet>

      <picture
        style={{
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <img
          srcSet="/logo/cakto-logo.png"
          alt="logo"
          style={{
            objectFit: 'cover',
            maxWidth: '100%',
            height: '150px',
          }}
        />
      </picture>

      <Typography variant="h3" paragraph>
        Criar senha
      </Typography>

      <Typography sx={{ color: 'text.secondary', mb: 5 }}>
        Crie sua senha para acessar a Cakto.
      </Typography>

      <GoogleReCaptchaProvider
        reCaptchaKey={process.env.REACT_APP_RECAPTCHA_KEY}
        language="pt-BR"
        useRecaptchaNet={false}
        useEnterprise
        container={{
          parameters: {
            theme: 'light',
          },
        }}
      >
        <AuthCreatePassword />
      </GoogleReCaptchaProvider>

      <Link
        component={RouterLink}
        to={PATH_AUTH.login}
        color="inherit"
        variant="subtitle2"
        sx={{
          mx: 'auto',
          alignItems: 'center',
          display: 'inline-flex',
          mt: 4,
        }}
      >
        <Iconify icon="eva:chevron-left-fill" width={16} />
        Voltar ao login
      </Link>

      {isLoading && <Loading />}
    </>
  );
}
