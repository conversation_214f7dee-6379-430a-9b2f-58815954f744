import { Helmet } from 'react-helmet-async';
// sections
import { GoogleOAuthProvider } from '@react-oauth/google';
import Login from '../../sections/auth/Login/Login';
// import Login from '../../sections/auth/LoginAuth0';

// ----------------------------------------------------------------------

export default function LoginPage() {
  return (
    <>
      <Helmet>
        <title> Entrar </title>
      </Helmet>

      <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID}>
        <Login />
      </GoogleOAuthProvider>
    </>
  );
}
