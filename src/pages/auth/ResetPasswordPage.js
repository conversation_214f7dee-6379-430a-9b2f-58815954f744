import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { Helmet } from 'react-helmet-async';
import { Link as RouterLink } from 'react-router-dom';
// @mui
import { Link, Typography } from '@mui/material';
// routes
import { PATH_AUTH } from '../../routes/paths';
// components
import Iconify from '../../components/iconify';
// sections
import AuthResetPasswordForm from '../../sections/auth/AuthResetPasswordForm';
// assets
import { PasswordIcon } from '../../assets/icons';

// ----------------------------------------------------------------------

export default function ResetPasswordPage() {
  return (
    <>
      <Helmet>
        <title> Redefinir Senha </title>
      </Helmet>

      <PasswordIcon sx={{ mb: 5, height: 96 }} />

      <Typography variant="h3" paragraph>
        Esqueceu sua senha?
      </Typography>

      <Typography sx={{ color: 'text.secondary', mb: 5 }}>
        Digite o endereço de e-mail associado à sua conta e enviaremos um link para redefinir sua senha.
      </Typography>

      <GoogleReCaptchaProvider
        reCaptchaKey={process.env.REACT_APP_RECAPTCHA_KEY}
        language="pt-BR"
        useRecaptchaNet={false}
        useEnterprise
        container={{
          parameters: {
            theme: 'light',
          },
        }}
      >
        <AuthResetPasswordForm />
      </GoogleReCaptchaProvider>

      <Link
        component={RouterLink}
        to={PATH_AUTH.login}
        color="inherit"
        variant="subtitle2"
        sx={{
          mt: 3,
          mx: 'auto',
          alignItems: 'center',
          display: 'inline-flex',
        }}
      >
        <Iconify icon="eva:chevron-left-fill" width={16} />
        Voltar ao login
      </Link>
    </>
  );
}
