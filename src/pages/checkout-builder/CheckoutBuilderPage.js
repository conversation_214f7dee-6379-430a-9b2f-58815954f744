import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Badge, Box, Button, IconButton, LinearProgress, ToggleButton } from '@mui/material';
import { Stack, alpha } from '@mui/system';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { useSnackbar } from 'notistack';
import { memo, useContext, useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import { Context, Preview } from 'react-dnd-preview';
import { createPortal } from 'react-dom';
import { Helmet } from 'react-helmet-async';
import { useForm, useWatch } from 'react-hook-form';
import { v4 } from 'uuid';
import * as yup from 'yup';
import LazyPortal from '@/components/dom/LazyPortal';
import FormProvider from '@/components/hook-form/FormProvider';
import RHFToggleButtonGroup from '@/components/hook-form/RHFToggleButtonGroup';
import Iconify from '@/components/iconify';
import { CheckoutBuilderContext } from '@/contexts/CheckoutBuilderContext';
import { SelectedElementContext, SelectedElementProvider } from '@/contexts/SelectedElementContext';
import CheckoutBuilderChatComponent from '@/sections/@checkout-builder/components/CheckoutBuilderChatComponent';
import CheckoutBuilderComponentCard from '@/sections/@checkout-builder/components/CheckoutBuilderComponentCard';
import CheckoutBuilderComponentsMenuDrawer from '@/sections/@checkout-builder/components/CheckoutBuilderComponentsMenuDrawer';
import CheckoutBuilderExitPopUpComponent from '@/sections/@checkout-builder/components/CheckoutBuilderExitPopUpComponent';
import CheckoutBuilderNotificationComponent from '@/sections/@checkout-builder/components/CheckoutBuilderNotificationComponent';
import CheckoutBuilderPanel from '@/sections/@checkout-builder/components/CheckoutBuilderPanel';
import { CheckoutBuilderRowCard } from '@/sections/@checkout-builder/components/CheckoutBuilderRowCard';
import {
  CHECKOUT_BUILDER_DRAWER_WIDTH,
  CheckoutBuilderComponentTypes,
  CheckoutBuilderDeviceTypes,
  CheckoutBuilderFontFamilies,
  lightThemeValues,
} from '@/sections/@checkout-builder/constants';
import CheckoutBuilderAdvantageComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderAdvantageComponentForm';
import CheckoutBuilderChatComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderChatComponentForm';
import CheckoutBuilderCountdownComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderCountdownComponentForm';
import CheckoutBuilderExitPopUpComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderExitPopUpComponentForm';
import CheckoutBuilderFacebookComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderFacebookComponentForm';
import CheckoutBuilderFormDrawer from '@/sections/@checkout-builder/form/CheckoutBuilderFormDrawer';
import CheckoutBuilderHeaderComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderHeaderComponentForm';
import CheckoutBuilderImageComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderImageComponentForm';
import CheckoutBuilderListComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderListComponentForm';
import CheckoutBuilderMapComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderMapComponentForm';
import CheckoutBuilderNotificationComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderNotificationComponentForm';
import CheckoutBuilderRowComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderRowComponentForm';
import CheckoutBuilderSealComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderSealComponentForm';
import CheckoutBuilderTestimonialComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderTestimonialComponentForm';
import CheckoutBuilderTextComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderTextComponentForm';
import CheckoutBuilderVideoComponentForm from '@/sections/@checkout-builder/form/CheckoutBuilderVideoComponentForm';
import useDragAndDropRows from '@/sections/@checkout-builder/hooks/useDragAndDropRowsHook';
import CheckoutPreview from '@/sections/@checkout-builder/preview/CheckoutPreview';
import { FormScopes } from '@/utils/form';
import useResponsive from '@/hooks/useResponsive';

const defaultValuesDesktop = {
  settings: {
    ...lightThemeValues,
  },
  extra: {
    exitPopup: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.exitPopup.id,
      attributes: CheckoutBuilderComponentTypes.exitPopup.attributes,
    },
    notification: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.notification.id,
      attributes: CheckoutBuilderComponentTypes.notification.attributes,
    },
    chat: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.chat.id,
      attributes: CheckoutBuilderComponentTypes.chat.attributes,
    },
  },
  rows: [
    {
      id: v4(),
      layout: [12],
      type: 'row',
      columns: [
        {
          id: v4(),
          type: 'column',
          components: [],
        },
      ],
    },
    {
      id: v4(),
      layout: [12],
      type: 'row',
      columns: [
        {
          id: v4(),
          type: 'column',
          components: [
            {
              id: v4(),
              type: CheckoutBuilderComponentTypes.checkout.id,
              attributes: CheckoutBuilderComponentTypes.checkout.attributes,
            },
          ],
        },
        {
          id: v4(),
          type: 'column',
          components: [],
        },
      ],
    },
  ],
};

const defaultValuesMobile = {
  settings: {
    ...lightThemeValues,
  },
  extra: {
    exitPopup: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.exitPopup.id,
      attributes: CheckoutBuilderComponentTypes.exitPopup.attributes,
    },
    notification: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.notification.id,
      attributes: CheckoutBuilderComponentTypes.notification.attributes,
    },
    chat: {
      id: v4(),
      type: CheckoutBuilderComponentTypes.chat.id,
      attributes: CheckoutBuilderComponentTypes.chat.attributes,
    },
  },
  rows: [
    {
      id: v4(),
      layout: [12],
      type: 'row',
      columns: [
        {
          id: v4(),
          type: 'column',
          components: [],
        },
      ],
    },
    {
      id: v4(),
      layout: [12],
      type: 'row',
      columns: [
        {
          id: v4(),
          type: 'column',
          components: [
            {
              id: v4(),
              type: CheckoutBuilderComponentTypes.checkout.id,
              attributes: CheckoutBuilderComponentTypes.checkout.attributes,
            },
          ],
        },
      ],
    },
  ],
};

const defaultValues = {
  device: CheckoutBuilderDeviceTypes.desktop.id,
  desktop: defaultValuesDesktop,
  mobile: defaultValuesMobile,
};

CheckoutBuilderPage.propTypes = {
  checkout: PropTypes.object,
};

function CheckoutBuilderPage({ checkout }) {
  const ColumnSchema = yup.object().shape({
    id: yup.string().required('O campo id da coluna é obrigatório'),
    type: yup.string().oneOf(['column']).required(),
    components: yup.array().of(
      yup.object().shape({
        id: yup.string().required('O campo id do dos componentes da coluna é obrigatório'),
        type: yup.string().required('O campo tipo do dos componentes da coluna é obrigatório'),
        attributes: yup
          .object()
          .when('type', (type, schema) =>
            CheckoutBuilderComponentTypes[type]?.schema
              ? CheckoutBuilderComponentTypes[type]?.schema
              : yup.object()
          ),
      })
    ),
  });

  const RowSchema = yup.object().shape({
    id: yup.string().required('O campo id da linha é obrigatório'),
    type: yup.string().oneOf(['row']).required('O campo tipo da linha é obrigatório'),
    layout: yup.array().of(yup.number().required('O campo layout da linha é obrigatório')),
    columns: yup.array().of(ColumnSchema).required('O campo coluna da linha é obrigatório'),
  });

  const schema = yup.object().shape({
    settings: yup.object().shape({
      background: yup.object().shape({
        color: yup.string().required(),
        image: yup.object().nullable(),
        fixed: yup.boolean().required(),
        repeat: yup.boolean().required(),
        cover: yup.boolean().required(),
      }),
      font: yup.object().shape({
        family: yup.string().oneOf(CheckoutBuilderFontFamilies).required(),
      }),
    }),
    extra: yup.object().shape({
      exitPopup: yup.object().shape({
        id: yup.string().required(),
        type: yup.string().required(),
        attributes: CheckoutBuilderComponentTypes.exitPopup.schema,
      }),
      notification: yup.object().shape({
        id: yup.string().required(),
        type: yup.string().required(),
        attributes: CheckoutBuilderComponentTypes.notification.schema,
      }),
      chat: yup.object().shape({
        id: yup.string().required(),
        type: yup.string().required(),
        attributes: CheckoutBuilderComponentTypes.chat.schema,
      }),
    }),
    rows: yup.array().of(RowSchema).required(),
  });

  const form = useForm({
    defaultValues: checkout?.config || defaultValues,
    resolver: yupResolver(
      yup.object().shape({
        device: yup.string().oneOf(Object.keys(CheckoutBuilderDeviceTypes)).required(),
        desktop: schema,
        mobile: schema,
      })
    ),
  });

  const { update, updating } = useContext(CheckoutBuilderContext);

  const [preview, setPreview] = useState(false);

  const { enqueueSnackbar } = useSnackbar();

  const onSubmit = (data) => {
    update({ ...checkout, config: data });
  };

  function extractErrors() {
    const config = form?.formState?.errors;
    function traverseAndCollectErrors(key, value, prefix) {
      return Object.keys(value).reduce((acc, k) => {
        if (k === 'message') {
          acc.push(`${prefix}: ${value[k]}`);
        } else if (typeof value[k] === 'object' && value[k] !== null) {
          acc = acc.concat(traverseAndCollectErrors(k, value[k], prefix));
        }
        return acc;
      }, []);
    }

    let errors = [];

    if (config.desktop) {
      errors = errors.concat(
        traverseAndCollectErrors('desktop', config.desktop, 'Erro na configuração do Desktop')
      );
    }

    if (config.mobile) {
      errors = errors.concat(
        traverseAndCollectErrors('mobile', config.mobile, 'Erro na configuração do Mobile')
      );
    }

    return errors;
  }

  const isMobile = useResponsive('down', 'sm');

  useEffect(() => {
    if (Object.keys(form.formState.errors)?.length) {
      const errors = extractErrors();

      errors.forEach((error) => {
        enqueueSnackbar(error, { variant: 'error' });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form?.formState?.errors]);

  return (
    <>
      <Helmet>
        <title>Checkout Builder</title>
      </Helmet>

      <FormProvider
        methods={form}
        id="checkout-builder-form"
        onSubmit={form.handleSubmit(onSubmit, async () => {
          const isValid = await form.trigger();

          if (isValid) {
            onSubmit(form.getValues());
          }
        })}
      >
        <SelectedElementProvider>
          <LazyPortal id="checkout-builder-header-actions">
            <Stack direction="row" gap={1} alignItems="center">
              <Box>
                <DeviceToggleButtons />
              </Box>
              {!isMobile && <CopyComponentsFromDesktopToMobileButton />}
            </Stack>
          </LazyPortal>
          <LazyPortal id="checkout-builder-header-buttons">
            <Stack direction="column" gap={2}>
              <Stack direction="row" gap={1} alignItems="center">
                {preview ? (
                  <IconButton
                    onClick={() => {
                      setPreview(false);
                    }}
                  >
                    <Iconify icon="mdi:close" />
                  </IconButton>
                ) : (
                  <>
                    <Badge variant="dot" color="warning">
                      <LoadingButton
                        variant="contained"
                        color="primary"
                        type="submit"
                        form="checkout-builder-form"
                        loading={updating}
                      >
                        Salvar
                      </LoadingButton>
                    </Badge>
                    <Button
                      variant="outlined"
                      color="inherit"
                      endIcon={<Iconify icon="mdi:eye" />}
                      onClick={() => {
                        setPreview(true);
                      }}
                    >
                      Preview
                    </Button>
                  </>
                )}
              </Stack>
              {isMobile && <CopyComponentsFromDesktopToMobileButton />}
            </Stack>
          </LazyPortal>
          {!isMobile && <ExtraComponentsPreview />}
          <CheckoutPreview open={preview} onClose={() => setPreview(false)} />
          <DndProvider backend={isMobile ? TouchBackend : HTML5Backend}>
            <Box p={2} id="checkout-builder-panel">
              <CheckoutBuilderPanel />
            </Box>
            <Preview>
              <Context.Consumer>
                {({ itemType, item, style }) =>
                  createPortal(
                    <Box
                      sx={{
                        '& *': {
                          zIndex: (theme) => `${theme.zIndex.tooltip + 1} !important`,
                        },
                      }}
                    >
                      {['new-component', 'component'].includes(itemType) && (
                        <CheckoutBuilderComponentCard
                          style={style}
                          config={CheckoutBuilderComponentTypes[item.type]}
                          sx={{
                            width: 150,
                            height: 110,
                          }}
                        />
                      )}
                      {['row', 'new-row'].includes(itemType) && (
                        <CheckoutBuilderRowCard
                          draggable={false}
                          style={style}
                          layout={item.layout}
                          sx={{
                            width: 308,
                            p: 1,
                          }}
                        />
                      )}
                    </Box>,
                    document.body
                  )
                }
              </Context.Consumer>
            </Preview>

            <CheckoutBuilderDrawer />
          </DndProvider>
        </SelectedElementProvider>
      </FormProvider>
    </>
  );
}

const ExtraComponentsPreview = memo(() => {
  const { selectedID, setSelectedID } = useContext(SelectedElementContext);

  const { setScope } = useContext(CheckoutBuilderContext);

  const components = {
    [CheckoutBuilderComponentTypes.exitPopup.id]: CheckoutBuilderExitPopUpComponent,
    [CheckoutBuilderComponentTypes.notification.id]: CheckoutBuilderNotificationComponent,
    [CheckoutBuilderComponentTypes.chat.id]: CheckoutBuilderChatComponent,
  };

  const Component = components[selectedID] || (() => null);

  const handleClose = () => {
    setSelectedID(null);
    setScope(null);
  };

  const variants = {
    open: {
      display: 'flex',
      opacity: 1,
    },
    closed: {
      opacity: 0,
    },
    hidden: {
      display: 'none',
    },
  };

  return (
    <Stack
      component={motion.div}
      variants={variants}
      initial="hidden"
      animate={selectedID in components ? 'open' : 'closed'}
      exit="hidden"
      transition={{ duration: 0.5 }}
      height="calc(100% - 64px)"
      width={`calc(100% - ${CHECKOUT_BUILDER_DRAWER_WIDTH}px)`}
      position="fixed"
      sx={{
        zIndex: (theme) => `${theme.zIndex.tooltip + 1} !important`,
        bgcolor: (theme) => alpha(theme.palette.grey[900], 0.5),
      }}
      justifyContent="center"
      alignItems="center"
      onClick={handleClose}
    >
      <Component onClose={handleClose} />
    </Stack>
  );
});

const CheckoutBuilderDrawer = memo(() => {
  const { selected, selectedID } = useContext(SelectedElementContext);

  const Forms = {
    [CheckoutBuilderComponentTypes.text.id]: CheckoutBuilderTextComponentForm,
    [CheckoutBuilderComponentTypes.countdown.id]: CheckoutBuilderCountdownComponentForm,
    [CheckoutBuilderComponentTypes.header.id]: CheckoutBuilderHeaderComponentForm,
    [CheckoutBuilderComponentTypes.row.id]: CheckoutBuilderRowComponentForm,
    [CheckoutBuilderComponentTypes.advantage.id]: CheckoutBuilderAdvantageComponentForm,
    [CheckoutBuilderComponentTypes.list.id]: CheckoutBuilderListComponentForm,
    [CheckoutBuilderComponentTypes.testimonial.id]: CheckoutBuilderTestimonialComponentForm,
    [CheckoutBuilderComponentTypes.seal.id]: CheckoutBuilderSealComponentForm,
    [CheckoutBuilderComponentTypes.image.id]: CheckoutBuilderImageComponentForm,
    [CheckoutBuilderComponentTypes.video.id]: CheckoutBuilderVideoComponentForm,
    [CheckoutBuilderComponentTypes.facebook.id]: CheckoutBuilderFacebookComponentForm,
    [CheckoutBuilderComponentTypes.map.id]: CheckoutBuilderMapComponentForm,
    [CheckoutBuilderComponentTypes.exitPopup.id]: CheckoutBuilderExitPopUpComponentForm,
    [CheckoutBuilderComponentTypes.notification.id]: CheckoutBuilderNotificationComponentForm,
    [CheckoutBuilderComponentTypes.chat.id]: CheckoutBuilderChatComponentForm,
  };

  return (
    <>
      <CheckoutBuilderComponentsMenuDrawer />
      <CheckoutBuilderFormDrawer key={selectedID}>
        <Box>{selected && Forms[selected?.type] && Forms[selected?.type]({ selected })}</Box>
      </CheckoutBuilderFormDrawer>
    </>
  );
});

const DeviceToggleButtons = () => {
  const { setSelectedID } = useContext(SelectedElementContext);

  const { setScope } = useContext(CheckoutBuilderContext);

  return (
    <RHFToggleButtonGroup
      name="device"
      exclusive
      sx={{
        border: 'none',
      }}
      color="primary"
      defaultValue={defaultValuesDesktop.device}
    >
      {Object.values(CheckoutBuilderDeviceTypes).map(({ id, icon }) => (
        <ToggleButton
          value={id}
          aria-label={id}
          onClick={({ _, value }) => {
            setScope(FormScopes.INDEX);
            setSelectedID(null);
          }}
        >
          <Iconify icon={icon} />
        </ToggleButton>
      ))}
    </RHFToggleButtonGroup>
  );
};

const CopyComponentsFromDesktopToMobileButton = () => {
  const device = useWatch({
    name: 'device',
  });

  const { copyComponentFromDesktopToMobile } = useDragAndDropRows();

  const isMobile = useResponsive('down', 'sm');

  return (
    device === CheckoutBuilderDeviceTypes.mobile.id && (
      <Button
        variant="contained"
        onClick={() => {
          copyComponentFromDesktopToMobile();
        }}
        size={isMobile ? 'medium' : 'small'}
        startIcon={<Iconify icon="mdi:content-copy" />}
      >
        Copiar do Desktop
      </Button>
    )
  );
};

export default () => {
  const { checkout, loading } = useContext(CheckoutBuilderContext);

  return (
    <Stack>
      {loading && <LinearProgress />}
      {checkout && (
        <CheckoutBuilderPage
          checkout={{
            ...checkout,
            config: {
              ...(checkout?.config || defaultValues),
              device: CheckoutBuilderDeviceTypes.desktop.id,
            },
          }}
        />
      )}
    </Stack>
  );
};
