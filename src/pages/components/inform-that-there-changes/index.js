import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router';

InformThatThereChanges.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default function InformThatThereChanges({ open, onClose }) {
  const navigate = useNavigate();

  const removeChanges = () => {
    navigate('/dashboard/products?tab=products');
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Existem alterações pendentes</DialogTitle>
      <DialogContent>
        <DialogContentText>Caso você volte perderá as suas alterações.</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={removeChanges} color="primary">
          Remover Alterações
        </Button>
        <Button variant="contained" onClick={onClose}>
          Continuar Edição
        </Button>
      </DialogActions>
    </Dialog>
  );
}
