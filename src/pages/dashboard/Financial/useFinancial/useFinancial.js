import { useQuery } from '@tanstack/react-query';
import { getBalance, getPixKey } from '@/services/financial';

export default function useFinancial() {
  const {
    isFetching: fetchingBalance,
    data: { balance, antecipateAmount, pending },
  } = useQuery({
    queryKey: ['balance'],
    queryFn: getBalance,
    initialData: {
      balance: 0,
      pending: 0,
      antecipate0Amount: 0,
    },
  });

  useQuery({
    queryKey: ['pix'],
    queryFn: getPixKey,
    initialData: {
      pix: '',
    },
  });

  return {
    fetchingBalance,
    balance,
    antecipateAmount,
    pending,
  };
}
