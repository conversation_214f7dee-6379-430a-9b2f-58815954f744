import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';
import useFinancial from './useFinancial';

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

describe('useFinancial', () => {
  it.skip('should return the correct values', () => {
    const mockBalanceData = {
      balance: 1000,
      antecipateAmount: 200,
      pending: 300,
    };

    const mockPixKeyData = { pix: 'f8b3f95c-2d1a-4d7a-a3d2-4e3a2b7c8d1f' };

    useQuery.mockImplementation(({ queryKey, queryFn, initialData, select }) => {
      queryFn = jest.fn();
      if (queryKey[0] === 'balance') {
        initialData = {
          balance: 0,
          pending: 0,
          antecipate0Amount: 0,
        };
        return { isFetching: false, data: mockBalanceData };
      }
      if (queryKey[0] === 'fee') {
        const mockFeeData = {
          creditCardFixed: 1.5,
          creditCardPercentage: 2.5,
          creditCardRelease: 30,
          ticketFixed: 1.0,
          ticketPercentage: 2.0,
          ticketRelease: 10,
          pixFixed: 0.5,
          pixPercentage: 1.0,
          pixRelease: 5,
          antecipationPercentage: 1.5,
          picpayFixed: 1,
          picpayPercentage: 2,
          picpayRelease: 3,
        };

        const data = select(mockFeeData);

        return {
          data,
        };
      }
      if (queryKey[0] === 'pix') {
        return { data: mockPixKeyData };
      }
      return {};
    });

    const { result } = renderHook(() => useFinancial(), { wrapper: global.wrapper });
    expect(result.current.fetchingBalance).toBe(false);
    expect(result.current.balance).toBe(1000);
    expect(result.current.antecipateAmount).toBe(200);
    expect(result.current.pending).toBe(300);
    // expect(result.current.fee).toEqual({
    //   card: {
    //     fixed: 1.5,
    //     percentage: 2.5,
    //     release: 30,
    //   },
    //   ticket: {
    //     fixed: 1.0,
    //     percentage: 2.0,
    //     release: 10,
    //   },
    //   pix: {
    //     fixed: 0.5,
    //     percentage: 1.0,
    //     release: 5,
    //   },
    //   picpay: {
    //     fixed: 1,
    //     percentage: 2,
    //     release: 3,
    //   },
    //   antecipationPercentage: 1.5,
    // });
  });
});
