import { describe, beforeEach, jest, expect } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { HelmetProvider } from 'react-helmet-async';
import { MemoryRouter } from 'react-router-dom';
import Financial from './Financial';
import { useAuthContext } from '@/auth/useAuthContext';
import * as useFinancial from './useFinancial/useFinancial';

jest.mock('@/auth/useAuthContext');

jest.mock('@/sections/@dashboard/withdrawals/WithdrawalTable', () => () => (
  <div data-testid="mocked-withdrawal-table">Mocked Withdrawal Table</div>
));

jest.mock('@/sections/@dashboard/withdrawals/WithdrawalForm', () => {
  const Dialog = require('@mui/material/Dialog').default;

  return ({ antecipation, minAmount, balance, open, onClose }) => (
    <Dialog open={open} onClose={onClose}>
      <div>
        <h2 id="withdrawal-modal-title">Withdrawal Form</h2>
        <p id="withdrawal-modal-description">
          Anticipation: {antecipation ? 'Yes' : 'No'}, Min Amount: {minAmount}, Balance: {balance}
        </p>
        <button onClick={onClose}>Close Modal</button>
      </div>
    </Dialog>
  );
});

jest.mock('@/contexts/IdentityVerificationContext', () => ({ children }) => <div>{children}</div>);

jest.mock('@/sections/@dashboard/withdrawals/WithdrawalTable', () => () => (
  <div data-testid="mocked-withdrawal-table">Mocked Withdrawal Table</div>
));

jest.mock('@/sections/@dashboard/financial/BankData', () => () => (
  <div data-testid="mocked-bank-data">Mocked Bank Data</div>
));

jest.mock('@/sections/@dashboard/identity-verification/IdentityVerificationWarning', () => () => (
  <div data-testid="mocked-identity-verification-warning">Mocked Identity Verification Warning</div>
));

jest.mock('@/sections/@dashboard/identity-verification/IdentityVerificationStepper', () => () => (
  <div data-testid="mocked-identity-verification-stepper">Mocked Identity Verification Stepper</div>
));

const Component = () => (
  <MemoryRouter>
    <HelmetProvider>
      <Financial />
    </HelmetProvider>
  </MemoryRouter>
);

describe('Financial', () => {
  let useFinancialSpy;

  const defaultUseFinancialSpyValues = {
    antecipateAmount: 100,
    balance: 500,
    fetchingBalance: false,
    pending: 200,
    fee: {
      card: { percentage: 2.5, fixed: 0.3, release: 30 },
      ticket: { percentage: 1.5, fixed: 0.2, release: 1 },
      pix: { percentage: 1.0, fixed: 0.1, release: 0 },
    },
  };

  beforeEach(() => {
    useFinancialSpy = jest.spyOn(useFinancial, 'default');
    useFinancialSpy.mockReturnValue(defaultUseFinancialSpyValues);

    useAuthContext.mockReturnValue({
      user: {
        companyStatus: 'approved',
        totalSales: 100,
      },
    });
  });

  it('should render Financial component with main elements', () => {
    render(<Component />);

    expect(screen.getByText('Financeiro')).toBeInTheDocument();

    expect(screen.getByText('Saldo Disponível')).toBeInTheDocument();
    expect(screen.getByText('R$ 500,00')).toBeInTheDocument();
    expect(screen.getByText('Saldo Pendente')).toBeInTheDocument();
    expect(screen.getByText('R$ 300,00')).toBeInTheDocument();

    // expect(screen.getByText('Efetuar Saque')).toBeInTheDocument();
  });

  it.skip('should open and close WithdrawalModal when clicking the button', async () => {
    render(<Component />);

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();

    const saqueButton = screen.getByText('Efetuar Saque');

    fireEvent.click(saqueButton);

    expect(screen.getByRole('dialog')).toBeInTheDocument();

    const closeButton = screen.getByText('Close Modal');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('should go to the tab accourding the company status', () => {
    useAuthContext.mockReturnValue({
      user: {
        companyStatus: 'rejected',
        totalSales: 100,
      },
    });

    render(<Component />);

    expect(screen.getByTestId('mocked-identity-verification-stepper')).toBeInTheDocument();
  });

  it.skip('should go to the tab accourding the tab query param', () => {
    const mockGet = jest.fn().mockReturnValueOnce('fees');
    const spy = jest.spyOn(require('react-router-dom'), 'useSearchParams');
    spy.mockReturnValue([{ get: mockGet }]);

    render(<Component />);

    expect(screen.getByText(/Taxas e Prazos/)).toBeInTheDocument();
  });

  it.skip('should change tabs when clicked', () => {
    render(<Component />);

    expect(screen.getByText('Extrato')).toBeInTheDocument();
    expect(screen.getByText('Dados Bancários')).toBeInTheDocument();
    expect(screen.getByText('Taxas')).toBeInTheDocument();
    expect(screen.getByText('Identidade')).toBeInTheDocument();

    expect(screen.getByTestId('mocked-withdrawal-table')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Dados Bancários'));
    expect(screen.getByTestId('mocked-bank-data')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Taxas'));
    expect(screen.getByText(/Taxas e Prazos/)).toBeInTheDocument();

    fireEvent.click(screen.getByText('Identidade'));
    expect(screen.getByTestId('mocked-identity-verification-stepper')).toBeInTheDocument();
  });

  it.skip('should show the correct fee information based on payment type', () => {
    render(<Component />);

    fireEvent.click(screen.getByText('Taxas'));

    expect(screen.getByText(/2,50%/)).toBeInTheDocument();
    expect(screen.getByText(/\+ R\$ 0,30 por venda aprovada/)).toBeInTheDocument();
    expect(screen.getByText(/Recebimento: 30 dias/)).toBeInTheDocument();

    fireEvent.click(screen.getByText('Boleto'));
    expect(screen.getByText(/1,50%/)).toBeInTheDocument();
    expect(screen.getByText(/\+ R\$ 0,20 por venda aprovada/)).toBeInTheDocument();
    expect(screen.getByText(/Recebimento: 1 dia/)).toBeInTheDocument();

    fireEvent.click(screen.getByText('Pix'));
    expect(screen.getByText(/1%/)).toBeInTheDocument();
    expect(screen.getByText(/\+ R\$ 0,10 por venda aprovada/)).toBeInTheDocument();
    expect(screen.getByText(/Recebimento: Instantâneo/)).toBeInTheDocument();
  });
});
