import BarcodeIcon from '@/assets/icons/BarcodeIcon';
import CreditCardIcon from '@/assets/icons/CreditCardIcon';
import KeyPixIcon from '@/assets/icons/KeyPixIcon';
import SendPixIcon from '@/assets/icons/SendPixIcon';
import WithdrawPixIcon from '@/assets/icons/WithdrawPixIcon';
import { useAuthContext } from '@/auth/useAuthContext';
import { isNewLayoutEnabled } from '@/consts/financial';
import IdentityVerificationProvider from '@/contexts/IdentityVerificationContext';
import {
  PayBilletModal,
  PaymentsCard,
  ReceivePixModal,
  TransferPixModal,
} from '@/sections/@dashboard/financial';
import BankData from '@/sections/@dashboard/financial/BankData';
import IdentityVerificationStepper from '@/sections/@dashboard/identity-verification/IdentityVerificationStepper';
import IdentityVerificationWarning from '@/sections/@dashboard/identity-verification/IdentityVerificationWarning';
import MySalesWidget from '@/sections/@dashboard/my-sales/MySalesWidget';
import WithdrawalModal from '@/sections/@dashboard/withdrawals/WithdrawalForm';
import WithdrawalTable from '@/sections/@dashboard/withdrawals/WithdrawalTable';
import { fCurrency } from '@/utils/formatNumber';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { Alert, Box, Button, Card, Grid, Tab, Typography } from '@mui/material';
import { Container, Stack } from '@mui/system';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';

import useFinancial from './useFinancial/useFinancial';

// ----------------------------------------------------------------------
export default function Financial() {
  const { user } = useAuthContext();

  const { companyStatus } = user;

  const { pathname } = useLocation();

  const [searchParams] = useSearchParams();

  const navigate = useNavigate();

  const tabParam = searchParams.get('tab');

  const { antecipateAmount, balance, fetchingBalance, pending } = useFinancial();
  const pre_approved = companyStatus === 'pre_approved';

  useEffect(() => {
    if (companyStatus !== 'approved') {
      handleTabChange(null, 'identity');
    } else {
      handleTabChange(null, 'extract');
    }

    if (tabParam) handleTabChange(null, tabParam);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [companyStatus]);

  const [tabValue, setTabValue] = useState('extract');

  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  const [isAnticipation, setIsAnticipation] = useState(false);

  const [isOpenReceivePixModal, setIsOpenReceivePixModal] = useState(false);
  const [isOpenTransferPixModal, setIsOpenTransferPixModal] = useState(false);
  const [isOpenPayBilletModal, setIsOpenPayBilletModal] = useState(false);

  const onOpenReceivePixModal = () => setIsOpenReceivePixModal(true);
  const onCloseReceivePixModal = () => setIsOpenReceivePixModal(false);

  const onOpenTransferPixModal = () => setIsOpenTransferPixModal(true);
  const onCloseTransferPixModal = () => setIsOpenTransferPixModal(false);

  const onOpenPayBilletModal = () => setIsOpenPayBilletModal(true);
  const onClosePayBilletModal = () => setIsOpenPayBilletModal(false);

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
    navigate(`${pathname}?tab=${newValue}`);
  };

  const handleNavigateToTheCreditCardPage = () => navigate('credit-card');

  const openWithdrawModal = () => {
    setShowWithdrawModal(true);
    setIsAnticipation(false);
  };

  const paymentCards = [
    {
      icon: <WithdrawPixIcon sx={{ width: 40, height: 40 }} />,
      title: 'Receber pix',
      onClick: onOpenReceivePixModal,
    },
    {
      icon: <SendPixIcon sx={{ width: 40, height: 40 }} />,
      title: 'Transferir com pix',
      onClick: onOpenTransferPixModal,
    },
    {
      icon: <KeyPixIcon sx={{ width: 40, height: 40 }} />,
      title: 'Minhas chaves pix',
      onClick: () => navigate('/dashboard/financial/pix-keys'),
    },
    {
      icon: <BarcodeIcon sx={{ width: 40, height: 40 }} />,
      title: 'Pagar boleto',
      onClick: onOpenPayBilletModal,
    },
    {
      icon: <CreditCardIcon sx={{ width: 40, height: 40 }} />,
      title: 'Cartão virtual',
      onClick: handleNavigateToTheCreditCardPage,
    },
  ];

  return (
    <>
      <Helmet>
        <title> Dashboard | Financeiro</title>
      </Helmet>

      <WithdrawalModal
        antecipation={isAnticipation}
        minAmount={isAnticipation ? 1 : 10}
        balance={isAnticipation ? antecipateAmount : balance}
        open={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
      />

      {isOpenReceivePixModal && (
        <ReceivePixModal open={isOpenReceivePixModal} onClose={onCloseReceivePixModal} />
      )}

      {isOpenTransferPixModal && (
        <TransferPixModal open={isOpenTransferPixModal} onClose={onCloseTransferPixModal} user={user} />
      )}

      {isOpenPayBilletModal && (
        <PayBilletModal open={isOpenPayBilletModal} onClose={onClosePayBilletModal} />
      )}

      <Container maxWidth="lg">
        <Stack gap={2}>
          <Typography variant="h4">Financeiro</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <MySalesWidget
                title="Saldo Disponível"
                total={fCurrency(balance)}
                fetching={fetchingBalance}
                sx={{
                  borderLeft: (theme) => `solid 4px ${theme.palette.success.main}`,
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <MySalesWidget
                title="Saldo Pendente"
                total={fCurrency(pending + antecipateAmount)}
                fetching={fetchingBalance}
                sx={{
                  borderLeft: (theme) => `solid 4px ${theme.palette.warning.light}`,
                }}
              />
            </Grid>
          </Grid>

          {!isNewLayoutEnabled && (
            <Stack direction="row" justifyContent="end" gap={1}>
              {/* <Button
              variant="contained"
              color="warning"
              onClick={() => {
                setShowWithdrawModal(true);
                setIsAnticipation(true);
              }}
            >
              Efetuar Antecipação
            </Button> */}
              <Button variant="contained" onClick={openWithdrawModal}>
                Efetuar Saque
              </Button>
            </Stack>
          )}

          {isNewLayoutEnabled && (
            <Grid item xs={12}>
              <Stack direction={{ xs: 'column', sm: 'row' }} gap={1}>
                {paymentCards.map((card) => (
                  <PaymentsCard
                    onClick={card?.onClick}
                    key={card.title}
                    icon={card.icon}
                    title={card.title}
                  />
                ))}
              </Stack>
            </Grid>
          )}
          <IdentityVerificationProvider>
            <TabContext value={tabValue}>
              <Card>
                <TabList value={tabValue} onChange={handleTabChange} sx={{ p: 1, px: 3 }}>
                  <Tab value="extract" label="Extrato" />
                  <Tab value="bank_data" label="Dados Bancários" />
                  <Tab value="identity" label="Identidade" />
                </TabList>
              </Card>
              <TabPanel value="extract" sx={{ p: 0 }}>
                <WithdrawalTable />
              </TabPanel>
              <TabPanel value="bank_data" sx={{ p: 0, pl: 1 }}>
                <BankData />
              </TabPanel>
              <TabPanel value="identity" sx={{ p: 0, pl: 1 }}>
                <Box flexDirection="column" display="flex" gap={3}>
                  <IdentityVerificationWarning />
                  <Grid container spacing={2}>
                    {pre_approved && user.totalSales <= 0.0 ? (
                      <Grid item xs={12}>
                        <Alert severity="warning" variant="standard">
                          Você deve realizar uma venda pra poder continuar a verificação
                        </Alert>
                      </Grid>
                    ) : (
                      <>
                        <Grid item xs={12} md={5}>
                          <Typography variant="subtitle1" marginBottom={1}>
                            Completar cadastro
                          </Typography>
                          <Typography variant="body2" color="gray">
                            Preencha seus dados para receber o dinheiro das suas vendas
                          </Typography>
                        </Grid>
                        <Grid item xs={12} md={7}>
                          <Card sx={{ p: 2 }}>
                            <IdentityVerificationStepper />
                          </Card>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Box>
              </TabPanel>
            </TabContext>
          </IdentityVerificationProvider>
        </Stack>
      </Container>
    </>
  );
}
