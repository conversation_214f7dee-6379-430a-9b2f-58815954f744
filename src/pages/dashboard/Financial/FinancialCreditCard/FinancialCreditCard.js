import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Helmet } from 'react-helmet-async';
import { Container, Stack } from '@mui/system';
import { Button } from '@mui/material';
import Iconify from '@/components/iconify';
import { CreditCardList, CreditCardModalForm } from '@/sections/@dashboard/financial';

const FinancialCreditCard = () => {
  const [isOpenCreditCardModalForm, setIsOpenCreditCardModalForm] = useState(false);

  const handleOpenCreditCardModalForm = () => setIsOpenCreditCardModalForm(true);

  const handleCloseCreditCardModalForm = () => setIsOpenCreditCardModalForm(false);

  const navigate = useNavigate();

  const handleGoBack = () => navigate('/dashboard/financial');

  return (
    <>
      <Helmet>
        <title>Dashboard | Financeiro | Cartão de Crédito</title>
      </Helmet>

      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <Stack direction="row" justifyContent="space-between" mb={3}>
          <Button
            variant="text"
            startIcon={<Iconify icon="ion:arrow-back-outline" />}
            onClick={handleGoBack}
          >
            Voltar
          </Button>

          <Button variant="contained" onClick={handleOpenCreditCardModalForm}>
            Adicionar Cartão
          </Button>
        </Stack>

        <CreditCardList />

        <CreditCardModalForm
          open={isOpenCreditCardModalForm}
          onClose={handleCloseCreditCardModalForm}
        />
      </Container>
    </>
  );
};

export default FinancialCreditCard;
