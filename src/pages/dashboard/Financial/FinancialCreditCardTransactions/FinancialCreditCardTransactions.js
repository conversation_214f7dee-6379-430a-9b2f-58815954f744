import { useNavigate } from 'react-router';
import { Helmet } from 'react-helmet-async';
import { Container, Stack } from '@mui/system';
import { Button } from '@mui/material';
import Iconify from '@/components/iconify';
import { CreditCardTransactionsTable } from '@/sections/@dashboard/financial';

const FinancialCreditCardTransactions = () => {
  const navigate = useNavigate();

  const handleGoBack = () => navigate('/dashboard/financial/credit-card');

  return (
    <>
      <Helmet>
        <title>Dashboard | Financeiro | Cartão de Crédito | Transações</title>
      </Helmet>

      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <Stack direction="row" justifyContent="flex-start" mb={3}>
          <Button
            variant="text"
            startIcon={<Iconify icon="ion:arrow-back-outline" />}
            onClick={handleGoBack}
          >
            Voltar
          </Button>
        </Stack>

        <CreditCardTransactionsTable />
      </Container>
    </>
  );
};

export default FinancialCreditCardTransactions;
