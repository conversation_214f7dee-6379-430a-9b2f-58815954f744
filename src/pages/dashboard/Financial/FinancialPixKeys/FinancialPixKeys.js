import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Helmet } from 'react-helmet-async';
import { Container, Stack } from '@mui/system';
import { Button, MenuItem } from '@mui/material';
import MenuPopover from '@/components/menu-popover/MenuPopover';
import Iconify from '@/components/iconify';
import { PixKeysList } from '@/sections/@dashboard/financial';
import {
  RegisterCPFPixKeyModal,
  RegisterCNPJPixKeyModal,
  RegisterCellphonePixKeyModal,
  RegisterEmailPixKeyModal,
  RegisterAleatoryPixKeyModal,
} from '@/sections/@dashboard/financial/PixKeysList/RegisterPixKeyModals';
import { mapIcon } from '@/sections/@dashboard/financial/PixKeysList/constants';

const FinancialPixKeys = () => {
  const navigate = useNavigate();
  const [openPopover, setOpenPopover] = useState(null);

  const [isOpenRegisterCPFPixKeyModal, setIsOpenRegisterCPFPixKeyModal] = useState(false);
  const [isOpenRegisterCNPJPixKeyModal, setIsOpenRegisterCNPJPixKeyModal] = useState(false);
  const [isOpeRegisterCellphonePixKeyModal, setIsOpenRegisterCellphonePixKeyModal] =
    useState(false);

  const [isOpenRegisterEmailPixKeyModal, setIsOpenRegisterEmailPixKeyModal] = useState(false);
  const [isOpenRegisterAleatoryPixKeyModal, setIsOpenRegisterAleatoryPixKeyModal] = useState(false);

  const onOpenRegisterCPFPixKeyModal = () => setIsOpenRegisterCPFPixKeyModal(true);
  const onCloseRegisterCPFPixKeyModal = () => setIsOpenRegisterCPFPixKeyModal(false);

  const onOpenRegisterCNPJPixKeyModal = () => setIsOpenRegisterCNPJPixKeyModal(true);
  const onCloseRegisterCNPJPixKeyModal = () => setIsOpenRegisterCNPJPixKeyModal(false);

  const onOpenRegisterCellphonePixKeyModal = () => setIsOpenRegisterCellphonePixKeyModal(true);
  const onCloseRegisterCellphonePixKeyModal = () => setIsOpenRegisterCellphonePixKeyModal(false);

  const onOpenRegisterEmailPixKeyModal = () => setIsOpenRegisterEmailPixKeyModal(true);
  const onCloseRegisterEmailPixKeyModal = () => setIsOpenRegisterEmailPixKeyModal(false);

  const onOpenRegisterAleatoryPixKeyModal = () => setIsOpenRegisterAleatoryPixKeyModal(true);
  const onCloseRegisterAleatoryPixKeyModal = () => setIsOpenRegisterAleatoryPixKeyModal(false);

  const onOpenPopover = (event) => {
    event.stopPropagation();
    setOpenPopover(event.currentTarget);
  };

  const handleGoBack = () => navigate('/dashboard/financial');

  return (
    <>
      <Helmet>
        <title>Dashboard | Financeiro | Minhas chaves pix</title>
      </Helmet>
      {isOpenRegisterCPFPixKeyModal && (
        <RegisterCPFPixKeyModal
          open={isOpenRegisterCPFPixKeyModal}
          onClose={onCloseRegisterCPFPixKeyModal}
        />
      )}

      {isOpeRegisterCellphonePixKeyModal && (
        <RegisterCellphonePixKeyModal
          open={isOpeRegisterCellphonePixKeyModal}
          onClose={onCloseRegisterCellphonePixKeyModal}
        />
      )}

      {isOpenRegisterCNPJPixKeyModal && (
        <RegisterCNPJPixKeyModal
          open={isOpenRegisterCNPJPixKeyModal}
          onClose={onCloseRegisterCNPJPixKeyModal}
        />
      )}

      {isOpenRegisterEmailPixKeyModal && (
        <RegisterEmailPixKeyModal
          open={isOpenRegisterEmailPixKeyModal}
          onClose={onCloseRegisterEmailPixKeyModal}
        />
      )}

      {isOpenRegisterAleatoryPixKeyModal && (
        <RegisterAleatoryPixKeyModal
          open={isOpenRegisterAleatoryPixKeyModal}
          onClose={onCloseRegisterAleatoryPixKeyModal}
        />
      )}

      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <Stack direction="row" justifyContent="space-between" mb={3}>
          <Button
            variant="text"
            startIcon={<Iconify icon="ion:arrow-back-outline" />}
            onClick={handleGoBack}
          >
            Voltar
          </Button>

          <Button variant="contained" onClick={onOpenPopover}>
            Cadastrar nova chave
          </Button>
        </Stack>

        <MenuPopover open={openPopover} onClose={() => setOpenPopover(null)}>
          <MenuItem onClick={onOpenRegisterCPFPixKeyModal}>
            <Iconify icon={mapIcon.cpf} />
            CPF
          </MenuItem>

          <MenuItem onClick={onOpenRegisterCNPJPixKeyModal}>
            <Iconify icon={mapIcon.cnpj} />
            CNPJ
          </MenuItem>

          <MenuItem onClick={onOpenRegisterCellphonePixKeyModal}>
            <Iconify icon={mapIcon.cellphone} />
            Celular
          </MenuItem>

          <MenuItem onClick={onOpenRegisterEmailPixKeyModal}>
            <Iconify icon={mapIcon.email} />
            E-mail
          </MenuItem>

          <MenuItem onClick={onOpenRegisterAleatoryPixKeyModal}>
            <Iconify icon={mapIcon.aleatory} />
            Chave aleatória
          </MenuItem>
        </MenuPopover>

        <PixKeysList />
      </Container>
    </>
  );
};

export default FinancialPixKeys;
