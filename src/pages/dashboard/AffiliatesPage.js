import { Tab<PERSON>ontext, TabList } from '@mui/lab';
import { Card, Tab, Typography } from '@mui/material';
import { Container, Stack } from '@mui/system';
import { useContext } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router';
import { useLocation } from 'react-router-dom';
import { AffiliatesContext } from '../../contexts/AffiliatesContext';
import MyAffiliatesTable from '../../sections/@dashboard/affiliates/MyAffiliatesTable';

export default function AffiliatesPage() {
  const { pathname } = useLocation();

  const { tabValue, setTabValue } = useContext(AffiliatesContext);

  const navigate = useNavigate();

  return (
    <>
      <Helmet>
        <title> Dashboard | Afiliados </title>
      </Helmet>

      <Container maxWidth="lg">
        <Stack gap={2}>
          <Typography variant="h4">Meus Afiliados</Typography>
          <TabContext value={tabValue}>
            <Card>
              <TabList
                value={tabValue}
                onChange={(event, newTab) => {
                  setTabValue(newTab);
                  navigate(`${pathname}?tab=${newTab}`);
                }}
                sx={{ p: 1, px: 3 }}
              >
                <Tab value="active" label="Ativos" />
                <Tab value="pending" label="Pendentes" />
                <Tab value="disabled" label="Recusados, bloqueados ou cancelados" />
              </TabList>

              <MyAffiliatesTable />
            </Card>
          </TabContext>
        </Stack>
      </Container>
    </>
  );
}
