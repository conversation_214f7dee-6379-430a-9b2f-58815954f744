import { useState, useEffect, useCallback } from 'react';

import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useForm } from 'react-hook-form';

// @mui
import { Container, Tab, Tabs, Box } from '@mui/material';

// sections
import {
  AccountGeneral,
  AccountChangePassword,
  Security,
} from '@/sections/@dashboard/user/account';
import { FinancialFees } from '@/sections/@dashboard/financial';

// components
import Iconify from '@/components/iconify';
import { useSettingsContext } from '@/components/settings';
import FormProvider from '@/components/hook-form/FormProvider';

// ----------------------------------------------------------------------
export default function Profile() {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { themeStretch } = useSettingsContext();
  const methods = useForm();

  const [currentTab, setCurrentTab] = useState('general');
  const [searchParams] = useSearchParams();
  const tabParam = searchParams.get('tab');

  const handleTabChange = useCallback(
    (_, newValue) => {
      setCurrentTab(newValue);
      navigate(`${pathname}?tab=${newValue}`);
    },
    [navigate, pathname]
  );

  useEffect(() => {
    if (tabParam) {
      handleTabChange(null, tabParam);
    }
  }, [tabParam, handleTabChange]);

  const TABS = [
    {
      value: 'general',
      label: 'Geral',
      icon: <Iconify icon="ic:round-account-box" />,
      component: <AccountGeneral />,
    },
    {
      value: 'change_password',
      label: 'Alterar Senha',
      icon: <Iconify icon="ic:round-vpn-key" />,
      component: <AccountChangePassword />,
    },
    {
      value: 'security',
      label: 'Segurança',
      icon: <Iconify icon="mingcute:safe-lock-fill" />,
      component: (
        <FormProvider methods={methods}>
          <Security />
        </FormProvider>
      ),
    },
    {
      value: 'fees',
      label: 'Planos e Taxas',
      icon: <Iconify icon="mdi:percent-circle" />,
      component: <FinancialFees />,
    },
  ];

  return (
    <>
      <Helmet>
        <title> Meu Perfil </title>
      </Helmet>

      <Container maxWidth={themeStretch ? false : 'lg'}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {TABS.map((tab) => (
            <Tab key={tab.value} label={tab.label} icon={tab.icon} value={tab.value} />
          ))}
        </Tabs>

        {TABS.map(
          (tab) =>
            tab.value === currentTab && (
              <Box key={tab.value} sx={{ mt: 5 }}>
                {tab.component}
              </Box>
            )
        )}
      </Container>
    </>
  );
}
