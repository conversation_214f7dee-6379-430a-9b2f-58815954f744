import { Helmet } from 'react-helmet-async';
// @mui
import { Card, Grid, MenuItem, Tab, Typography } from '@mui/material';
// sections
import { LoadingButton, TabContext, TabList } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { useContext, useState } from 'react';
// components
import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import Iconify from '../../components/iconify/Iconify';
import MenuPopover from '../../components/menu-popover/MenuPopover';
import { OrdersContext } from '../../contexts/OrdersContext';
import MySalesTable from '../../sections/@dashboard/my-sales/MySalesTable';
import MySalesWidget from '../../sections/@dashboard/my-sales/MySalesWidget';
// utils
import { fCurrency, fPercent } from '../../utils/formatNumber';

// ----------------------------------------------------------------------
export default function MySales() {
  const [anchorEl, setAnchorEl] = useState(null);

  const [searchParams] = useSearchParams();

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'paid');
  const { pathname } = useLocation();

  const navigate = useNavigate();
  const {
    setStatus,
    ordersCount,
    ordersAmount,
    ordersRefundAmount,
    ordersPixAmount,
    refundPercentage,
    ordersChargebackAmount,
    fetching,
    mutateExport,
    loadingExport,
  } = useContext(OrdersContext);

  return (
    <Container maxWidth="lg">
      <Helmet>
        <title> Dashboard | Minhas Vendas</title>
      </Helmet>

      <MenuPopover open={anchorEl} onClose={() => setAnchorEl(null)} sx={{ width: 160 }}>
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            mutateExport({ extension: 'xlsx' });
          }}
        >
          XLS
        </MenuItem>
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            mutateExport({ extension: 'csv' });
          }}
        >
          CSV
        </MenuItem>
      </MenuPopover>

      <Stack gap={2}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          gap={2}
          flexWrap="wrap"
        >
          <Typography variant="h4">Minhas Vendas</Typography>
          <LoadingButton
            endIcon={<Iconify icon="eva:arrow-ios-downward-outline" />}
            size="medium"
            variant="contained"
            loading={loadingExport}
            onClick={(event) => {
              setAnchorEl(event.currentTarget);
            }}
          >
            Exportar
          </LoadingButton>
        </Stack>

        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={12} md={6}>
            <MySalesWidget
              title="Vendas encontradas"
              total={ordersCount || 0}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <MySalesWidget
              title="Valor líquido"
              total={fCurrency(ordersAmount || 0)}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <MySalesWidget
              title="Total reembolsado"
              total={fCurrency(ordersRefundAmount || 0)}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <MySalesWidget
              title="Vendas no pix"
              total={fCurrency(ordersPixAmount || 0)}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <MySalesWidget
              title="Porcentagem de reembolso"
              total={fPercent(refundPercentage || '0.00')}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <MySalesWidget
              title="Chargeback"
              total={fCurrency(ordersChargebackAmount || 0)}
              fetching={fetching}
            />
          </Grid>
        </Grid>

        <TabContext value={tabValue}>
          <Card>
            <TabList
              onChange={(_, newTab) => {
                setTabValue(newTab);
                if (newTab === 'all') {
                  setStatus('');
                } else {
                  setStatus(newTab);
                }

                navigate(`${pathname}?tab=${newTab}`);
              }}
              sx={{ p: 1, px: 3, mb: 1 }}
            >
              <Tab value="paid" label="Aprovadas" />
              <Tab value="refunded" label="Reembolsadas" />
              <Tab value="chargedback" label="Chargeback" />
              <Tab value="MED" label="MED " />
              <Tab value="all" label="Todas" />
            </TabList>
            <MySalesTable />
          </Card>
        </TabContext>
      </Stack>
    </Container>
  );
}
