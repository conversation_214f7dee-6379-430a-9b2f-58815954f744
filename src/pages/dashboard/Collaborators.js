import { useContext } from 'react';
import { Helmet } from 'react-helmet-async';
// @mui
import { Button, Typography } from '@mui/material';
// sections
import { Container, Stack } from '@mui/system';
import { CollaboratorsContext } from '../../contexts/CollaboratorsContext';
import { CollaboratorsModal, CollaboratorsTable } from '../../sections/@dashboard/collaborators';
import { FormScopes } from '../../utils/form';

// ----------------------------------------------------------------------
export default function Collaborators() {
  const { scope, setScope, form } = useContext(CollaboratorsContext);

  return (
    <>
      <Helmet>
        <title>Dashboard | Colaboradores</title>
      </Helmet>

      <CollaboratorsModal
        open={[FormScopes.CREATE, FormScopes.EDIT].includes(scope)}
        onClose={() => setScope(FormScopes.INDEX)}
      />

      <Container maxWidth="lg">
        <Stack gap={2}>
          <Typography variant="h4">Colaboradores</Typography>
          <CollaboratorsTable
            Actions={
              <Button
                variant="contained"
                onClick={() => {
                  form.reset({});
                  setScope(FormScopes.CREATE);
                }}
              >
                Adicionar Colaborador
              </Button>
            }
          />
        </Stack>
      </Container>
    </>
  );
}
