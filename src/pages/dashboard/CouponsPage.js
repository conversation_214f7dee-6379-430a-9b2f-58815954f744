import { Helmet } from 'react-helmet-async';
import { useState } from 'react';
import { Button, Card, Tab } from '@mui/material';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { FormScopes } from '../../utils/form';
import CouponsProvider from '../../contexts/CouponsContext';
import { CouponsTable, CouponsForm } from '../../sections/@dashboard/coupons';

export default function CouponsPage() {
  const [isCouponsFormOpen, setIsCouponsFormOpen] = useState(false);

  const handleOpenCouponsForm = () => {
    setIsCouponsFormOpen(true);
  };

  const handleCloseCouponsForm = () => {
    setIsCouponsFormOpen(false);
  };

  return (
    <CouponsProvider>
      <Helmet>
        <title> Dashboard | Cupons </title>
      </Helmet>

      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <Stack width={1} direction="row" justifyContent="flex-end" mb={3}>
          <Button onClick={handleOpenCouponsForm} variant="contained">
            Adicionar Cupom
          </Button>
        </Stack>

        <TabContext value="coupons">
          <Card>
            <TabList value="coupons" sx={{ p: 1, px: 3 }}>
              <Tab value="coupons" label="Meus Cupons" />
            </TabList>

            <TabPanel value="coupons" sx={{ p: 0 }}>
              <CouponsTable />
            </TabPanel>
          </Card>
        </TabContext>

        <CouponsForm
          scope={FormScopes.CREATE}
          open={isCouponsFormOpen}
          onClose={handleCloseCouponsForm}
        />
      </Container>
    </CouponsProvider>
  );
}
