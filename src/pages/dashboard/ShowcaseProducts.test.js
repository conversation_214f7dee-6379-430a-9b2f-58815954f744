import { fetchBannerService } from '@/services/banner';
import {
  getProductCategoriesService,
  getShowcaseProducts,
  registerMetricsForShowcaseProducts,
} from '@/services/products';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { HelmetProvider } from 'react-helmet-async';
import ShowcaseProducts from './ShowcaseProducts';

// Mock the services
jest.mock('@/services/products', () => ({
  getShowcaseProducts: jest.fn(),
  getProductCategoriesService: jest.fn(),
  registerMetricsForShowcaseProducts: jest.fn().mockResolvedValue(),
}));

jest.mock('@/services/banner', () => ({
  fetchBannerService: jest.fn(),
}));

// Mock useAuthContext
jest.mock('@/auth/useAuthContext', () => ({
  useAuthContext: () => ({
    user: { has_usable_password: true },
  }),
}));

// Mock useNavigate
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

// Mock usePageView
jest.mock('@/utils/FacebookPixel', () => ({
  usePageView: () => jest.fn(),
}));

jest.mock('@/sections/@dashboard/product-showcase/ProductFilterDrawer', () => () => (
  <div>FilterDrawer</div>
));

jest.mock('@/sections/@dashboard/product-showcase/ProductDetailsDrawer', () => () => (
  <div>ProductDetailsDrawer</div>
));

const createWrapper = (queryClient) => {
  return ({ children }) => (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </HelmetProvider>
  );
};

describe('ShowcaseProducts Metrics', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(<ShowcaseProducts />, { wrapper: createWrapper(queryClient) });
  };

  it('should register view metrics when products are loaded', async () => {
    // Mock the API responses
    const mockProducts = {
      results: [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' },
      ],
      next: null,
    };

    getShowcaseProducts.mockResolvedValue(mockProducts);
    getProductCategoriesService.mockResolvedValue([]);
    fetchBannerService.mockResolvedValue([]);

    renderComponent();

    // Wait for the products to be loaded
    await waitFor(() => {
      expect(registerMetricsForShowcaseProducts).toHaveBeenCalledWith('view', ['1', '2']);
    });
  });

  it('should not register duplicate view metrics for the same products', async () => {
    // Mock the API responses
    const mockProducts = {
      results: [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' },
      ],
      next: null,
    };

    getShowcaseProducts.mockResolvedValue(mockProducts);
    getProductCategoriesService.mockResolvedValue([]);
    fetchBannerService.mockResolvedValue([]);

    renderComponent();

    // Wait for initial load
    await waitFor(() => {
      expect(registerMetricsForShowcaseProducts).toHaveBeenCalledWith('view', ['1', '2']);
    });

    // Clear the mock to check subsequent calls
    jest.clearAllMocks();

    // Trigger a re-render that would cause products to be loaded again
    await queryClient.invalidateQueries(['showcase']);

    // Wait and verify that metrics are not registered again
    await waitFor(() => {
      expect(registerMetricsForShowcaseProducts).not.toHaveBeenCalled();
    });
  });

  it('should register click metrics when a product is clicked', async () => {
    // Mock the API responses
    const mockProducts = {
      results: [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' },
      ],
      next: null,
    };

    getShowcaseProducts.mockResolvedValue(mockProducts);
    getProductCategoriesService.mockResolvedValue([]);
    fetchBannerService.mockResolvedValue([]);

    renderComponent();

    // Wait for products to be loaded
    await waitFor(() => {
      expect(screen.getByText('Product 1')).toBeInTheDocument();
    });

    // Click on a product
    fireEvent.click(screen.getByText('Product 1'));

    // Verify that click metrics were registered
    await waitFor(() => {
      expect(registerMetricsForShowcaseProducts).toHaveBeenCalledWith('click', ['1']);
    });
  });

  it('should handle errors when registering metrics', async () => {
    // Mock the API responses
    const mockProducts = {
      results: [{ id: '1', name: 'Product 1' }],
      next: null,
    };

    getShowcaseProducts.mockResolvedValue(mockProducts);
    getProductCategoriesService.mockResolvedValue([]);
    fetchBannerService.mockResolvedValue([]);
    registerMetricsForShowcaseProducts.mockRejectedValue(new Error('Failed to register metrics'));

    // Mock console.error to prevent error output in tests
    const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

    renderComponent();

    // Wait for the error to be handled
    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith('Error registering metrics for showcase products');
    });

    // Cleanup
    consoleError.mockRestore();
  });
});
