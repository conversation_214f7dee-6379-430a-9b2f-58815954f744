import { Helmet } from 'react-helmet-async';
// @mui
import { Grid, Typography } from '@mui/material';
// sections
import { Container, Stack } from '@mui/system';
import { NotificationsTable } from '../../sections/@dashboard/notifications';

// ----------------------------------------------------------------------
export default function NotificationsPage() {
  return (
    <Grid spacing={3}>
      <Helmet>
        <title>Dashboard | Notificações</title>
      </Helmet>

      <Container maxWidth="md">
        <Stack gap={2}>
          <Typography variant="h4">Notificações</Typography>
          <NotificationsTable
            tableLabels={[
              { id: 'title', label: 'Títu<PERSON>' },
              { id: 'description', label: 'Descrição' },
              { id: 'date', label: 'Data' },
              { id: 'read', label: 'Lida' },
            ]}
          />
        </Stack>
      </Container>
    </Grid>
  );
}
