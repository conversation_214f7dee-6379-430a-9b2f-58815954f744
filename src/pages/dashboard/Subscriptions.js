import { Helmet } from 'react-helmet-async';
// @mui
import { Card, Grid, MenuItem, Tab, Typography } from '@mui/material';
// sections
import { LoadingButton, TabContext, TabList } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { useContext, useState } from 'react';
// components
import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import Iconify from '../../components/iconify/Iconify';
import MenuPopover from '../../components/menu-popover/MenuPopover';
import { SubscriptionContext } from '../../contexts/SubscriptionContext';
import { MySalesWidget } from '../../sections/@dashboard/my-sales';
import SubscriptionsTable from '../../sections/@dashboard/subscriptions/SubscriptionsTable';
// utils
import { fCurrency } from '../../utils/formatNumber';

// ----------------------------------------------------------------------
export default function Subscriptions() {
  const { exportSubscription, exporting, fetching, tab, activeCount, recurringAmount } =
    useContext(SubscriptionContext);

  const [anchorEl, setAnchorEl] = useState(null);
  const [searchParams] = useSearchParams();

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'active');

  const { pathname } = useLocation();

  const navigate = useNavigate();
  return (
    <Container maxWidth="lg">
      <Helmet>
        <title> Dashboard | Minhas Assinaturas</title>
      </Helmet>

      <MenuPopover open={anchorEl} onClose={() => setAnchorEl(null)} sx={{ width: 160 }}>
        <MenuItem
          onClick={() => {
            exportSubscription({ extension: 'xlsx' });
            setAnchorEl(null);
          }}
        >
          XLS
        </MenuItem>
        <MenuItem
          onClick={() => {
            exportSubscription({ extension: 'csv' });
            setAnchorEl(null);
          }}
        >
          CSV
        </MenuItem>
      </MenuPopover>

      <Stack gap={2}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          gap={2}
          flexWrap="wrap"
        >
          <Typography variant="h4">Assinaturas</Typography>
          <LoadingButton
            startIcon={<Iconify icon="ion:download-outline" />}
            size="medium"
            variant="contained"
            loading={exporting}
            onClick={(event) => {
              setAnchorEl(event.currentTarget);
            }}
          >
            Exportar
          </LoadingButton>
        </Stack>

        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={12} md={6}>
            <MySalesWidget
              title="Assinaturas ativas"
              total={activeCount}
              sx={{
                borderLeft: (theme) => `4px solid ${theme.palette.success.main}`,
              }}
              fetching={fetching}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <MySalesWidget
              title="Faturamento recorrente mensal"
              total={fCurrency(recurringAmount)}
              sx={{
                borderLeft: (theme) => `4px solid ${theme.palette.success.main}`,
              }}
              fetching={fetching}
            />
          </Grid>
        </Grid>

        <TabContext value={tab}>
          <Card>
            <TabList
              value={tabValue}
              onChange={(event, newTab) => {
                setTabValue(newTab);
                navigate(`${pathname}?tab=${newTab}`);
              }}
              sx={{ p: 1, px: 3, mb: 1 }}
            >
              <Tab value="active" label="Ativas" />
              <Tab value="canceled" label="Canceladas" />
              <Tab value="all" label="Todas" />
            </TabList>
            <SubscriptionsTable />
          </Card>
        </TabContext>
      </Stack>
    </Container>
  );
}
