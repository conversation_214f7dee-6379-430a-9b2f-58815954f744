import Iconify from '@/components/iconify';
import VotingBanner from '@/components/voting-banner';
import { useDebounce } from '@/hooks/useDebounce';
import { useShowcaseMetrics } from '@/hooks/useShowcaseMetrics';
import { CarouselBanner } from '@/sections/@dashboard/home';
import ProductCard from '@/sections/@dashboard/product-showcase/ProductCard';
import ProductCardSkeleton from '@/sections/@dashboard/product-showcase/ProductCardSkeleton';
import CourseDetailsDrawer from '@/sections/@dashboard/product-showcase/ProductDetailsDrawer';
import CoursesFilterDrawer from '@/sections/@dashboard/product-showcase/ProductFilterDrawer';
import { fetchBannerService } from '@/services/banner';
import { getProductCategoriesService, getShowcaseProducts } from '@/services/products';
import { LoadingButton } from '@mui/lab';
import {
  Badge,
  Button,
  Card,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Box, Container } from '@mui/system';
import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
// eslint-disable-next-line import/no-extraneous-dependencies
import { useInView } from 'react-intersection-observer';

const ORDER_BY_OPTIONS = [
  {
    id: 'temperature_desc',
    label: 'Mais quentes',
    field: 'temperature',
    direction: 'desc',
  },
  {
    id: 'created_at_desc',
    label: 'Recentes',
    field: 'createdAt',
    direction: 'desc',
  },
  {
    id: 'created_at_asc',
    label: 'Antigos',
    field: 'createdAt',
    direction: 'asc',
  },
  {
    id: 'price_desc',
    label: 'Mais caros',
    field: 'price',
    direction: 'desc',
  },
  {
    id: 'price_asc',
    label: 'Mais baratos',
    field: 'price',
    direction: 'asc',
  },
  {
    id: 'name_asc',
    label: 'Nome (A-Z)',
    field: 'name',
    direction: 'asc',
  },
  {
    id: 'name_desc',
    label: 'Nome (Z-A)',
    field: 'name',
    direction: 'desc',
  },
];
export default function ShowcaseProducts() {
  const { ref, inView } = useInView();
  const [openBannerDetails, setOpenBannerOpenDetails] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState(ORDER_BY_OPTIONS[0]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [filters, setFilters] = useState({});
  const queryClient = useQueryClient();
  const { registerMetric } = useShowcaseMetrics();

  const { data, isFetching: isBannerFetching } = useQuery(['fetchBannerService'], {
    queryFn: () => fetchBannerService({ location: 'vitrine' }),
    initialData: [],
  });

  const debouncedSearch = useDebounce(search, 500);

  const {
    data: products,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ['showcase', { debouncedSearch, selectedCategory, sortBy, ...filters }],
    queryFn: ({ pageParam }) =>
      getShowcaseProducts(debouncedSearch, selectedCategory, pageParam, filters, sortBy).then(
        (response) => {
          const productIds = response.results.map((product) => product.id);
          registerMetric('view', productIds);
          return response;
        }
      ),
    getNextPageParam: (lastPage) => {
      if (!lastPage?.next) {
        return undefined;
      }

      const nextPageWithParams = lastPage.next.split('page=')[1];
      if (!nextPageWithParams) {
        return undefined;
      }

      const nextPage = nextPageWithParams.split('&')[0];
      return nextPage ? Number(nextPage) : undefined;
    },
  });

  const productCategoriesQuery = useQuery({
    queryKey: ['product-categories'],
    queryFn: () => getProductCategoriesService(),
    initialData: [],
  });

  const handleOpenBannerDetails = (course) => {
    setSelectedProduct(course);
    setOpenBannerOpenDetails(true);
  };

  const handleCloseBannerDetails = () => {
    setSelectedProduct(null);
    setOpenBannerOpenDetails(false);
  };

  const handleOpenFilter = () => {
    setShowFilter(true);
  };

  const handleCloseFilter = () => {
    setShowFilter(false);
  };

  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };

  const countActiveFilters = (filtersObj) => {
    if (!filtersObj || typeof filtersObj !== 'object') return 0;

    return Object.values(filtersObj).reduce((count, value) => {
      if (Array.isArray(value) && value.length > 0) return count + 1;

      if (typeof value === 'object' && Object.keys(value).length > 0) return count + 1;

      return count + 1;
    }, 0);
  };

  const productsResults = useMemo(() => {
    const allProducts = products?.pages?.flatMap((page) => page.results) || [];

    const uniqueProductsMap = allProducts.reduce((map, product) => {
      if (!map.has(product.id)) {
        map.set(product.id, product);
      }
      return map;
    }, new Map());

    return Array.from(uniqueProductsMap.values());
  }, [products]);

  const isProductsEmpty = productsResults.length === 0;
  const activeFiltersCount = countActiveFilters(filters);

  const onAffiliateSuccess = async () => {
    await queryClient.invalidateQueries({
      queryKey: ['showcase', { debouncedSearch, selectedCategory, sortBy, ...filters }],
    });

    const updatedData = await queryClient.fetchQuery({
      queryKey: ['showcase', { debouncedSearch, selectedCategory, sortBy, ...filters }],
    });

    const updatedProductsResults = updatedData?.pages?.flatMap((page) => page.results) || [];
    const selectedProductData = updatedProductsResults.find(
      (product) => product.id === selectedProduct.id
    );

    if (selectedProductData) {
      setSelectedProduct(selectedProductData);
    }
  };

  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [fetchNextPage, inView]);

  const clearFilters = (filtersToCleanUp) =>
    Object.entries(filtersToCleanUp).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '' && value !== false) {
        acc[key] = value;
      }
      return acc;
    }, {});

  return (
    <>
      <Helmet>
        <title>Dashboard | Vitrine</title>
      </Helmet>

      <Container maxWidth="xl">
        <VotingBanner />
        <CarouselBanner banners={data} fetching={isBannerFetching} />

        <Grid item xs={12} marginBottom={3}>
          <Typography variant="h4">Vitrine de Afiliação</Typography>
        </Grid>
        <Card>
          <Grid item xs={12} p={4}>
            <Grid
              container
              flexDirection={{ xs: 'column', sm: 'row' }}
              justifyContent="space-between"
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              spacing={2}
            >
              <Grid item xs={12} sm="auto">
                <TextField
                  sx={{
                    width: '220px',
                  }}
                  size="small"
                  placeholder="Pesquisar"
                  onChange={(event) => setSearch(event.target.value)}
                  value={search}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify icon="ion:search-outline" />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              <Grid item xs={12} sm="auto">
                <Box
                  display="flex"
                  flexDirection={{ xs: 'column', sm: 'row' }}
                  gap={2}
                  alignItems={{ xs: 'stretch', sm: 'center' }}
                  width="100%"
                >
                  <FormControl size="small" sx={{ width: '220px' }}>
                    <TextField
                      select
                      id="category-id"
                      size="small"
                      label="Categorias"
                      value={selectedCategory}
                      renderValue={(value) => (value === '' ? 'Todas as categorias' : value)}
                      onChange={handleCategoryChange}
                      InputProps={{
                        endAdornment: selectedCategory ? (
                          <InputAdornment position="end">
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCategory('');
                              }}
                              sx={{ minWidth: 'auto', p: 0.5, mr: 1 }}
                            >
                              <Iconify icon="mdi:close" />
                            </Button>
                          </InputAdornment>
                        ) : null,
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      SelectProps={{
                        displayEmpty: true,
                        MenuProps: {
                          PaperProps: {
                            sx: {
                              px: 1,
                              maxHeight: 280,
                              '& .MuiMenuItem-root': {
                                px: 1,
                                typography: 'body2',
                                borderRadius: 0.75,
                              },
                            },
                          },
                        },
                      }}
                    >
                      <MenuItem value="">Todas as categorias</MenuItem>
                      {productCategoriesQuery.data?.map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.name}
                        </MenuItem>
                      ))}
                    </TextField>
                  </FormControl>

                  <FormControl size="small" sx={{ width: '220px' }}>
                    <TextField
                      id="sort-id"
                      label="Ordenar por"
                      select
                      size="small"
                      value={sortBy.id}
                      onChange={(event) =>
                        setSortBy(
                          ORDER_BY_OPTIONS.find((option) => option.id === event.target.value)
                        )
                      }
                      defaultValue=""
                      SelectProps={{
                        MenuProps: {
                          PaperProps: {
                            sx: {
                              px: 1,
                              maxHeight: 280,
                              '& .MuiMenuItem-root': {
                                px: 1,
                                typography: 'body2',
                                borderRadius: 0.75,
                              },
                            },
                          },
                        },
                      }}
                    >
                      {ORDER_BY_OPTIONS.map((option) => (
                        <MenuItem key={option.id} value={option.id}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  </FormControl>

                  <Badge badgeContent={activeFiltersCount} color="primary">
                    <Button
                      variant="outlined"
                      onClick={handleOpenFilter}
                      startIcon={<Iconify icon="eva:options-2-outline" />}
                      color="inherit"
                      fullWidth
                      sx={{
                        height: '40px',
                        maxWidth: { sm: '120px' },
                      }}
                    >
                      Filtros
                    </Button>
                  </Badge>
                </Box>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12} px={4}>
            <Box display="flex" justifyContent="start">
              <Tooltip title="Produtos mais vendidos" placement="top">
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="h4" width="auto">
                    Mais quentes que o deserto
                  </Typography>
                  <img src="/assets/icons/showcase/cakto.png" alt="Cakto" width={24} height={32} />
                </Box>
              </Tooltip>
            </Box>
          </Grid>

          <Grid
            container
            spacing={{
              xs: 1,
              md: 2,
            }}
            px={4}
            py={2}
            mb={2}
          >
            {(() => {
              if (status === 'loading') {
                return Array.from(new Array(8)).map((_, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                    <ProductCardSkeleton key={index} />
                  </Grid>
                ));
              }

              if (isProductsEmpty) {
                return (
                  <Grid item xs={12}>
                    <Box py={5} textAlign="center">
                      <Typography variant="h6" color="text.secondary">
                        Nenhum produto encontrado
                      </Typography>
                    </Box>
                  </Grid>
                );
              }

              return productsResults?.map((product, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <ProductCard
                    product={product}
                    onClick={() => {
                      handleOpenBannerDetails(product);
                      registerMetric('click', [product.id]);
                    }}
                    sx={{
                      height: '100%',
                    }}
                  />
                </Grid>
              ));
            })()}
          </Grid>

          {hasNextPage && (
            <Grid sx={{ display: 'flex', justifyContent: 'center', padding: 2 }}>
              <LoadingButton
                ref={ref}
                size="sm"
                onClick={() => fetchNextPage()}
                loading={isFetchingNextPage}
                variant="ghost"
              >
                {!isFetchingNextPage ? (
                  <Typography color="text.secondary">Carregar mais</Typography>
                ) : (
                  ''
                )}
              </LoadingButton>
            </Grid>
          )}
        </Card>
      </Container>

      <CourseDetailsDrawer
        key={selectedProduct?.id}
        open={openBannerDetails}
        onClose={handleCloseBannerDetails}
        onAffiliate={onAffiliateSuccess}
        product={selectedProduct}
      />

      <CoursesFilterDrawer
        open={showFilter}
        onClose={handleCloseFilter}
        onSubmit={(values) => setFilters(clearFilters(values))}
      />
    </>
  );
}
