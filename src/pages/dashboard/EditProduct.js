import { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { Helmet } from 'react-helmet-async';
// @mui
import {
  Box,
  Button,
  Card,
  Link,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tab,
  Typography,
  Menu,
  MenuItem,
  Chip,
} from '@mui/material';
// sections

// utils
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton, TabContext, TabList, TabPanel } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { useForm } from 'react-hook-form';
import { useAuthContext } from '@/auth/useAuthContext';
import { isUserMembersV2Tester, generateMembersV2Url, canUserAccessMembersV2 } from '@/consts/members-v2-testers';

import { OrderBumpBlock } from '@/sections/@dashboard/product-bumps/OrderBumpBlock';
import { ProductCoupons } from '@/sections/@dashboard/product-coupons';
import { UpsellDownsellBlock } from '@/sections/@dashboard/product-upsell/UpsellDownsellBlock';

// import { Invoice } from '@/sections/@dashboard/product-invoice';

import { _bumps } from '@/_mock/arrays/_bumps';
import { _prices } from '@/_mock/arrays/_prices';
import ConfirmDialogAdvanced from '@/components/confirm-dialog/ConfirmDialogAdvanced';
import FormProvider from '@/components/hook-form/FormProvider';
import Iconify from '@/components/iconify';
import ProductWarning from '@/components/product-warning';
import { CheckoutProvider } from '@/contexts/CheckoutContext';
import { EditProductContext, ProductType } from '@/contexts/EditProductContext';
import ProductCoProductionsProvider from '@/contexts/ProductCoProductionContext';
import ProductLinksProvider from '@/contexts/ProductLinksContext';
import { PATH_DASHBOARD } from '@/routes/paths';
import EditProductAffiliated from '@/sections/@dashboard/product-affiliated-settings/EditProductAffiliated';
import EditProductCheckout from '@/sections/@dashboard/product-checkouts/EditProductCheckout';
import EditProductCoProduction from '@/sections/@dashboard/product-coproductions/EditProductCoProduction';
import EditProductLinks from '@/sections/@dashboard/product-links/EditProductLinks';
import EditProductSettings from '@/sections/@dashboard/product-settings/EditProductSettings';
import EditProductGeneral from '@/sections/@dashboard/products/EditProductGeneral/EditProductGeneral';
import { useNavigate, useParams } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import * as yup from 'yup';
import InformThatThereChanges from '../components/inform-that-there-changes';

// ----------------------------------------------------------------------
export default function EditProduct() {
  const { user } = useAuthContext();
  const [searchParams] = useSearchParams();

  const { id } = useParams();

  const { pathname } = useLocation();

  const isAffiliate = pathname.includes('affiliates');
  const isStaging = window.location.href.includes('staging.cakto');
  const disableProductCheckoutUrl =
    'https://ajuda.cakto.com.br/pt/article/como-encerrar-as-vendas-e-desabilitar-o-checkout-de-um-produto-xbxxu6/';

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'general');

  const navigate = useNavigate();

  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
  const [openInformationDialog, setOpenInformationDialog] = useState(false);
  const [isDirtyFields, setIsDirtyFields] = useState(false);
  const [membersMenuAnchor, setMembersMenuAnchor] = useState(null);

  const [openConfirmCancelSubscriptions, setOpenConfirmCancelSubscriptions] = useState(false);
  const isThreeDsEnabled = String(user?.threeDsEnabled).toLowerCase() === 'true';

  const {
    product,
    isUpdatingProduct,
    deleteProduct,
    isDeletingProduct,
    cancelSubscriptions,
    isCancelingSubscriptions,
    updateProduct,
    setFirstError,
    refetchProductData,
  } = useContext(EditProductContext);

  const OfferSchema = yup.object().shape({
    delete: yup.boolean(),
    name: yup.string().when('delete', {
      is: true,
      then: yup.string().optional(),
      otherwise: yup.string().required('Campo obrigatório'),
    }),
    type: yup.string().when('delete', {
      is: true,
      then: yup.string().optional(),
      otherwise: yup.string().required('Campo obrigatório'),
    }),
    price: yup.number().when('delete', {
      is: true,
      then: yup.number().optional(),
      otherwise: yup.number().required('Preço é obrigatório').min(5, 'O preço mínimo é R$ 5,00'),
    }),
    intervalType: yup.string().when('delete', {
      is: true,
      then: yup.string().optional(),
      otherwise: yup
        .string()
        .oneOf(['week', 'month', 'year', 'lifetime'], 'Tipo de intervalo inválido')
        .required('Campo obrigatório'),
    }),
    interval: yup.number().when(['intervalType', 'delete'], {
      is: (intervalType, del) => del || intervalType === 'lifetime',
      then: yup.number().notRequired(),
      otherwise: yup
        .number()
        .required('Campo obrigatório')
        .min(1, 'A quantidade do intervalo deve ser maior que 1'),
    }),
    recurrence_period: yup
      .number()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable()
      .when(['delete', 'type'], {
        is: (deleteValue, typeValue) => deleteValue === false && typeValue === 'subscription',
        then: yup.number().required('Campo obrigatório'),
        otherwise: yup.number().nullable(),
      }),
    trial_days: yup
      .string()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? '0' : value))
      .test(
        'is-greater-than-zero',
        'A quantidade de dias de teste deve ser maior ou igual a zero',
        (value) => {
          if (value === null || value === '') return true;
          return parseInt(value, 10) >= 0;
        }
      ),
    max_retries: yup
      .string()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? '0' : value))
      .test(
        'is-greater-than-zero',
        'A quantidade de dias de teste deve ser maior ou igual a zero',
        (value) => {
          if (value === null || value === '') return true;
          return parseInt(value, 10) >= 0;
        }
      ),
    retry_interval: yup
      .string()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? '0' : value))
      .test(
        'is-greater-than-zero',
        'A quantidade de dias de teste deve ser maior ou igual a zero',
        (value) => {
          if (value === null || value === '') return true;
          return parseInt(value, 10) >= 0;
        }
      ),
  });

  const schemas = {
    general: {
      name: yup.string().required('Nome do produto é obrigatório'),
      description: yup
        .string()
        .min(100, 'Coloque mais detalhes sobre seu produto, mínimo 100 caracteres.')
        .required('Descrição é obrigatória'),
      category: yup.string().required('Categoria é obrigatória'),
      type: yup.string().required('O tipo é obrigatório'),
      image: yup.mixed().when('affiliateMarketplace', {
        is: true,
        then: yup.mixed().required('Imagem é obrigatória quando o produto está no marketplace'),
        otherwise: yup.mixed().nullable(false),
      }),
      price: yup
        .number()
        .required('Preço é obrigatório')
        .min(5, 'O preço mínimo é R$ 5,00')
        .typeError('Por favor, digite um valor válido.'),
      guarantee: yup.mixed().oneOf([7, 14, 21, 30], 'Garantia é obrigatória').nullable(false),
      differentOffers: yup.boolean(),
      offers: yup
        .array()
        .of(OfferSchema)
        .when('differentOffers', {
          is: true,
          then: yup.array().min(2, 'Você tem que inserir ao menos uma oferta'),
          otherwise: yup.array().max(11, 'Você pode inserir no máximo 10 ofertas'),
        }),
      salesPage: yup.string().required('Página de vendas é obrigatória'),
      supportEmail: yup
        .string()
        .email('E-mail inválido')
        .required('E-mail de suporte é obrigatório'),
      producerName: yup.string().required('Nome de exibição do produtor é obrigatório'),
    },
    settings: {
      paymentMethods: yup
        .array()
        .of(yup.string().required('Método de pagamento é obrigatório'))
        .min(1, 'Selecione pelo menos um método de pagamento')
        .required('Método de pagamento é obrigatório')
        .typeError('Método de pagamento é obrigatório'),
      invoiceDescription: yup.string(),
      installments: yup.number().required('Quantidade máxima de parcelas é obrigatório'),
      ticketExpiration: yup.string().required('Validade do boleto é obrigatório'),
      confirmEmail: yup.boolean(),
      redirectUpsellWithBumpFail: yup.boolean(),
      defaultPaymentMethod: yup
        .string()
        .required('Método de pagamento padrão do Checkout é obrigatório'),
      bumps: yup.array().of(yup.object()),
      contentDeliveries: yup
        .array()
        .of(yup.string().required('Antes de prosseguir selecione uma das opções acima.'))
        .min(1, 'Antes de prosseguir selecione uma das opções acima.')
        .required('Antes de prosseguir selecione uma das opções acima.')
        .typeError('Antes de prosseguir selecione uma das opções acima.'),
      emailAccessLink: yup
        .string()
        .url('Insira uma URL válida')
        .max(2048, 'O link deve ter no máximo 2048 caracteres')
        .when('contentDeliveries', (contentDeliveries, schema) =>
          contentDeliveries && contentDeliveries.includes('emailAccess')
            ? schema
                .typeError('O link de acesso por email é obrigatório')
                .required('O link de acesso por email é obrigatório')
            : schema.nullable()
        ),
      instagram_cf_instagramUserName: yup
        .string()
        .when('contentDeliveries', (contentDeliveries, schema) =>
          contentDeliveries && contentDeliveries.includes('instagram_cf')
            ? schema
                .typeError('O usuário do Instagram Close Friends é obrigatório')
                .required('O usuário do Instagram Close Friends é obrigatório')
            : schema.nullable()
        ),
      instagram_cf_instagramPassword: yup
        .string()
        .when('contentDeliveries', (contentDeliveries, schema) =>
          contentDeliveries && contentDeliveries.includes('instagram_cf')
            ? schema
                .typeError('A senha do Instagram Close Friends é obrigatória')
                .required('A senha do Instagram Close Friends é obrigatória')
            : schema.nullable()
        ),
      instagram_pp_instagramUserName: yup
        .string()
        .when('contentDeliveries', (contentDeliveries, schema) =>
          contentDeliveries && contentDeliveries.includes('instagram_pp')
            ? schema
                .typeError('O usuário do Instagram Perfil Privado é obrigatório')
                .required('O usuário do Instagram Perfil Privado é obrigatório')
            : schema.nullable()
        ),
      instagram_pp_instagramPassword: yup
        .string()
        .when('contentDeliveries', (contentDeliveries, schema) =>
          contentDeliveries && contentDeliveries.includes('instagram_pp')
            ? schema
                .typeError('A senha do Instagram Perfil Privado é obrigatória')
                .required('A senha do Instagram Perfil Privado é obrigatória')
            : schema.nullable()
        ),
      instagram_pp_serverConnection: yup.string(),
      instagram_cf_serverConnection: yup.string(),
      instagram_cf_isInstagramConnected: yup
        .boolean()
        .when('contentDeliveries', {
          is: (val) => Array.isArray(val) && val.includes('instagram_cf'),
          then: yup
            .boolean()
            .oneOf([true], 'A conexão com o Instagram Close Friends é obrigatória'),
          otherwise: yup.boolean().notRequired(),
        })
        .default(false),
      instagram_pp_isInstagramConnected: yup
        .boolean()
        .when('contentDeliveries', {
          is: (val) => Array.isArray(val) && val.includes('instagram_pp'),
          then: yup
            .boolean()
            .oneOf([true], 'A conexão com o Instagram Perfil Privado é obrigatória'),
          otherwise: yup.boolean().notRequired(),
        })
        .default(false),
    },
    affiliate: {
      affiliateSupportEmail: yup.string().email(),
      affiliate: yup.boolean(),
      affiliateRequest: yup.boolean(),
      affiliateRequestSales: yup.boolean(),
      affiliateContact: yup.boolean(),
      affiliateShareBump: yup.boolean(),
      affiliateShareUpsell: yup.boolean(),
      affiliateMarketplace: yup.boolean(),
      affiliateCloneQuiz: yup.boolean(),
      affiliateCloneQuizUrl: yup.string().when('affiliateCloneQuiz', {
        is: true,
        then: yup
          .string()
          .url('Link de clonagem do quiz é inválido')
          .matches(
            isStaging
              ? /^https:\/\/stg-quiz\.launchify\.com\.br\// // Staging
              : /^https:\/\/(www\.)?quiz\.cakto\.com\.br\//, // Produção
            isStaging
              ? 'O link de clonagem deve começar com https://stg-quiz.launchify.com.br/'
              : 'O link de clonagem deve começar com https://quiz.cakto.com.br/ ou https://www.quiz.cakto.com.br/'
          )
          .required('Link de clonagem do quiz é obrigatório'),
      }),
      affiliateSalesPage: yup.string().optional(),
      category: yup.string().when(['affiliate', 'affiliateMarketplace'], {
        is: true,
        then: yup.string().required('Categoria é obrigatória'),
      }),
      affiliateDescription: yup.string().when('affiliate', {
        is: true,
        then: yup.string().required('Aprovação manual é obrigatória'),
      }),
      affiliateCommission: yup.number().when('affiliate', {
        is: true,
        then: yup
          .number()
          .min(1, 'Comissão deve ser maior ou igual a 1%')
          .max(95, 'Comissão deve ser menor ou igual a 95%')
          .required('Aprovação manual é obrigatória'),
      }),
      affiliateClick: yup.string().when('affiliate', {
        is: true,
        then: yup.string().required('Aprovação manual é obrigatória'),
      }),
      cookieTime: yup.number().when('affiliate', {
        is: true,
        then: yup.number().required('Aprovação manual é obrigatória'),
      }),
    },
  };

  const schema = yup.object().shape({
    ...schemas.general,
    ...schemas.settings,
    ...schemas.affiliate,
  });

  const defaultValues = useMemo(
    () => ({
      name: product?.name || '',
      description: product?.description || '',
      category: product?.category || '',
      image: product?.image ? { preview: product?.image } : null,
      price: product?.price || '0.00',
      guarantee: product?.guarantee || null,
      paymentType: product?.paymentType || 1,
      salesPage: product?.salesPage || '',
      supportEmail: product?.supportEmail || '',
      affiliateSupportEmail: product?.affiliateSupportEmail || '',
      producerName: product?.producerName || '',
      type: product?.type || '',
      differentOffers: product?.differentOffers || false,
      offers: product?.offers || _prices,
      paymentMethod: product?.paymentMethod,
      contentDeliveries: product?.contentDeliveries || [],
      paymentMethods: product?.paymentMethods || [],
      invoiceDescription: product?.invoiceDescription || '',
      installments: product?.installments || 1,
      ticketExpiration: product?.ticketExpiration || '',
      confirmEmail: typeof product?.confirmEmail === 'boolean' ? product?.confirmEmail : true,
      defaultPaymentMethod: product?.defaultPaymentMethod
        ? product?.defaultPaymentMethod
        : 'credit_card',
      redirectUpsellWithBumpFail:
        typeof product?.redirectUpsellWithBumpFail === 'boolean'
          ? product?.redirectUpsellWithBumpFail
          : true,
      bumps:
        (product?.bumps
          ? product?.bumps.map((bump) => ({ ...bump, externalId: bump.id }))
          : null) || _bumps,
      affiliate: product?.affiliate || false,
      affiliateRequest: product?.affiliateRequest || false,
      affiliateRequestSales: product?.affiliateRequestSales || false,
      affiliateContact: product?.affiliateContact || false,
      affiliateShareBump:
        product?.affiliateShareBump === undefined ? true : product?.affiliateShareBump,
      affiliateShareUpsell: product?.affiliateShareUpsell || false,
      affiliateMarketplace: product?.affiliateMarketplace || false,
      affiliateProductCategory: product?.affiliateProductCategory || '',
      affiliateDescription: product?.affiliateDescription || '',
      affiliateCommission: product?.affiliateCommission || '0.00',
      affiliateClick: product?.affiliateClick || 'last',
      affiliateCloneQuiz: product?.affiliateCloneQuiz || false,
      affiliateCloneQuizUrl: product?.affiliateCloneQuizUrl || '',
      affiliateSalesPage: product?.affiliateSalesPage || '',
      cookieTime: product?.cookieTime || 1,
      upsell: product?.upsell || false,
      upsellPage: product?.upsellPage || '',
      emailAccessLink: product?.emailAccessLink,
      showCouponField: product?.showCouponField || false,
      showAddressFields: product?.showAddressFields || false,
      threeDsRetryEnabled: isThreeDsEnabled ? true : product?.threeDsRetryEnabled,
      disable_orderbump_pixel_events: product?.disable_orderbump_pixel_events || false,
    }),
    [product, isThreeDsEnabled]
  );

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues,
  });

  const showHasChangesMessage = () => setOpenInformationDialog(true);

  const hasDirtyField = (field) =>
    Object.values(field).some(
      (value) => value === true || (Array.isArray(value) && value.some((v) => v === true))
    );

  const handleReturnPage = async () => {
    const { dirtyFields } = form.formState;
    if (dirtyFields.offers) {
      await refetchProductData();
    }
    navigate('/dashboard/products?tab=products');
  };

  useEffect(() => {
    const { dirtyFields } = form.formState;
    const filteredDirtyFields = { ...dirtyFields };
    delete filteredDirtyFields.offers;
    const hasDirtyFields = hasDirtyField(filteredDirtyFields);
    setIsDirtyFields(hasDirtyFields);
  }, [form.formState]);

  const onClickGoBack = async () => {
    if (isDirtyFields) {
      showHasChangesMessage();
    } else {
      await handleReturnPage();
    }
  };

  const validateTabSchema = useCallback(
    ({ tab }) => Object.keys(schemas[tab]).some((key) => form.formState.errors[key]),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form.formState.errors]
  );

  useEffect(() => {
    const [invalidTab] = [
      !validateTabSchema({ tab: 'affiliate' }) ? false : 'affiliates',
      !validateTabSchema({ tab: 'general' }) ? false : 'general',
      !validateTabSchema({ tab: 'settings' }) ? false : 'settings',
    ].filter((item) => item);

    if (invalidTab) {
      navigate(`${pathname}?tab=${invalidTab}`);
      setTabValue(invalidTab);
    }

    form.setFocus('description');
  }, [form, validateTabSchema, navigate, pathname]);

  const Tabs = useMemo(
    () =>
      [
        {
          value: 'general',
          label: 'Geral',
          valid: isAffiliate ? true : !validateTabSchema({ tab: 'general' }),
          visible: !isAffiliate,
        },
        {
          value: 'settings',
          label: 'Configurações',
          valid: isAffiliate ? true : !validateTabSchema({ tab: 'settings' }),
          visible: true,
        },
        {
          value: 'orderBump',
          label: 'Order bump',
          valid: true,
          visible: !isAffiliate,
        },
        {
          value: 'upSell',
          label: 'Upsell / Downsell',
          valid: true,
          visible: !isAffiliate,
        },
        {
          value: 'checkout',
          label: 'Checkout',
          valid: true,
          visible: !isAffiliate,
        },
        {
          value: 'coproduction',
          label: 'Co-produção',
          valid: true,
          visible: !isAffiliate,
        },
        {
          value: 'coupons',
          label: 'Cupons',
          valid: true,
          visible: true,
        },
        {
          value: 'affiliates',
          label: 'Afiliados',
          valid: isAffiliate ? true : !validateTabSchema({ tab: 'affiliate' }),
          visible: !isAffiliate,
        },
        {
          value: 'members',
          label: 'Área de membros',
          valid: true,
          visible: !isAffiliate && (product?.contentDeliveries?.includes('cakto') || product?.contentDeliveries?.includes('cakto_v2')),
          hasDropdown: canUserAccessMembersV2(user),
        },
        // {
        //   value: 'invoice',
        //   label: 'Nota Fiscal',
        //   valid: true,
        //   visible: true,
        // },
        {
          value: 'links',
          label: 'Links',
          valid: true,
          visible: true,
        },
      ].map(
        (tab) =>
          tab.visible && (
            <Tab
              key={tab.value}
              value={tab.value}
              label={
                <Typography
                  variant="body2"
                  color={tab.valid ? 'text.primary' : 'error.main'}
                  fontWeight={tab.valid ? 'normal' : 'bold'}
                >
                  {tab.label}
                  {!tab.valid && ' *'}
                </Typography>
              }
              onClick={(event) => {
                if (tab.hasDropdown) {
                  event.preventDefault();
                  setMembersMenuAnchor(event.currentTarget);
                }
              }}
            />
          )
      ),
    [isAffiliate, product?.contentDeliveries, validateTabSchema, user]
  );

  useEffect(() => {
    form.reset(defaultValues);

    form.setValue('guarantee', product?.guarantee ?? 7);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValues, product]);

  const onSubmit = async (data) => {
    setFirstError('');
    await updateProduct({ ...data, offers: form.getValues('offers') });

    const [firstError] = Object.keys(form.formState.errors);
    setFirstError(firstError);
  };

  return (
    <FormProvider methods={form}>
      <Helmet>
        <title> Editar Produto </title>
      </Helmet>

      <InformThatThereChanges
        onClose={() => setOpenInformationDialog(false)}
        open={openInformationDialog}
      />
      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <Stack direction="row" justifyContent="space-between" mb={3}>
          <Button
            variant="text"
            startIcon={<Iconify icon="ion:arrow-back-outline" />}
            onClick={onClickGoBack}
          >
            Voltar
          </Button>
          {!isAffiliate && (
            <LoadingButton
              variant="contained"
              type="button"
              loading={isUpdatingProduct}
              onClick={form.handleSubmit(onSubmit)}
            >
              Salvar Produto
            </LoadingButton>
          )}
        </Stack>
        <TabContext value={tabValue}>
          {product?.status === 'blocked' && (
            <ProductWarning
              title="Produto Bloqueado"
              message="Este produto encontra-se bloqueado. Contate o suporte para mais informações."
            />
          )}
          <Card>
            <TabList
              value={tabValue}
              onChange={(_, newTab) => {
                if (newTab === 'members') {
                  // Verificar se o usuário pode acessar a Members V2
                  if (canUserAccessMembersV2(user)) {
                    // Não mudar a aba, apenas abrir o menu dropdown
                    return;
                  }

                  navigate(`${PATH_DASHBOARD.general.products.membersArea(id)}?tab=content`);
                } else {
                  navigate(`${pathname}?tab=${newTab}`);
                }
                setTabValue(newTab);
              }}
              sx={{ p: 1, px: 3 }}
            >
              {Tabs}
            </TabList>
          </Card>

          {/* Menu dropdown para área de membros */}
          <Menu
            anchorEl={membersMenuAnchor}
            open={Boolean(membersMenuAnchor)}
            onClose={() => setMembersMenuAnchor(null)}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            PaperProps={{
              sx: {
                mt: 1,
                minWidth: 280,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              }
            }}
          >
            <MenuItem
              onClick={() => {
                setMembersMenuAnchor(null);
                navigate(`${PATH_DASHBOARD.general.products.membersArea(id)}?tab=content`);
              }}
              sx={{ py: 1.5, px: 2 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <Iconify icon="eva:book-open-fill" />
                <Typography variant="body2">Área de membros v1</Typography>
              </Box>
            </MenuItem>
            <MenuItem
              onClick={() => {
                setMembersMenuAnchor(null);
                const membersV2Url = generateMembersV2Url(id, product?.name);
                window.open(membersV2Url, '_blank');
              }}
              sx={{ py: 1.5, px: 2 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <Iconify icon="eva:external-link-fill" />
                <Typography variant="body2">Área de membros v2</Typography>
                <Chip
                  label="Beta"
                  size="small"
                  sx={{
                    backgroundColor: '#ff6b35',
                    color: 'white',
                    fontSize: '10px',
                    height: 20,
                    '& .MuiChip-label': {
                      px: 1,
                    }
                  }}
                />
              </Box>
            </MenuItem>
          </Menu>

          <TabPanel sx={{ p: 0, mt: 2 }} value="general">
            <EditProductGeneral />
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="settings">
            <EditProductSettings />
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="checkout">
            <CheckoutProvider>
              <EditProductCheckout />
            </CheckoutProvider>
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="orderBump">
            <OrderBumpBlock />
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="upSell">
            <Stack direction="row" spacing={2} alignItems="center" my={2} justifyContent="center" >
              <a href="https://app.funeleiro.com.br" target="_blank" rel="noopener noreferrer">
                <Box
                  component="img"
                  src="https://cdn-checkout.cakto.com.br/banners/01-FUNILEIROdash.png"
                  alt="Funeleiro"
                  className='rounded-md'
                  sx={{
                    display: { xs: 'none', md: 'block' },
                    maxWidth: '100%',
                    height: 'auto'
                  }}
                />
                <Box
                  component="img"
                  src="https://cdn-checkout.cakto.com.br/banners/01-FUNILEIROapp.png"
                  alt="Funeleiro"
                  className='rounded-md'
                  sx={{
                    display: { xs: 'block', md: 'none' },
                    maxWidth: '100%',
                    height: 'auto'
                  }}
                />
              </a>
            </Stack>

            <UpsellDownsellBlock />
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="coproduction">
            <ProductCoProductionsProvider type="producer">
              <EditProductCoProduction />
            </ProductCoProductionsProvider>
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="coupons">
            <ProductCoupons />
          </TabPanel>

          <TabPanel sx={{ p: 0, mt: 2 }} value="affiliates">
            <EditProductAffiliated />
          </TabPanel>

          {/* <TabPanel sx={{ p: 0, mt: 2 }} value="invoice">
            <Invoice />
          </TabPanel> */}

          <TabPanel sx={{ p: 0, mt: 2 }} value="links">
            <ProductLinksProvider>
              <EditProductLinks />
            </ProductLinksProvider>
          </TabPanel>
        </TabContext>

        {!isAffiliate && (
          <Stack direction="row" justifyContent="space-between" mt={3}>
            <Stack direction="row" spacing={1}>
              <Button variant="contained" color="error" onClick={() => setOpenConfirmDelete(true)}>
                Excluir Produto
              </Button>
              {product?.type === ProductType.subscription.value && (
                <Button
                  variant="outlined"
                  color="error"
                  onClick={() => setOpenConfirmCancelSubscriptions(true)}
                >
                  Cancelar assinaturas
                </Button>
              )}
            </Stack>
            <LoadingButton
              type="button"
              variant="contained"
              loading={isUpdatingProduct}
              onClick={form.handleSubmit(onSubmit)}
            >
              Salvar Produto
            </LoadingButton>
          </Stack>
        )}

        <ConfirmDialogAdvanced
          action="Excluir"
          title="Você tem certeza que deseja excluir esse produto?"
          content={
            <Stack gap={1}>
              <Typography variant="body2" color="text.secondary">
                Essa ação é irreversível e você perderá:
              </Typography>
              <Stack color="text.secondary">
                <ListItem disableGutters>
                  <ListItemIcon>
                    <Iconify icon="ic:twotone-block" />
                  </ListItemIcon>
                  <ListItemText disableGutters secondary="A sua área de membros" />
                </ListItem>
                <ListItem disableGutters>
                  <ListItemIcon>
                    <Iconify icon="ic:twotone-block" />
                  </ListItemIcon>
                  <ListItemText disableGutters secondary="Links e vídeos" />
                </ListItem>
                <ListItem disableGutters>
                  <ListItemIcon>
                    <Iconify icon="ic:twotone-block" />
                  </ListItemIcon>
                  <ListItemText disableGutters secondary="Todas as configurações do produto" />
                </ListItem>
              </Stack>
              <Typography variant="body2" color="text.secondary">
                Se você quer apenas desabilitar os links do checkout,{' '}
                <Link target="_blank" href={disableProductCheckoutUrl}>
                  aprenda como aqui.
                </Link>
              </Typography>
            </Stack>
          }
          onAction={deleteProduct}
          loading={isDeletingProduct}
          word="EXCLUIR"
          open={openConfirmDelete}
          onClose={() => setOpenConfirmDelete(false)}
        />

        {product?.type === ProductType.subscription.value && (
          <ConfirmDialogAdvanced
            action="Cancelar assinaturas"
            title="Você tem certeza que deseja cancelar TODAS as assinaturas desse produto?"
            content={
              <Stack gap={1} color="text.secondary">
                <Typography variant="body2">Essa ação é irreversível.</Typography>
                <Typography variant="body2">
                  Todas as assinaturas serão canceladas, e os clientes perderão o acesso a área de
                  membros na data de vencimento do plano.
                </Typography>
                <Typography variant="body2">
                  As vendas não serão reembolsadas. Apenas as assinaturas serão canceladas,
                  impedindo novas renovações.
                </Typography>
              </Stack>
            }
            onAction={() =>
              cancelSubscriptions().then(() => setOpenConfirmCancelSubscriptions(false))
            }
            loading={isCancelingSubscriptions}
            word="CANCELAR ASSINATURAS"
            open={openConfirmCancelSubscriptions}
            onClose={() => setOpenConfirmCancelSubscriptions(false)}
          />
        )}

      </Container>
    </FormProvider>
  );
}
