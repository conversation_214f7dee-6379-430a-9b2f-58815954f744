import { useQuery } from '@tanstack/react-query';
import { useSettingsContext } from '../../../components/settings/SettingsContext';
import { getPlatforms } from '../../../services/apps';

export default function useApps() {
  const { themeMode } = useSettingsContext();

  const { data: platforms, isFetching } = useQuery({
    queryKey: ['platforms'],
    queryFn: getPlatforms,
    select: ({ results }) => results,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });

  return {
    themeMode,
    platforms,
    isFetching,
  };
}
