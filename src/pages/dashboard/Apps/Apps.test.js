import { fetchBannerService } from '@/services/banner';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { HelmetProvider } from 'react-helmet-async';
import { PLATFORMS_CONFIG } from '../../../consts/dashboardApps';
import Apps from './Apps';
import useApps from './useApps';

jest.mock('./useApps');
jest.mock('react-helmet-async', () => ({ 
  Helmet: ({ children }) => <>{children}</>,
  HelmetProvider: ({ children }) => <>{children}</>
}));
jest.mock('../../../sections/@dashboard/apps/AppWidget/AppWidget', () => ({ onClick, image }) => (
  <div data-testid="app-widget" onClick={onClick} style={{ backgroundImage: `url(${image})` }}>
    AppWidget
  </div>
));
jest.mock(
  '../../../sections/@dashboard/apps/AppsIntegrationsDrawer/AppsIntegrationsDrawer',
  () =>
    ({ open, onClose, platform }) =>
      open ? <div data-testid="app-integrations-drawer">AppIntegrationsDrawer</div> : null
);

jest.mock('@/services/banner', () => ({
  fetchBannerService: jest.fn(),
}));

const createWrapper = (queryClient) => {
  return ({ children }) => (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </HelmetProvider>
  );
};


describe('Apps Component', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  const platformsMock = [
    { id: '1', type: 'type1' },
    { id: '2', type: 'type2' },
  ];

  beforeEach(() => {
    useApps.mockReturnValue({
      isFetching: false,
      platforms: platformsMock,
      themeMode: 'light',
    });
  });

  it('renders loading skeletons when fetching', () => {
    useApps.mockReturnValueOnce({
      isFetching: true,
      platforms: [],
      themeMode: 'light',
    });
    fetchBannerService.mockResolvedValue([]);

    render(<Apps />, { wrapper: createWrapper(queryClient) });
    const skeletons = screen.getAllByTestId('app-loading');
    expect(skeletons).toHaveLength(8);
  });

  it('renders the AppWidgets when platforms are available', () => {
    fetchBannerService.mockResolvedValue([]);
    render(<Apps />, { wrapper: createWrapper(queryClient) });
    const appWidgets = screen.getAllByTestId('app-widget');
    expect(appWidgets).toHaveLength(platformsMock.length);
  });

  it('opens the AppsIntegrationsDrawer when an AppWidget is clicked', async () => {
    fetchBannerService.mockResolvedValue([]);
    render(<Apps />, { wrapper: createWrapper(queryClient) });
    const appWidget = screen.getAllByTestId('app-widget')[0];
    fireEvent.click(appWidget);

    await waitFor(() => {
      const drawer = screen.getByTestId('app-integrations-drawer');
      expect(drawer).toBeInTheDocument();
    });
  });

  it('renders the correct platform image in AppWidgets', () => {
    fetchBannerService.mockResolvedValue([]);
    render(<Apps />, { wrapper: createWrapper(queryClient) });
    const appWidgets = screen.getAllByTestId('app-widget');
    appWidgets.forEach((widget, index) => {
      const platform = platformsMock[index];
      const image = PLATFORMS_CONFIG[platform.type]?.image.light || '';
      expect(widget).toHaveStyle(`background-image: url(${image})`);
    });
  });
});
