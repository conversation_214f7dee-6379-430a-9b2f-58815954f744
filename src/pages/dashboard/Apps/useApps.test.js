import { expect } from '@jest/globals';
import { useQuery } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react-hooks';
import useApps from './useApps';

jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

describe('useApps', () => {
  it('should return the correct values', () => {
    const mockPlatformsData = { results: [{ id: 1, name: 'Platform 1' }] };
    useQuery.mockImplementation(({ select, queryFn }) => {
      queryFn = jest.fn();
      const data = mockPlatformsData;
      return {
        data: select(data),
        isFetching: true,
      };
    });

    const { result } = renderHook(() => useApps());

    expect(result.current.platforms).toEqual(mockPlatformsData.results);
    expect(result.current.isFetching).toEqual(true);
  });
});
