// @mui
import { Grid, Skeleton, Typography } from '@mui/material';
// sections
import { PLATFORMS_CONFIG } from '@/consts/dashboardApps';
import { AppWidget, AppsIntegrationsDrawer } from '@/sections/@dashboard/apps';
import { Container } from '@mui/system';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
// custom hooks
import { CarouselBanner } from '@/sections/@dashboard/home';
import { fetchBannerService } from '@/services/banner';
import { useQuery } from '@tanstack/react-query';
import useApps from './useApps';

export default function Apps() {
  const [open, setOpen] = useState(false);

  const [platform, setPlatform] = useState({});

  const { data, isFetching: isBannerFetching } = useQuery(['apps-banners'], {
    queryFn: () => fetchBannerService({ location: 'apps' }),
    initialData: [],
  });

  const { isFetching, platforms, themeMode } = useApps();

  const handleOpenPlataform = (plataform) => {
    setPlatform(plataform);
    setOpen(true);
  };

  const onCloseAppsIntegrationsDrawer = () => setOpen(false);

  return (
    <Grid container spacing={3}>
      <Helmet>
        <title> Dashboard | Apps </title>
      </Helmet>

      {platform && (
        <AppsIntegrationsDrawer
          open={open}
          platform={platform}
          onClose={onCloseAppsIntegrationsDrawer}
        />
      )}

      <Container maxWidth="lg">
        <CarouselBanner banners={data} fetching={isBannerFetching} />

        <Grid container spacing={3} marginBottom={3} p={2}>
          <Grid item xs={12}>
            <Typography variant="h4">Apps</Typography>
          </Grid>

          {Array.from({ length: 8 })
            .filter(() => isFetching)
            .map((_, index) => (
              <Grid data-testid="app-loading" key={index} item xs={12} md={3}>
                <Skeleton variant="rectangular" height={128} style={{ borderRadius: 10 }} />
              </Grid>
            ))}

          {platforms
            ?.filter(() => !isFetching)
            ?.map((p) => (
              <Grid key={p.id} item xs={12} md={3}>
                <AppWidget
                  data-testid="app-widget"
                  image={PLATFORMS_CONFIG[p.type]?.image[themeMode] || ''}
                  onClick={() => {
                    handleOpenPlataform(p);
                  }}
                />
              </Grid>
            ))}
        </Grid>
      </Container>
    </Grid>
  );
}
