/* eslint-disable no-restricted-globals */
import { useAuthContext } from '@/auth/useAuthContext';
import { NPS } from '@/components';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useForm } from 'react-hook-form';

// @mui
import { Container, Grid, MenuItem, Typography } from '@mui/material';

// sections
import { RHFMultiSelect, RHFSelect } from '@/components/hook-form';
import FormProvider from '@/components/hook-form/FormProvider';
import RHFDateRangePicker from '@/components/hook-form/RHFDateRangePicker';
import VotingBanner from '@/components/voting-banner';
import {
  CarouselBanner,
  DetailsCard,
  HomeLastSales,
  HomeWidgetSummary,
  PaymentMethodsTable,
} from '@/sections/@dashboard/home';
import CreatePasswordModal from '@/sections/auth/CreatePasswordModal';
import { fetchBannerService } from '@/services/banner';
import { getDashboardData } from '@/services/dashboard';
import { getProductsService } from '@/services/products';

// utils
import { fCurrency } from '@/utils/formatNumber';
import FunnelConversion from '@/sections/home/<USER>';
import { usePageView } from '../../utils/FacebookPixel';

import { InfoRegistration } from '../components/inform-pending-registration';
// ----------------------------------------------------------------------
export default function DashboardHomePage() {
  usePageView();

  const { user } = useAuthContext();

  const companyStatus = user?.companyStatus;

  const show = localStorage.getItem(InfoRegistration.STATUS) !== InfoRegistration.DISPLAYED;
  const [openInform, setOpenInform] = useState(companyStatus !== 'approved');
  const [openNewPasswordFormModal, setOpenNewPasswordFormModal] = useState(false);

  const form = useForm({
    defaultValues: {
      products: [],
      startDate: new Date(),
      endDate: new Date(),
    },
  });

  const { products: selectedProducts, startDate, endDate, coproductionType } = form.watch();

  const { data: products, isFetching: fetchingProducts } = useQuery({
    queryKey: ['products', coproductionType],
    queryFn: () =>
      getProductsService({
        page: 1,
        limit: 1000,
        status: 'active',
        productionType: coproductionType,
      }),
    select: (data) =>
      data.products.map((product) => ({ label: product?.name, value: product?.id })),
    initialData: {
      products: [],
    },
  });

  const paymentMethodMapper = {
    pix: () => ({ icon: 'simple-icons:pix', title: 'Pix' }),
    pix_auto: () => ({ icon: 'simple-icons:pix', title: 'PIX Automático' }),
    credit_card: () => ({ icon: 'ic:round-credit-card', title: 'Cartão de crédito' }),
    picpay: () => ({ icon: 'simple-icons:picpay', title: 'Pic Pay' }),
    applepay: () => ({ icon: 'simple-icons:applepay', title: 'Apple Pay' }),
    googlepay: () => ({ icon: 'simple-icons:googlepay', title: 'Google Pay' }),
    nupay: () => ({ icon: 'simple-icons:nubank', title: 'Nupay' }),
    boleto: () => ({ icon: 'ic:outline-barcode', title: 'Boleto' }),
    openfinance_nubank: () => ({ icon: 'simple-icons:nubank', title: 'Nubank' }),
    threeDs: () => ({ icon: 'quill:creditcard', title: '3DS' }),
  };

  const {
    data: { totalSalesAmount, totalSales, chartData, detailsData, paymentMethodsData } = {
      totalSalesAmount: 0,
      averageOrderAmount: 0,
      totalSales: 0,
      chartData: [
        {
          amount: 0,
          totalSales: 0,
        },
      ],
      detailsData: [
        { title: 'Abandono C.', value: 0 },
        { title: 'Reembolso', value: 0 },
        { title: 'Charge Back', value: 0 },
        { title: 'MED', value: 0 },
      ],
      paymentMethodsData: [],
      orderMEDPercentage: 0,
    },
    isFetching,
  } = useQuery({
    queryKey: ['dashboard', selectedProducts, startDate, endDate],
    queryFn: () =>
      getDashboardData({
        products: selectedProducts,
        startDate,
        endDate,
      }),
    select: (data) => ({
      totalSalesAmount: data.totalSalesAmount,
      totalSales: data.totalSales,
      chartData: data.chartData,
      detailsData: [
        { title: 'Abandono C.', value: data.salesAbandonment, isNumeral: true },
        { title: 'Reembolso', value: data.orderRefundPercentage },
        { title: 'Charge Back', value: data.chargebackAmount },
        { title: 'MED', value: data.orderMEDPercentage },
      ],
      paymentMethodsData: data.paymentMethods.map((method) => ({
        ...data[method],
        ...paymentMethodMapper[method](),
      })),
    }),

    keepPreviousData: true,
  });

  const { data = [], isFetching: isBannerFetching } = useQuery(['fetchBannerService'], {
    queryFn: () => fetchBannerService({ location: 'dashboard' }),
  });

  useEffect(() => {
    if (!user.has_usable_password) {
      setOpenNewPasswordFormModal(true);
    }
  }, [user]);

  return (
    <>
      <FormProvider methods={form}>
        <Helmet>
          <title> Dashboard | Página Inicial</title>
        </Helmet>

        <Container maxWidth="lg">
          <VotingBanner />
          <CarouselBanner banners={data} fetching={isBannerFetching} />

          <Grid container spacing={2} alignItems="stretch" justifyContent="center">
            <Grid item xs={12} md={3}>
              <Typography variant="h4">Dashboard</Typography>
            </Grid>

            <Grid item xs={12} md={3}>
              <RHFSelect name="coproductionType" label="Tipo" size="small">
                <MenuItem value='producer,coproducer,affiliate'>Todos</MenuItem>
                <MenuItem value='producer'>Sou produtor</MenuItem>
                <MenuItem value='coproducer'>Sou co-produtor</MenuItem>
                <MenuItem value='affiliate'>Sou afiliado</MenuItem>
              </RHFSelect>
            </Grid>

            <Grid item xs={12} md={3}>
              <RHFMultiSelect
                size="small"
                checkbox
                chip
                fullWidth
                name="products"
                label="Produtos"
                options={products}
                disabled={fetchingProducts}
                sx={{ width: '100%' }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <RHFDateRangePicker label="Período" initialPeriod="today" />
            </Grid>

            <Grid item xs={12} md={6}>
              <HomeWidgetSummary
                title="Vendas realizadas"
                percent={0.6}
                total={fCurrency(totalSalesAmount || 0)}
                fetching={isFetching}
                fontSize={{
                  xs: 25,
                  md: 28,
                }}
                showInfo
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <HomeWidgetSummary
                title="Quantidade de vendas"
                percent={2.6}
                total={totalSales}
                fetching={isFetching}
                fontSize={{
                  xs: 25,
                  md: 28,
                }}
                showInfo
              />
            </Grid>

            <Grid item xs={12} md={8} sx={{ display: 'flex', flexDirection: 'column' }}>
              <PaymentMethodsTable
                data={paymentMethodsData}
                fetching={isFetching}
                sx={{ flexGrow: 1 }}
              />
            </Grid>

            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column' }}>
              <DetailsCard data={detailsData} fetching={isFetching} sx={{ flexGrow: 1 }} />
            </Grid>

            <Grid item xs={12}>
              <HomeLastSales fetching={isFetching} chart={chartData} />
            </Grid>

            <Grid item xs={12}>
              <FunnelConversion
                isFetching={isFetching}
                productId={selectedProducts[0]}
                startDate={startDate}
                endDate={endDate}
              />
            </Grid>
          </Grid>
        </Container>
      </FormProvider>
      <NPS />
      {openNewPasswordFormModal && (
        <CreatePasswordModal
          open={openNewPasswordFormModal}
          handleClose={setOpenNewPasswordFormModal}
        />
      )}
    </>
  );
}
