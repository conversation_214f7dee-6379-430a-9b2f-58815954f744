import { Helmet } from 'react-helmet-async';
// @mui
import { <PERSON><PERSON>, Card, Grid, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
// sections
import { ReportCoursesEngagementTable } from '../../sections/@dashboard/reports';

// ----------------------------------------------------------------------
export default function ReportCoursesEngagement() {
  // TODO ESTE COMPONENTE É MAIS UTILIZADO
  return (
    <Grid spacing={3}>
      <Helmet>
        <title> Engajamento dos alunos </title>
      </Helmet>

      <Grid
        container
        spacing={3}
        marginBottom={3}
        justifyContent="center"
      >
        <Grid container item xs={11} md={8} lg={8}>
          <Grid
            container
            item
            marginBottom={3}
            justifyContent="space-between"
            spacing={3}
          >
            <Grid container item spacing={3} xs={11} md={8} lg={8}>
              <Grid item>
                <DatePicker
                  label="Start date"
                  renderInput={(params) => <TextField size="small" {...params} />}
                />
              </Grid>

              <Grid item>
                <DatePicker
                  label="End date"
                  renderInput={(params) => <TextField size="small" {...params} />}
                />
              </Grid>
            </Grid>

            <Grid item>
              <Button
                variant="contained"
              >
                Exportar
              </Button>
            </Grid>
          </Grid>

          <Grid item xs={12} md={12} lg={12}>
            <Card>
              <ReportCoursesEngagementTable
                tableData={[
                  {
                    curse: "Curse 01",
                    comments: "Lorem Ipsum is simply dummy text of the printing and typesetting industry lorem Ipsum.",
                    studentsCompleted: "120",
                    assessments: "62",
                    averageRating: "4/5"
                  }
                ]}
                tableLabels={[
                  { id: 'curse', label: 'Curso' },
                  { id: 'comments', label: 'Comentários' },
                  { id: 'studentsCompleted', label: 'Alunos Completaram' },
                  { id: 'assessments', label: 'Avaliações' },
                  { id: 'averageRating', label: 'Avaliação média' },
                ]}
              />
            </Card>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
