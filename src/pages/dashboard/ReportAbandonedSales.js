import { Helmet } from 'react-helmet-async';
// @mui
import { Card, Grid, IconButton, MenuItem, Typography } from '@mui/material';
// sections
import { LoadingButton } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { RHFMultiSelect } from '../../components/hook-form';
import FormProvider from '../../components/hook-form/FormProvider';
import RHFDateRangePicker, { PERIODS } from '../../components/hook-form/RHFDateRangePicker';
import Iconify from '../../components/iconify/Iconify';
import MenuPopover from '../../components/menu-popover/MenuPopover';
import useTableFilter from '../../hooks/useTableFilter';
import { ReportAbandonedSalesTable } from '../../sections/@dashboard/reports';
import { getProductsService } from '../../services/products';
import { exportAbandonedSalesReport, getAbandonedSalesReport } from '../../services/reports';

// ----------------------------------------------------------------------
export default function ReportAbandonedSales() {
  const { enqueueSnackbar } = useSnackbar();

  const [anchorEl, setAnchorEl] = useState(null);

  const { data: productsOptions, isFetching: fetchingProducts } = useQuery({
    queryKey: ['products'],
    queryFn: () =>
      getProductsService({
        page: 1,
        limit: 1000,
      }),
    select: (data) =>
      data.products.map((product) => ({ label: product?.name, value: product?.id })),
    initialData: {
      products: [],
    },
  });

  const table = useTableFilter({
    defaultCurrentPage: 1,
  });

  const form = useForm({
    defaultValues: {
      period: PERIODS.TODAY,
      startDate: new Date(),
      endDate: new Date(),
      products: [],
    },
  });

  const { startDate, endDate, products } = form.watch();

  const { data: reportData, isFetching: fetchingReport } = useQuery({
    queryKey: ['report-abandoned-sales', startDate, endDate, products, table.page, table.search],
    queryFn: () =>
      getAbandonedSalesReport({
        startDate,
        endDate,
        products,
        page: table.page,
        search: table.search,
      }),
    initialData: {
      results: [],
      count: 0,
    },
  });

  const { mutateAsync: exportReport, isLoading: exporting } = useMutation({
    mutationFn: ({ extension }) =>
      exportAbandonedSalesReport({ startDate, endDate, products, extension, search: table.search }),
    onSuccess: () => {
      enqueueSnackbar('Exportação realizada com sucesso', { variant: 'success' });
    },
    onError: () => {
      enqueueSnackbar('Erro ao exportar', { variant: 'error' });
    },
  });

  return (
    <FormProvider methods={form}>
      <Helmet>
        <title> Relatórios | Vendas abandonadas </title>
      </Helmet>

      <MenuPopover open={anchorEl} onClose={() => setAnchorEl(null)} sx={{ width: 160 }}>
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            exportReport({ extension: 'xlsx' });
          }}
        >
          XLS
        </MenuItem>
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            exportReport({ extension: 'csv' });
          }}
        >
          CSV
        </MenuItem>
      </MenuPopover>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Stack direction="row" gap={1} alignItems="center" mb={1}>
            <Link to="/dashboard/reports">
              <IconButton>
                <Iconify icon="eva:arrow-ios-back-outline" />
              </IconButton>
            </Link>
            <Stack>
              <Typography variant="h4">Relatório</Typography>
              <Typography variant="body2" color="text.secondary" fontFamily="monospace">
                Vendas abandonadas
              </Typography>
            </Stack>
          </Stack>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <RHFDateRangePicker label="Período" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <RHFMultiSelect
            size="small"
            checkbox
            chip
            fullWidth
            name="products"
            label="Produtos"
            options={productsOptions}
            disabled={fetchingProducts}
            sx={{ width: '100%' }}
          />
        </Grid>
        <Grid
          item
          xs={12}
          md={4}
          sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'flex-end' }}
        >
          <LoadingButton
            endIcon={<Iconify icon="eva:arrow-ios-downward-outline" />}
            size="medium"
            variant="contained"
            loading={exporting}
            onClick={(event) => {
              setAnchorEl(event.currentTarget);
            }}
          >
            Exportar
          </LoadingButton>
        </Grid>
        <Grid minWidth="1200px" item xs={12}>
          <ReportAbandonedSalesTable
            records={reportData.results}
            count={reportData.count}
            fetching={fetchingReport}
            table={table}
          />
        </Grid>
      </Grid>
    </FormProvider>
  );
}
