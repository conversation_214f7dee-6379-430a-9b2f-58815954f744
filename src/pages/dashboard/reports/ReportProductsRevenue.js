import { useContext, useState } from 'react';
// sections
import {
  ReportBalanceStatisticsProducts,
  ReportProductsRevenueTable,
  ReportBody,
} from '../../../sections/@dashboard/reports';
// contexts
import { ReportProductsContext } from '../../../contexts/ReportProductsContext';

// ----------------------------------------------------------------------
export default function ReportProductsRevenue() {
  const [type, setType] = useState('bar');
  const { dates, setDates, mutateExport, loadingExport, productsReport } =
    useContext(ReportProductsContext);

  const onChangeDate = (value, key) => {
    if (value?.toString().length > 12) setDates({ ...dates, [key]: value });
  };

  return (
    <ReportBody
      title="Receita por produto"
      type={type}
      setType={setType}
      onChangeDate={onChangeDate}
      dates={dates}
      exportCSV={() => mutateExport('csv')}
      exportXLSX={() => mutateExport('xlsx')}
      loadingExport={loadingExport}
    >
      <>
        <ReportBalanceStatisticsProducts type={type} report={productsReport} />
        <ReportProductsRevenueTable />
      </>
    </ReportBody>
  );
}
