// @mui
// sections
import { useContext } from 'react';
import ReportsBalanceStatisticsEstimateCalendar from '@/sections/@dashboard/reports/ReportsBalanceStatisticsEstimateCalendar';

import { ReportBalanceContext } from '../../../contexts/ReportBalanceContext';
// ----------------------------------------------------------------------
export default function ReportFinancialEstimate() {
  const { dates, setDates, balanceReport } = useContext(ReportBalanceContext);

  return (
    <ReportsBalanceStatisticsEstimateCalendar
      balanceReport={balanceReport}
      setDates={setDates}
      dates={dates}
    />
  );
}
