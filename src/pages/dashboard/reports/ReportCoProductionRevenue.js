import { useContext, useState } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { useNavigate } from 'react-router';
// @mui
import { Tab, Tabs } from '@mui/material';
// sections
import {
  ReportBalanceStatisticsCoProduction,
  ReportCoProductionRevenueTable,
  ReportBody,
} from '../../../sections/@dashboard/reports';
// contexts
import { ReportCoProductionsContext } from '../../../contexts/ReportCoProductionsContext';

// ----------------------------------------------------------------------
export default function ReportCoProductionRevenue() {
  const [searchParams] = useSearchParams();
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const [type, setType] = useState('bar');

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'received');

  const { dates, setDates, mutateExport, loadingExport } = useContext(ReportCoProductionsContext);

  const tableLabels = [
    { id: 'date', label: 'Data' },
    { id: 'value', label: 'Total' },
  ];

  const onChangeDate = (value, key) => {
    if (value?.toString().length > 12) setDates({ ...dates, [key]: value });
  };

  return (
    <ReportBody
      title="Receita de co-produção"
      type={type}
      setType={setType}
      onChangeDate={onChangeDate}
      dates={dates}
      exportXLSX={() =>
        mutateExport({ type: tabValue === 'received' ? 'received' : 'sent', extension: 'xlsx' })
      }
      exportCSV={() =>
        mutateExport({ type: tabValue === 'received' ? 'received' : 'sent', extension: 'csv' })
      }
      loadingExport={loadingExport}
    >
      <Tabs
        value={tabValue}
        onChange={(event, newTab) => {
          setTabValue(newTab);
          navigate(`${pathname}?tab=${newTab}`);
        }}
        style={{ padding: 10, paddingLeft: 20 }}
      >
        <Tab value="received" label="Receita recebida" />
        <Tab value="sent" label="Receita enviada" />
      </Tabs>

      {tabValue === 'received' && (
        <>
          <ReportBalanceStatisticsCoProduction type={type} />
          <ReportCoProductionRevenueTable type="received" tableLabels={tableLabels} />
        </>
      )}

      {tabValue === 'sent' && (
        <>
          <ReportBalanceStatisticsCoProduction type={type} />
          <ReportCoProductionRevenueTable type="sent" tableLabels={tableLabels} />
        </>
      )}
    </ReportBody>
  );
}
