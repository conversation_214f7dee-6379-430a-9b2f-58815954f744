import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Stack, Container, Box } from '@mui/system';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { Card, Tab, useTheme } from '@mui/material';
import Iconify from '@/components/iconify';

// Contextos
import ReportCoProductionsProvider from '@/contexts/ReportCoProductionsContext';
import ReportProductsProvider from '@/contexts/ReportProductsContext';
import ReportAffiliateProvider from '@/contexts/ReportAffiliateContext';
import ReportBallanceProvider from '@/contexts/ReportBalanceContext';

// Componentes de relatório
import ReportCoProductionRevenue from './ReportCoProductionRevenue';
import ReportProductsRevenue from './ReportProductsRevenue';
// Rotas
import { PATH_DASHBOARD } from '../../../routes/paths';
import ReportWidget from '../../../sections/@dashboard/reports/ReportWidget';
import ReportAbandonedSales from '../ReportAbandonedSales';
import ReportAffiliatesRevenue from './ReportAffiliatesRevenue';
import ReportFinancialEstimate from './ReportFinancialEstimate';

export default function Reports() {
  const [tabValue, setTabValue] = useState('coproduction_revenue');

  const handleTabChange = (event, newTab) => {
    setTabValue(newTab);
  };

  return (
    <>
      <Helmet>
        <title> Dashboard | Relatórios </title>
      </Helmet>

      <TabContext value={tabValue}>
        <Card>
          <TabList
            value={tabValue}
            onChange={handleTabChange}
            sx={{ p: 1, px: 3, background: 'none' }}
          >
            <Tab
              value="coproduction_revenue"
              label="Receita de co-produção"
              icon={<Iconify icon="icon-park-solid:peoples" width={24} />}
              iconPosition="start"
            />
            <Tab
              value="products_revenue"
              label="Receita por produto"
              icon={<Iconify icon="fluent:arrow-bounce-16-filled" width={24} />}
              iconPosition="start"
            />
            <Tab
              value="abandoned_sales"
              label="Vendas abandonadas"
              icon={<Iconify icon="ion:cart" width={24} />}
              iconPosition="start"
            />
            <Tab
              value="affiliates_revenue"
              label="Receita por afiliado"
              icon={<Iconify icon="fluent:people-community-48-filled" width={24} />}
              iconPosition="start"
            />
            <Tab
              value="financial_estimate"
              label="Saldo a receber"
              icon={<Iconify icon="ic:round-account-balance-wallet" width={24} />}
              iconPosition="start"
            />
          </TabList>
        </Card>

        <Card
          style={{
            marginTop: '20px',
            padding: '26px',
          }}
        >
          <TabPanel value="coproduction_revenue" sx={{ p: 0 }}>
            <ReportCoProductionsProvider>
              <ReportCoProductionRevenue />
            </ReportCoProductionsProvider>
          </TabPanel>

          <TabPanel value="products_revenue" sx={{ p: 0 }}>
            <ReportProductsProvider>
              <ReportProductsRevenue />
            </ReportProductsProvider>
          </TabPanel>

          <TabPanel value="abandoned_sales" sx={{ p: 0 }}>
            <ReportAbandonedSales />
          </TabPanel>

          <TabPanel value="affiliates_revenue" sx={{ p: 0 }}>
            <ReportAffiliateProvider>
              <ReportAffiliatesRevenue />
            </ReportAffiliateProvider>
          </TabPanel>
          <TabPanel value="financial_estimate" sx={{ p: 0 }}>
            <ReportBallanceProvider>
              <ReportFinancialEstimate />
            </ReportBallanceProvider>
          </TabPanel>
        </Card>
      </TabContext>
    </>
  );
}
