import { useContext, useMemo, useState } from 'react';
import { ReportAffiliateContext } from '../../../contexts/ReportAffiliateContext';
import {
  ReportAffiliatesRevenueTable,
  ReportBalanceStatisticsProducts,
  ReportBody,
} from '../../../sections/@dashboard/reports';

// ----------------------------------------------------------------------
export default function ReportAffiliatesRevenue() {
  const [type, setType] = useState('bar');
  const { dates, setDates, mutateExport, loadingExport, affiliateReport } =
    useContext(ReportAffiliateContext);

  const onChangeDate = (value, key) => {
    if (value?.toString().length > 12) setDates({ ...dates, [key]: value });
  };

  const report = useMemo(
    () =>
      affiliateReport.map((item) => ({
        name: item.email,
        ...item,
      })),
    [affiliateReport]
  );

  return (
    <ReportBody
      title="Receita por afiliado"
      type={type}
      setType={setType}
      onChangeDate={onChangeDate}
      dates={dates}
      exportCSV={() => mutateExport('csv')}
      exportXLSX={() => mutateExport('xlsx')}
      loadingExport={loadingExport}
    >
      <>
        <ReportBalanceStatisticsProducts type={type} report={report} />
        <ReportAffiliatesRevenueTable />
      </>
    </ReportBody>
  );
}
