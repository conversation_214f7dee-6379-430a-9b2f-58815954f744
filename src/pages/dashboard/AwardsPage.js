import { Card, Typography } from '@mui/material';
import { Container, Stack } from '@mui/system';
import { Helmet } from 'react-helmet-async';
import { ChevronRight, ChevronRightIcon } from 'lucide-react';
import { bottomTiers, topTiers } from '@/utils/constants';
import ProgressLine from '@/pages/components/extra/ProgressLine';
import { useQuery } from '@tanstack/react-query';
import { fetchRanksService } from '@/services/rank';
import LoadingScreen from '@/components/loading-screen/LoadingScreen';
import { useAuthContext } from '@/auth/useAuthContext';
import { Link } from 'react-router-dom';
import { useSettingsContext } from '@/components/settings';
import useResponsive from '@/hooks/useResponsive';
import { useState, useEffect } from 'react';

export default function AwardsPage() {
  const { themeMode } = useSettingsContext();
  const { user } = useAuthContext();
  const totalSales = user?.totalSales || 0;
  const isMobile = useResponsive('down', 'sm');
  const [isDesktop, setisDesktop] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setisDesktop(window.innerWidth >= 1620);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const getThemeColor = (lightColor, darkColor) => themeMode === 'dark' ? darkColor : lightColor;

  const { data, isLoading } = useQuery({
    queryKey: ['ranks'],
    async queryFn() {
      return fetchRanksService();
    },
  });

  if (isLoading) return <LoadingScreen />;

  const getCurrentRank = () => {
    if (!data?.results || data.results.length === 0) return null;

    const sortedRanks = [...data.results].sort((a, b) => a.min_value - b.min_value);

    const currentRank = sortedRanks.reverse().find((rank) => {
      if (totalSales >= rank.min_value && (!rank.max_value || totalSales <= rank.max_value)) {
        return true;
      }
      return false;
    });

    return currentRank;
  };

  const mapRankToTier = (rank) => {
    if (!rank) return null;

    const allTiers = [...bottomTiers, ...topTiers];
    const matchingTier = allTiers.find(tier => tier.code === rank.code);

    return matchingTier || null;
  };

  const currentRank = getCurrentRank();
  const currentTier = mapRankToTier(currentRank);

  const getCurrentLevelPosition = () => {
    if (!currentTier) return null;

    const allTiers = [...bottomTiers, ...topTiers];
    const tierIndex = allTiers.findIndex(tier => tier.name === currentTier.name);

    if (tierIndex === -1) return null;

    return {
      tierIndex,
      isTopTier: tierIndex >= bottomTiers.length,
      adjustedIndex: tierIndex >= bottomTiers.length ? tierIndex - bottomTiers.length : tierIndex
    };
  };

  const currentLevelPosition = getCurrentLevelPosition();

  const getDisplayTiers = (tiers) => isMobile ? [...tiers].reverse() : tiers;

  const getAdjustedIndex = (originalIndex, totalLength) => isMobile ? totalLength - 1 - originalIndex : originalIndex;

  const getDisplayOrder = (tiers) => {
    if (!isMobile) return tiers;
    
    const reversedTiers = [...tiers].reverse();
    const result = [];
    
    for (let i = 0; i < reversedTiers.length; i += 2) {
      if (i + 1 < reversedTiers.length) {
        result.push(reversedTiers[i + 1], reversedTiers[i]);
      } else {
        result.push(reversedTiers[i]);
      }
    }
    
    return result;
  };

  const getDisplayIndex = (originalIndex, tiers) => {
    if (!isMobile) return originalIndex;
    
    const reversedTiers = [...tiers].reverse();
    const originalTier = tiers[originalIndex];
    
    const displayOrder = getDisplayOrder(tiers);
    return displayOrder.findIndex(tier => tier.name === originalTier.name);
  };

  const displayTopTiers = getDisplayOrder(topTiers);
  const displayBottomTiers = getDisplayOrder(bottomTiers);

  return (
    <>
      <Helmet>
        <title> Dashboard | Premiações Cakto </title>
      </Helmet>

      <Container maxWidth="xl">
        <Card>
          <Stack gap={2} className='p-1 sm:p-4'>
            <div className='grid sm:flex sm:justify-between text-center items-center my-2'>
              <Typography variant="h4">Premiações Cakto</Typography>
              <p className='text-sm text-center'>Leia sobre o {' '}
                <Link to="https://ajuda.cakto.com.br/pt/article/premiacoes-cakto-rnwqbv" target='_blank' className='text-[#43A697] underline'
                  style={{ color: getThemeColor('#43A697', '#0F7864') }}>programa de premiações</Link>
              </p>
            </div>
            <div className="min-h-screen sm:p-4 lg:p-8">
              <div className={`grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 items-stretch mb-6 lg:mb-8 relative gap-4 lg:gap-0 ${isDesktop ? 'xl:flex xl:justify-between' : 'lg:flex lg:justify-center'}`}>
                {displayTopTiers.map((tier, index) => (
                  <div key={tier.name} className="flex flex-col lg:flex-row items-center relative">
                    <div className="text-center flex flex-col h-full relative">
                      <div className="w-28 h-28 lg:w-40 lg:h-40 mx-auto mb-2">
                        <img src={tier.image} alt='Cackto premiação' className="w-full h-full object-contain" />
                      </div>
                      <h3
                        className="text-xs lg:text-lg font-semibold mb-1"
                        style={{ color: getThemeColor('#534A4A', '#ffffff') }}
                      >
                        {tier.name.toUpperCase()}
                      </h3>
                      <div
                        className="text-xs lg:text-lg font-semibold mb-1"
                        style={{ color: getThemeColor('#17A774', '#34F0AE') }}
                      >
                        {tier.range}
                      </div>
                      <div className="flex-1 flex items-start justify-center min-h-[96px] lg:min-h-[128px]">
                        <p
                          className="text-xs max-w-full lg:max-w-48 leading-relaxed"
                          style={{ color: getThemeColor('#637381', '#97a8b9') }}
                        >
                          {tier.description}
                        </p>
                      </div>

                      {currentLevelPosition && currentLevelPosition.isTopTier && getDisplayIndex(currentLevelPosition.adjustedIndex, topTiers) === index && (
                        <div className="absolute -top-4 lg:-top-10 left-1/2 transform -translate-x-1/2">
                          <div className="bg-[#00A067] text-white px-3 py-1.5 lg:px-4 lg:py-2 rounded-lg text-xs lg:text-sm font-bold relative w-32">
                            NÍVEL ATUAL
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-[#00A067]" />
                          </div>
                        </div>
                      )}
                    </div>

                    {index < displayTopTiers.length - 1 && (
                      <div className={`hidden lg:flex items-center ${isDesktop ? 'mx-8' : 'mx-4'}`}>
                        <div className={`${isDesktop ? 'w-24' : 'w-16'} border-t-2 border-dotted border-[#00A067]`} />
                        <ChevronRight
                          className="w-5 h-5"
                          style={{ color: getThemeColor('#17A774', '#34F0AE') }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
                
              <div className="lg:mb-8 flex justify-center items-center max-w-7xl mx-auto">
                <div className="hidden lg:block">
                  <ProgressLine className="w-full h-full" width={1100} height={100} />
                </div>
              </div>

              <div className={`grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 items-stretch relative gap-4 lg:gap-0 ${isDesktop ? 'xl:flex xl:justify-between' : 'lg:flex lg:justify-center'}`}>
                {displayBottomTiers.map((tier, index) => (
                  <div key={tier.name} className="flex flex-col lg:flex-row items-center relative">
                    <div className="text-center flex flex-col h-full relative">
                      <div className="w-28 h-28 lg:w-40 lg:h-40 mx-auto mb-2">
                        <img src={tier.image} alt='Cackto premiação' className="w-full h-full object-contain" />
                      </div>
                      <h3
                        className="text-xs lg:text-lg font-semibold mb-1"
                        style={{ color: getThemeColor('#534A4A', '#ffffff') }}
                      >
                        {tier.name.toUpperCase()}
                      </h3>
                      <div
                        className="text-xs lg:text-lg font-semibold mb-1"
                        style={{ color: getThemeColor('#17A774', '#34F0AE') }}
                      >
                        {tier.range}
                      </div>
                      <div className="flex-1 flex items-start justify-center min-h-[96px] lg:min-h-[128px]">
                        <p
                          className="text-xs max-w-full lg:max-w-48 leading-relaxed"
                          style={{ color: getThemeColor('#637381', '#97a8b9') }}
                        >
                          {tier.description}
                        </p>
                      </div>

                      {currentLevelPosition && !currentLevelPosition.isTopTier && getDisplayIndex(currentLevelPosition.adjustedIndex, bottomTiers) === index && (
                        <div className="absolute -top-4 lg:-top-12 left-1/2 transform -translate-x-1/2">
                          <div className="bg-[#00A067] text-white px-3 py-1.5 lg:px-4 lg:py-2 rounded-lg text-xs lg:text-sm font-bold relative w-32">
                            NÍVEL ATUAL
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-[#00A067]" />
                          </div>
                        </div>
                      )}
                    </div>

                    {index < displayBottomTiers.length - 1 && (
                      <div className={`hidden lg:flex items-center ${isDesktop ? 'mx-8' : 'mx-4'}`}>
                        <div className={`${isDesktop ? 'w-24' : 'w-16'} border-t-2 border-dotted border-[#00A067]`} />
                        <ChevronRightIcon
                          className="w-5 h-5"
                          style={{ color: getThemeColor('#17A774', '#34F0AE') }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Stack>
        </Card>
      </Container>
    </>
  );
}