import { Helmet } from 'react-helmet-async';
// @mui
import { <PERSON><PERSON>, <PERSON>, Tab } from '@mui/material';
// sections
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import { Container, Stack } from '@mui/system';
import { useContext, useState } from 'react';
// utils
import { useNavigate } from 'react-router';
import { useLocation, useSearchParams } from 'react-router-dom';
import MyAffiliationsProvider from '@/contexts/MyAffiliationsContext';
import ProductCoProductionsProvider from '@/contexts/ProductCoProductionContext';
import MyCoProductionsTable from '@/sections/@dashboard/coproductions/MyCoProductionsTable';
import MyAffiliationsTable from '@/sections/@dashboard/my-affiliations/MyAffiliationsTable';
import CreateProductModal from '@/sections/@dashboard/products/CreateProductModal/CreateProductModal';
import MyProductsTable from '@/sections/@dashboard/products/MyProductsTable';
import { ProductsContext } from '@/contexts/ProductsContext';
import VotingBanner from '@/components/voting-banner';

// ----------------------------------------------------------------------
export default function Products() {
  const [openCreateProductModal, setOpenCreateProductModal] = useState(false);
  const [searchParams] = useSearchParams();

  const [tabValue, setTabValue] = useState(searchParams.get('tab') || 'products');

  const { pathname } = useLocation();

  const navigate = useNavigate();

  return (
    <>
      <Helmet>
        <title> Dashboard | Produtos </title>
      </Helmet>

      <Container
        sx={{
          maxWidth: '1280px !important',
        }}
      >
        <VotingBanner />
        <CreateProductModal
          open={openCreateProductModal}
          onClose={() => setOpenCreateProductModal(false)}
        />

        <Stack width={1} direction="row" justifyContent="flex-end" mb={3}>
          <Button variant="contained" onClick={() => setOpenCreateProductModal(true)}>
            Adicionar Produto
          </Button>
        </Stack>

        <TabContext value={tabValue}>
          <Card>
            <TabList
              value={tabValue}
              onChange={(event, newTab) => {
                setTabValue(newTab);
                navigate(`${pathname}?tab=${newTab}`);
              }}
              sx={{ p: 1, px: 3 }}
            >
              <Tab value="products" label="Meus Produtos" />
              <Tab value="subscriptions" label="Minhas Assinaturas" />
              <Tab value="coproductions" label="Minhas co-produções" />
              <Tab value="affiliates" label="Minhas Afiliações" />
            </TabList>

            <TabPanel value="products" sx={{ p: 0 }}>
              <MyProductsTable initialType="unique" />
            </TabPanel>

            <TabPanel value="subscriptions" sx={{ p: 0 }}>
              <MyProductsTable initialType="subscription" />
            </TabPanel>

            <TabPanel value="coproductions" sx={{ p: 0 }}>
              <ProductCoProductionsProvider type="coproducer">
                <MyCoProductionsTable />
              </ProductCoProductionsProvider>
            </TabPanel>

            <TabPanel value="affiliates" sx={{ p: 0 }}>
              <MyAffiliationsProvider>
                <MyAffiliationsTable />
              </MyAffiliationsProvider>
            </TabPanel>
          </Card>
        </TabContext>
      </Container>
    </>
  );
}
