import { Helmet } from 'react-helmet-async';
// @mui
// sections
import { Container } from '@mui/system';
// utils
import { Box, Grid, IconButton, Toolbar, Typography } from '@mui/material';
import { useNavigate } from 'react-router';
import Iconify from '../../components/iconify';
import AffiliatesProvider from '../../contexts/AffiliatesContext';
import AffiliateInviteCard from '../../sections/@dashboard/affiliates/AffiliateInviteCard';

export default function AffiliateInvite() {
  const navigate = useNavigate();

  const redirectToDashboard = () => navigate(`/dashboard/home`);

  return (
    <>
      <Helmet>
        <title> Convite de Afiliado </title>
      </Helmet>

      <Container
        maxWidth="xl"
        sx={{
          py: {
            xs: 2,
            md: 1,
          },
        }}
      >
        <Toolbar>
          <Grid container alignItems="center" justifyContent="flex-start">
            <Grid item>
              <IconButton onClick={redirectToDashboard}>
                <Iconify icon="mdi:arrow-left" />
              </IconButton>
            </Grid>
            <Grid item>
              <Typography variant="h6">Dashboard</Typography>
            </Grid>
          </Grid>
        </Toolbar>
        <Box sx={{ py: { md: 3 } }} display="flex" justifyContent="center">
          <AffiliatesProvider>
            <AffiliateInviteCard />
          </AffiliatesProvider>
        </Box>
      </Container>
    </>
  );
}
