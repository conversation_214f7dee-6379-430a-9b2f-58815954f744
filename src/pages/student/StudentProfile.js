import { Box, Container, Grid, Tab, Tabs } from '@mui/material';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import Iconify from '../../components/iconify';
import { AccountChangePassword } from '../../sections/@dashboard/user/account';
import GeneralProfile from '../../sections/@dashboard/user/account/AccountGeneral';

export default function StudentProfile() {
  const [currentTab, setCurrentTab] = useState('general');

  const TABS = [
    {
      value: 'general',
      label: 'Geral',
      icon: <Iconify icon="ic:round-account-box" />,
      component: <GeneralProfile />,
    },
    {
      value: 'change_password',
      label: '<PERSON><PERSON><PERSON>',
      icon: <Iconify icon="ic:round-vpn-key" />,
      component: <AccountChangePassword />,
    },
  ];

  return (
    <>
      <Container maxWidth="xl">
        <Helmet>
          <title>Meu Perfil</title>
        </Helmet>
      </Container>

      <Grid container justifyContent="center" pb={5}>
        <Grid item xs={11} md={8}>
          <Tabs value={currentTab} onChange={(event, newValue) => setCurrentTab(newValue)}>
            {TABS.map((tab) => (
              <Tab key={tab.value} label={tab.label} icon={tab.icon} value={tab.value} />
            ))}
          </Tabs>

          {TABS.map(
            (tab) =>
              tab.value === currentTab && (
                <Box key={tab.value} sx={{ mt: 5 }}>
                  {tab.component}
                </Box>
              )
          )}
        </Grid>
      </Grid>
    </>
  );
}
