import { useState } from 'react';
import { Box, Typography, Tooltip, Card, IconButton, Stack, Skeleton } from '@mui/material';
import PropTypes from 'prop-types';
import { useTheme } from '@mui/material/styles';
import Iconify from '../../../components/iconify';

export const MetricCard = ({
  label,
  value,
  secondaryLabel,
  secondaryValue,
  tooltip,
  color,
  loading,
}) => {
  const theme = useTheme();
  const [showValue, setShowValue] = useState(true);

  return (
    <Card
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 2,
        borderLeft: `solid 4px ${color || theme.palette.success.main}`,
        backgroundColor: theme.palette.background.paper,
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
        borderRadius: 2,
        width: '100%',
      }}
    >
      {/* Informações à esquerda */}
      <Box sx={{ flexGrow: 1 }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography variant="subtitle2" color="text.secondary" noWrap>
                {label}
              </Typography>
              <Tooltip title={tooltip}>
                <Iconify icon="ph:question-light" sx={{ color: '#ccc', width: '18px' }} />
              </Tooltip>
            </Box>
            {loading ? (
              <Skeleton variant="text" width={100} height={36} />
            ) : (
              <Typography
                variant="h4"
                color="text.primary"
                sx={{ filter: showValue ? 'none' : 'blur(12px)' }}
              >
                {value}
              </Typography>
            )}
          </Box>

          {secondaryLabel && (
            <Box ml={4}>
              <Typography variant="subtitle2" color="text.secondary" noWrap>
                {secondaryLabel}
              </Typography>
              {loading ? (
                <Skeleton variant="text" width={100} height={36} />
              ) : (
                <Typography
                  variant="h4"
                  color="text.primary"
                  sx={{ filter: showValue ? 'none' : 'blur(12px)' }}
                >
                  {secondaryValue}
                </Typography>
              )}
            </Box>
          )}
        </Stack>
      </Box>

      <IconButton size="small" onClick={() => setShowValue(!showValue)}>
        <Iconify icon={showValue ? 'eva:eye-fill' : 'eva:eye-off-fill'} sx={{ color: '#ccc' }} />
      </IconButton>
    </Card>
  );
};

// Validação das props com PropTypes
MetricCard.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  secondaryLabel: PropTypes.string,
  secondaryValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  tooltip: PropTypes.string,
  color: PropTypes.string,
  loading: PropTypes.bool,
};

MetricCard.defaultProps = {
  secondaryLabel: '',
  secondaryValue: '',
  tooltip: '',
  loading: false,
};
