import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Paper } from "@mui/material";

const labels = [
  { id: "date", label: "Data" },
  { id: "plan", label: "Plano" },
  { id: "product", label: "Produto" },
  { id: "member", label: "Membro" },
  { id: "renewals", label: "Renovações" },
  { id: "commission", label: "Comissão" },
  { id: "renewalDate", label: "Renova em" },
  { id: "status", label: "Status" },
];

const fakeData = [
  {
    id: 1,
    date: "01/01/2024",
    plan: "Plano Básico",
    product: "Produto A",
    member: "Membro 1",
    renewals: 5,
    commission: 100.0,
    renewalDate: "01/02/2024",
    status: "active",
  },
];

export default function SubscriptionsTable() {
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {labels.map((label) => (
              <TableCell key={label.id}>{label.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {fakeData.map((row) => (
            <TableRow key={row.id}>
              <TableCell>
                <Typography variant="body2">{row.date}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.plan}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.product}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.member}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.renewals}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.commission}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{row.renewalDate}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color={row.status === "active" ? "green" : "red"}>
                  {row.status === "active" ? "Ativo" : "Inativo"}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
