import { Container, Grid, Typography, Box } from '@mui/material';
import Iconify from '@/components/iconify';
import { useForm } from 'react-hook-form';

import SubscriptionsTable from '@/sections/@dashboard/subscriptions/SubscriptionsTable';
import RHFDateRangePicker from '@/components/hook-form/RHFDateRangePicker';

import FormProvider from '@/components/hook-form/FormProvider';
import { useContext, useEffect } from 'react';
import { SubscriptionContext } from '@/contexts/SubscriptionContext';
import { fCurrency } from '@/utils/formatNumber';
import { MetricCard } from './components/MetricCard';

export default function SubscriptionPage() {
  const { subscriptions, buildCreatedAtFilter, fetchingSubscription } =
    useContext(SubscriptionContext);

  const form = useForm({
    defaultValues: {
      startDate: null,
      endDate: null,
      period: 'all_time',
    },
  });

  const { watch } = form;

  const startDate = watch('startDate');
  const endDate = watch('endDate');

  useEffect(() => {
    buildCreatedAtFilter(startDate, endDate);
  }, [startDate, endDate, buildCreatedAtFilter]);

  return (
    <FormProvider methods={form}>
      <Container maxWidth="lg" sx={{ minHeight: '100vh', padding: 4 }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          sx={{ marginBottom: 4 }}
        >
          <Typography variant="h4" color="white" gutterBottom>
            Assinaturas
          </Typography>
          <Box>
            <RHFDateRangePicker label="Período" initialPeriod="all_time" />
          </Box>
        </Box>

        <Grid container spacing={2} sx={{ marginBottom: 4 }} alignItems="center">
          <Grid item xs={12} md={6}>
            <MetricCard
              loading={fetchingSubscription}
              label="Ativas"
              value={subscriptions.active_count}
              secondaryLabel="Comissão"
              secondaryValue={fCurrency(subscriptions?.active_amount ?? 0)}
              icon={<Iconify icon="eva:eye-fill" />}
              tooltip="Número de assinaturas ativas e comissão total"
              color="#28a745"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <MetricCard
              loading={fetchingSubscription}
              label="Renovações"
              value={subscriptions.renewed_count}
              secondaryLabel="Comissão"
              secondaryValue={fCurrency(subscriptions?.renewed_amount ?? 0)}
              icon={<Iconify icon="eva:eye-fill" />}
              tooltip="Número de renovações e comissão das renovações"
              color="#28a745"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <MetricCard
              loading={fetchingSubscription}
              label="LTV"
              value={fCurrency(subscriptions?.ltv ?? 0)}
              icon={<Iconify icon="eva:eye-fill" />}
              tooltip={
                <div className="flex flex-col gap-3">
                  <p>
                    LTV (Lifetime Value) é a receita total esperada de um cliente durante seu
                    relacionamento com a empresa.
                  </p>
                  <div>
                    <p>É calculado assim:</p>
                    <p>
                      LTV = Valor Médio do Pedido × Número de Pedidos por Cliente × Tempo de
                      Retenção.
                    </p>
                  </div>
                </div>
              }
              color="#28a745"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <MetricCard
              loading={fetchingSubscription}
              label="MRR"
              value={fCurrency(subscriptions?.mrr ?? 0)}
              icon={<Iconify icon="eva:eye-fill" />}
              tooltip={
                <div className="flex flex-col gap-3">
                  <p>
                    MRR (Monthly Recurring Revenue) é a receita mensal recorrente de um negócio.
                  </p>
                  <div>
                    <p>É calculado assim:</p>
                    <p>
                      Calcula-se somando a receita de todas as assinaturas ativas no mês. MRR =
                      Número de Assinaturas Ativas × Valor Médio da Assinatura Mensal.
                    </p>
                  </div>
                </div>
              }
              color="#FDD465"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <MetricCard
              loading={fetchingSubscription}
              label="Churn Rate"
              value={`${subscriptions.churn_rate}%`}
              icon={<Iconify icon="eva:eye-fill" />}
              tooltip={
                <div className="flex flex-col gap-3">
                  <p>
                    Churn Rate é a taxa de cancelamento ou perda de clientes em um período. Uma taxa
                    alta indica a necessidade de melhorar retenção.
                  </p>
                  <div>
                    <p>É calculado assim:</p>
                    <p>
                      Churn Rate = (Número de Clientes Perdidos no Período / Número Total de
                      Clientes no Início do Período) × 100%.
                    </p>
                  </div>
                </div>
              }
              color="#FF5630"
            />
          </Grid>
        </Grid>

        <SubscriptionsTable />
      </Container>
    </FormProvider>
  );
}
