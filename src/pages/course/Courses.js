import { useAuthContext } from '@/auth/useAuthContext';
import VotingBanner from '@/components/voting-banner';
import { CarouselBanner } from '@/sections/@dashboard/home';
import CreatePasswordModal from '@/sections/auth/CreatePasswordModal';
import { CoursesList } from '@/sections/course';
import { fetchBannerService } from '@/services/banner';
import { Container, Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';

// ----------------------------------------------------------------------

export default function Courses() {
  const { user } = useAuthContext();
  const [openNewPasswordFormModal, setOpenNewPasswordFormModal] = useState(false);

  const { data: banners = [], isFetching: isBannerFetching } = useQuery({
    queryKey: ['banners-course'],
    async queryFn() {
      return fetchBannerService({ location: 'courses' });
    },
  });

  useEffect(() => {
    if (!user.has_usable_password) {
      setOpenNewPasswordFormModal(true);
    }
  }, [user]);


  return (
    <>
      <Container maxWidth="xl">
        <Helmet>
          <title>Cursos</title>
        </Helmet>
        <CarouselBanner banners={banners} fetching={isBannerFetching} />
        <VotingBanner />

        <Typography variant="h4">Meus Cursos</Typography>
        <CoursesList />
      </Container>
      {openNewPasswordFormModal && (
        <CreatePasswordModal
          open={openNewPasswordFormModal}
          handleClose={setOpenNewPasswordFormModal}
        />
      )}
    </>
  );
}
