import PropTypes from 'prop-types';
import { Navigate } from 'react-router-dom';
import { useAuthContext } from './useAuthContext';

export default function ExperimentalFeatureGuard({ children, feature }) {
  const { user } = useAuthContext();

  if (!user?.experimental_features?.includes(feature)) {
    return <Navigate to="/403" replace />;
  }

  return children;
}

ExperimentalFeatureGuard.propTypes = {
  children: PropTypes.node,
  feature: PropTypes.string,
};
