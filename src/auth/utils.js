import moment from 'moment-timezone';
import Cookies from 'js-cookie';
// routes
import { InfoRegistration } from '../pages/components/inform-pending-registration';
import { PATH_AUTH } from '../routes/paths';
// utils
import axios from '../utils/axios';

// ----------------------------------------------------------------------

export function jwtDecode(token) {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
}

// ----------------------------------------------------------------------

const getCurrentTimeInBrasilia = async () => {
  try {
    const dateTime = moment().tz('America/Sao_Paulo');
    return moment(dateTime).unix();
  } catch (error) {
    return new Date() / 1000;
  }
};

export const isValidToken = (accessToken) => {
  if (!accessToken) {
    return false;
  }

  const decoded = jwtDecode(accessToken);

  return getCurrentTimeInBrasilia().then(
    (currentTimeInBrasilia) => decoded.exp > currentTimeInBrasilia
  );
};

// ----------------------------------------------------------------------

export const tokenExpired = (exp) => {
  // eslint-disable-next-line prefer-const
  let expiredTimer;

  const currentTime = Date.now();

  // Test token expires after 10s
  // const timeLeft = currentTime + 10000 - currentTime; // ~10s
  const timeLeft = exp * 1000 - currentTime;

  clearTimeout(expiredTimer);

  expiredTimer = setTimeout(() => {
    alert('Token expired');

    localStorage.removeItem('accessToken');

    window.location.href = PATH_AUTH.login;
  }, timeLeft);
};

export const setSession = ({ accessToken, refreshToken }) => {
  if (accessToken) {
    localStorage.setItem('accessToken', accessToken);
  }

  if (refreshToken != null) {
    localStorage.setItem('refreshToken', refreshToken);
  } else {
    localStorage.removeItem('refreshToken');
  }
  axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
};

export const clearSession = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('hasTwoFactorAuthenticated');
  localStorage.removeItem('emailForResendCode');
  localStorage.removeItem('cakto-methods-mfa');
  localStorage.removeItem(InfoRegistration.STATUS);
  delete axios.defaults.headers.common.Authorization;
};

export const clearCookies = () => {
  const allCookies = Cookies.get();

  Object.keys(allCookies).forEach((cookieName) => {
    Cookies.remove(cookieName);
  });
};
